{"name": "clinique", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed:hr": "tsx scripts/seed-hr.ts", "seed:leaves": "tsx scripts/seed-leaves.ts", "seed:attendance": "tsx scripts/seed-attendance.ts", "seed:more-leaves": "tsx scripts/seed-more-leaves.ts", "seed:hospitalization": "tsx scripts/seed-hospitalization.ts", "seed:doctors": "tsx scripts/seed-doctors.ts", "seed:more-patients": "tsx scripts/seed-more-patients.ts", "seed:permissions": "tsx scripts/seed-permissions.ts", "check:permissions": "tsx scripts/check-user-permissions.ts", "promote:admin": "tsx scripts/promote-admin.ts", "check:users": "tsx scripts/check-users.ts", "create:test-user": "tsx scripts/check-users.ts create"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}