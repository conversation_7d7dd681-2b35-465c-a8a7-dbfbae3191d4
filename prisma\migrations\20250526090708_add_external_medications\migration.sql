-- DropForeignKey
ALTER TABLE "prescription_items" DROP CONSTRAINT "prescription_items_medicationId_fkey";

-- AlterTable
ALTER TABLE "prescription_items" ADD COLUMN     "estimatedPrice" DOUBLE PRECISION,
ADD COLUMN     "externalMedicationCategory" TEXT,
ADD COLUMN     "externalMedicationForm" TEXT,
ADD COLUMN     "externalMedicationName" TEXT,
ADD COLUMN     "externalMedicationStrength" TEXT,
ADD COLUMN     "isExternal" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN "medicationId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "prescription_items" ADD CONSTRAINT "prescription_items_medicationId_fkey" FOREIGN KEY ("medicationId") REFERENCES "medications"("id") ON DELETE SET NULL ON UPDATE CASCADE;
