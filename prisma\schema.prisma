// GlobalCare Solutions - Schéma Prisma
// Plateforme SaaS pour la gestion hospitalière
// Module: Gestion des Patients

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENUMS - Types de données standardisés
// ========================================

enum UserRole {
  SUPER_ADMIN     // Administrateur système
  ADMIN           // Administrateur de l'hôpital
  DOCTOR          // Médecin
  NURSE           // Infirmier(ère)
  PHARMACIST      // Pharmacien
  RECEPTIONIST    // Réceptionniste
  TECHNICIAN      // Technicien
  ACCOUNTANT      // Comptable
  LAB_TECHNICIAN  // Technicien de laboratoire
  RADIOLOGIST     // Radiologue
  ANESTHESIOLOGIST // Anesthésiste
}

enum PermissionAction {
  CREATE
  READ
  UPDATE
  DELETE
  APPROVE
  REJECT
  EXPORT
  PRINT
  MANAGE
}

enum PermissionResource {
  PATIENTS
  CONSULTATIONS
  PRESCRIPTIONS
  MEDICATIONS
  PHARMACY
  LABORATORY
  HOSPITALIZATION
  EMPLOYEES
  DEPARTMENTS
  PAYMENTS
  REPORTS
  ANALYTICS
  SETTINGS
  USERS
  ROLES
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum BloodType {
  A_POSITIVE
  A_NEGATIVE
  B_POSITIVE
  B_NEGATIVE
  AB_POSITIVE
  AB_NEGATIVE
  O_POSITIVE
  O_NEGATIVE
  UNKNOWN
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
  OTHER
}

enum ConsultationStatus {
  PENDING_PAYMENT  // En attente de paiement
  SCHEDULED        // Programmé (après paiement)
  IN_PROGRESS      // En cours
  COMPLETED        // Terminé
  CANCELLED        // Annulé
  NO_SHOW          // Absent
}

enum ConsultationType {
  GENERAL
  EMERGENCY
  FOLLOW_UP
  SPECIALIST
  TELEMEDICINE
}

enum PrescriptionStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  EXPIRED
}

enum MedicationForm {
  TABLET
  CAPSULE
  SYRUP
  INJECTION
  CREAM
  OINTMENT
  DROPS
  SPRAY
  INHALER
  PATCH
  OTHER
}

enum MedicationCategory {
  ANTIBIOTIC
  ANALGESIC
  ANTI_INFLAMMATORY
  ANTIHYPERTENSIVE
  ANTIDIABETIC
  ANTIHISTAMINE
  ANTACID
  VITAMIN
  SUPPLEMENT
  VACCINE
  OTHER
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  CANCELLED
  REFUNDED
}

enum LabOrderStatus {
  PENDING_PAYMENT  // En attente de paiement
  PAID            // Payé, en attente de prélèvement
  SAMPLE_TAKEN    // Échantillon prélevé
  IN_PROGRESS     // Analyse en cours
  COMPLETED       // Terminé
  CANCELLED       // Annulé
  EXTERNAL        // Prescription externe (patient fait l'analyse ailleurs)
}

enum LabUrgency {
  NORMAL
  URGENT
  EMERGENCY
}

enum ContractType {
  CDI           // Contrat à durée indéterminée
  CDD           // Contrat à durée déterminée
  STAGE         // Stage
  FREELANCE     // Freelance/Consultant
  PART_TIME     // Temps partiel
}

enum EmployeeStatus {
  ACTIVE        // Actif
  INACTIVE      // Inactif
  ON_LEAVE      // En congé
  SUSPENDED     // Suspendu
  TERMINATED    // Licencié
}

enum AttendanceStatus {
  PRESENT       // Présent
  ABSENT        // Absent
  LATE          // En retard
  SICK_LEAVE    // Congé maladie
  VACATION      // Congé
  HALF_DAY      // Demi-journée
}

enum LeaveType {
  ANNUAL        // Congé annuel
  SICK          // Congé maladie
  MATERNITY     // Congé maternité
  PATERNITY     // Congé paternité
  EMERGENCY     // Congé d'urgence
  UNPAID        // Congé sans solde
  STUDY         // Congé d'études
}

enum LeaveStatus {
  PENDING       // En attente
  APPROVED      // Approuvé
  REJECTED      // Refusé
  CANCELLED     // Annulé
}

// ========================================
// MODÈLES - Entités principales (SaaS Multi-Tenant)
// ========================================

// Organisations (Hôpitaux/Cliniques) - Tenants
model Organization {
  id              String   @id @default(cuid())
  name            String
  slug            String   @unique // URL-friendly identifier
  type            String   // hospital, clinic, medical_center

  // Informations de contact
  email           String?
  phone           String?
  address         String?
  city            String?
  country         String   @default("Mali")
  website         String?

  // Configuration
  logo            String?
  timezone        String   @default("Africa/Bamako")
  currency        String   @default("XOF") // Franc CFA
  language        String   @default("fr")

  // Abonnement SaaS
  subscriptionPlan String  @default("basic") // basic, premium, enterprise
  subscriptionStatus String @default("active") // active, suspended, cancelled
  trialEndsAt     DateTime?
  subscriptionEndsAt DateTime?

  // Limites du plan
  maxUsers        Int      @default(10)
  maxPatients     Int      @default(1000)
  maxStorage      Int      @default(5) // GB

  // Métadonnées
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  users           User[]
  patients        Patient[]
  consultations   Consultation[]
  prescriptions   Prescription[]
  medications     Medication[]
  medicalHistory  MedicalHistory[]
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensings     Dispensing[]
  // Relations facturation
  consultationPrices ConsultationPrice[]
  payments        Payment[]
  // Relations laboratoire
  labTestTypes    LabTestType[]
  labOrders       LabOrder[]
  labResults      LabResult[]
  labPayments     LabPayment[]
  // Relations personnel/RH
  departments     Department[]
  positions       Position[]
  employees       Employee[]
  attendances     Attendance[]
  leaveRequests   LeaveRequest[]
  // Relations hospitalisations
  rooms           Room[]
  beds            Bed[]
  admissions      Admission[]
  // Relations rapports
  reports         Report[]
  // Relations permissions
  auditLogs       AuditLog[]

  @@map("organizations")
}

// Utilisateurs du système (Personnel médical uniquement)
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  password    String
  firstName   String
  lastName    String
  phone       String?
  role        UserRole
  isActive    Boolean  @default(true)
  avatar      String?

  // Multi-tenant
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  // Métadonnées
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLogin   DateTime?

  // Relations
  consultations     Consultation[]
  prescriptions     Prescription[]
  createdPatients   Patient[]      @relation("CreatedBy")
  updatedPatients   Patient[]      @relation("UpdatedBy")
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensings       Dispensing[]   @relation("Pharmacist")
  // Relations facturation
  createdPayments   Payment[]      @relation("PaymentCreatedBy")
  // Relations laboratoire
  labOrders         LabOrder[]
  // Relations personnel/RH
  employee          Employee?
  // Relations hospitalisations
  doctorAdmissions  Admission[] @relation("DoctorAdmissions")
  // Relations rapports
  generatedReports  Report[]
  // Relations permissions
  userPermissions   UserPermission[]
  userSessions      UserSession[]
  auditLogs         AuditLog[]

  @@map("users")
}

// Patients (pas de compte utilisateur)
model Patient {
  id                String        @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization  @relation(fields: [organizationId], references: [id])

  // Informations personnelles
  firstName         String
  lastName          String
  dateOfBirth       DateTime
  gender            Gender
  phone             String?
  email             String?
  address           String?
  city              String?
  country           String        @default("Mali")

  // Informations médicales
  bloodType         BloodType     @default(UNKNOWN)
  allergies         String?       // JSON string des allergies
  chronicDiseases   String?       // JSON string des maladies chroniques
  emergencyContact  String?       // JSON string du contact d'urgence

  // Informations sociales
  maritalStatus     MaritalStatus @default(SINGLE)
  occupation        String?
  insurance         String?       // Informations d'assurance

  // Métadonnées
  patientNumber     String        // Numéro unique du patient (par organisation)
  isActive          Boolean       @default(true)
  notes             String?       // Notes générales
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  createdBy         User          @relation("CreatedBy", fields: [createdById], references: [id])
  createdById       String
  updatedBy         User?         @relation("UpdatedBy", fields: [updatedById], references: [id])
  updatedById       String?

  consultations     Consultation[]
  prescriptions     Prescription[]
  medicalHistory    MedicalHistory[]
  // Relations facturation
  payments          Payment[]
  // Relations laboratoire
  labOrders         LabOrder[]
  // Relations hospitalisations
  admissions        Admission[]

  // Index composé pour numéro patient unique par organisation
  @@unique([organizationId, patientNumber])
  @@map("patients")
}

// Consultations médicales
model Consultation {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  consultationDate  DateTime
  status            ConsultationStatus  @default(SCHEDULED)
  type              ConsultationType    @default(GENERAL)

  // Contenu médical
  chiefComplaint    String?             // Motif de consultation
  symptoms          String?             // Symptômes (JSON)
  diagnosis         String?             // Diagnostic
  treatment         String?             // Traitement prescrit
  notes             String?             // Notes du médecin

  // Données vitales
  weight            Float?              // Poids en kg
  height            Float?              // Taille en cm
  bloodPressure     String?             // Tension artérielle
  temperature       Float?              // Température en °C
  heartRate         Int?                // Fréquence cardiaque

  // Coûts
  consultationFee   Float?              // Frais de consultation
  paymentStatus     PaymentStatus       @default(PENDING)

  // Métadonnées
  duration          Int?                // Durée en minutes
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  patient           Patient             @relation(fields: [patientId], references: [id])
  patientId         String
  doctor            User                @relation(fields: [doctorId], references: [id])
  doctorId          String

  prescriptions     Prescription[]
  // Relations facturation
  payments          Payment[]
  // Relations laboratoire
  labOrders         LabOrder[]

  @@map("consultations")
}

// Médicaments disponibles
model Medication {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  name              String              // Nom commercial
  genericName       String?             // Nom générique
  brand             String?             // Marque
  category          MedicationCategory  @default(OTHER)
  form              MedicationForm      @default(TABLET)

  // Détails médicaux
  strength          String?             // Dosage (ex: 500mg, 10ml)
  activeIngredient  String?             // Principe actif
  description       String?             // Description
  sideEffects       String?             // Effets secondaires (JSON)
  contraindications String?             // Contre-indications (JSON)

  // Informations commerciales
  manufacturer      String?             // Fabricant
  barcode           String?             // Code-barres
  price             Float?              // Prix unitaire
  currency          String              @default("XOF")

  // Stock et disponibilité
  stockQuantity     Int                 @default(0)
  minStockLevel     Int                 @default(10)
  maxStockLevel     Int                 @default(1000)
  expiryDate        DateTime?           // Date d'expiration du stock
  batchNumber       String?             // Numéro de lot

  // Métadonnées
  isActive          Boolean             @default(true)
  requiresPrescription Boolean          @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  prescriptionItems PrescriptionItem[]
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensingItems   DispensingItem[]

  @@unique([organizationId, name, strength])
  @@map("medications")
}

// Prescriptions médicales
model Prescription {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  prescriptionDate  DateTime            @default(now())
  status            PrescriptionStatus  @default(ACTIVE)
  prescriptionNumber String             // Numéro unique de prescription

  // Instructions générales
  generalInstructions String?           // Instructions générales
  notes             String?             // Notes du médecin

  // Métadonnées
  issuedAt          DateTime            @default(now())
  expiresAt         DateTime?           // Date d'expiration
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  patient           Patient             @relation(fields: [patientId], references: [id])
  patientId         String
  doctor            User                @relation(fields: [doctorId], references: [id])
  doctorId          String
  consultation      Consultation?       @relation(fields: [consultationId], references: [id])
  consultationId    String?

  // Items de prescription
  items             PrescriptionItem[]
  // Relations pharmacie
  dispensings       Dispensing[]

  @@unique([organizationId, prescriptionNumber])
  @@map("prescriptions")
}

// Items individuels d'une prescription
model PrescriptionItem {
  id                String        @id @default(cuid())

  // Relations
  prescription      Prescription  @relation(fields: [prescriptionId], references: [id], onDelete: Cascade)
  prescriptionId    String
  medication        Medication?   @relation(fields: [medicationId], references: [id])
  medicationId      String?       // Optionnel pour médicaments externes

  // Support des médicaments externes
  isExternal        Boolean       @default(false)
  externalMedicationName     String?  // Nom du médicament externe
  externalMedicationForm     String?  // Forme (comprimé, sirop, etc.)
  externalMedicationStrength String?  // Dosage (500mg, etc.)
  externalMedicationCategory String?  // Catégorie
  estimatedPrice    Float?        // Prix estimé en FCFA

  // Posologie détaillée
  dosage            String        // Ex: "1 comprimé"
  frequency         String        // Ex: "3 fois par jour"
  duration          String        // Ex: "7 jours"
  timing            String?       // Ex: "avant les repas"
  route             String?       // Voie d'administration

  // Instructions spécifiques
  instructions      String?       // Instructions spéciales pour ce médicament
  quantity          Int           // Quantité totale prescrite

  // Dispensation
  dispensedQuantity Int           @default(0)  // Quantité déjà dispensée
  isDispensed       Boolean       @default(false)  // Complètement dispensé

  // Métadonnées
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations avec dispensation
  dispensingItems   DispensingItem[]

  @@map("prescription_items")
}

// Historique médical
model MedicalHistory {
  id                String    @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  date              DateTime  @default(now())
  type              String    // Type d'événement médical
  description       String    // Description de l'événement

  // Détails
  severity          String?   // Gravité (mild, moderate, severe)
  outcome           String?   // Résultat
  documents         String?   // JSON des documents associés

  // Métadonnées
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  patient           Patient   @relation(fields: [patientId], references: [id])
  patientId         String

  @@map("medical_history")
}

// ===== GESTION PHARMACIE =====

// Mouvements de stock de la pharmacie
model PharmacyMovement {
  id              String        @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization  @relation(fields: [organizationId], references: [id])

  // Médicament concerné
  medicationId    String
  medication      Medication    @relation(fields: [medicationId], references: [id])

  // Type de mouvement
  type            MovementType  // IN (entrée), OUT (sortie), ADJUSTMENT (ajustement)
  quantity        Int           // Quantité (positive pour entrée, négative pour sortie)
  previousStock   Int           // Stock avant le mouvement
  newStock        Int           // Stock après le mouvement

  // Détails du mouvement
  reason          String?       // Raison du mouvement
  reference       String?       // Référence (bon de livraison, etc.)
  unitPrice       Float?        // Prix unitaire lors du mouvement
  totalValue      Float?        // Valeur totale du mouvement

  // Expiration (pour les entrées)
  expiryDate      DateTime?     // Date d'expiration du lot
  batchNumber     String?       // Numéro de lot

  // Relations
  dispensingId    String?       // Si mouvement lié à une dispensation
  dispensing      Dispensing?   @relation(fields: [dispensingId], references: [id])

  // Utilisateur responsable
  userId          String
  user            User          @relation(fields: [userId], references: [id])

  // Métadonnées
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("pharmacy_movements")
}

// Types de mouvements de stock
enum MovementType {
  IN          // Entrée de stock (livraison, retour)
  OUT         // Sortie de stock (dispensation, péremption)
  ADJUSTMENT  // Ajustement d'inventaire
}

// Dispensations de médicaments
model Dispensing {
  id                String            @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization      @relation(fields: [organizationId], references: [id])

  // Numéro unique de dispensation
  dispensingNumber  String            @unique

  // Prescription liée
  prescriptionId    String
  prescription      Prescription      @relation(fields: [prescriptionId], references: [id])

  // Pharmacien responsable
  pharmacistId      String
  pharmacist        User              @relation("Pharmacist", fields: [pharmacistId], references: [id])

  // Statut de la dispensation
  status            DispensingStatus  @default(PENDING)

  // Montants
  totalAmount       Float             @default(0)    // Montant total
  paidAmount        Float             @default(0)    // Montant payé
  discountAmount    Float             @default(0)    // Remise accordée

  // Paiement
  paymentMethod     PaymentMethod?    // Mode de paiement
  paymentReference  String?           // Référence de paiement

  // Notes
  notes             String?           // Notes du pharmacien

  // Métadonnées
  dispensedAt       DateTime          @default(now())
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  items             DispensingItem[]
  movements         PharmacyMovement[]

  @@unique([organizationId, dispensingNumber])
  @@map("dispensings")
}

// Statuts de dispensation
enum DispensingStatus {
  PENDING     // En attente
  PARTIAL     // Partiellement dispensé
  COMPLETED   // Complètement dispensé
  CANCELLED   // Annulé
}

// Modes de paiement
enum PaymentMethod {
  CASH
  CARD
  MOBILE
  INSURANCE   // Assurance
  CREDIT      // Crédit
  BANK_TRANSFER // Virement bancaire
  CHECK       // Chèque
}

enum InvoiceStatus {
  DRAFT
  PENDING
  PAID
  PARTIALLY_PAID
  OVERDUE
  CANCELLED
}


enum ServiceType {
  CONSULTATION
  PHARMACY
  LABORATORY
  IMAGING
  SURGERY
  HOSPITALIZATION
  EMERGENCY
  VACCINATION
  CERTIFICATE
  TRANSPORT
  OTHER
}

// Items individuels d'une dispensation
model DispensingItem {
  id                  String            @id @default(cuid())

  // Relations
  dispensingId        String
  dispensing          Dispensing        @relation(fields: [dispensingId], references: [id], onDelete: Cascade)

  prescriptionItemId  String
  prescriptionItem    PrescriptionItem  @relation(fields: [prescriptionItemId], references: [id])

  medicationId        String
  medication          Medication        @relation(fields: [medicationId], references: [id])

  // Quantités
  prescribedQuantity  Int               // Quantité prescrite
  dispensedQuantity   Int               // Quantité réellement dispensée

  // Prix
  unitPrice           Float             // Prix unitaire au moment de la dispensation
  totalPrice          Float             // Prix total pour cet item

  // Lot et expiration
  batchNumber         String?           // Numéro de lot dispensé
  expiryDate          DateTime?         // Date d'expiration du lot

  // Métadonnées
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt

  @@map("dispensing_items")
}

// ===== GESTION FACTURATION & PAIEMENTS =====

// Grille tarifaire des consultations
model ConsultationPrice {
  id              String           @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization     @relation(fields: [organizationId], references: [id])

  // Type de consultation
  consultationType ConsultationType

  // Tarification
  basePrice       Float            // Prix de base
  emergencyPrice  Float?           // Prix d'urgence (majoration)
  insurancePrice  Float?           // Prix pour les assurés

  // Métadonnées
  isActive        Boolean          @default(true)
  description     String?          // Description du tarif
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  @@unique([organizationId, consultationType])
  @@map("consultation_prices")
}

// Paiements
model Payment {
  id                String        @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization  @relation(fields: [organizationId], references: [id])

  // Numérotation
  paymentNumber     String        // Numéro de paiement/reçu
  paymentDate       DateTime      @default(now())

  // Patient
  patientId         String
  patient           Patient       @relation(fields: [patientId], references: [id])

  // Consultation liée
  consultationId    String?
  consultation      Consultation? @relation(fields: [consultationId], references: [id])

  // Montants
  amount            Float         // Montant payé
  currency          String        @default("XOF")

  // Mode de paiement
  paymentMethod     PaymentMethod
  paymentReference  String?       // Référence de transaction (mobile money, etc.)

  // Statut
  status            PaymentStatus @default(PAID)

  // Informations additionnelles
  description       String?       // Description du paiement
  notes             String?       // Notes

  // Métadonnées
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  createdById       String
  createdBy         User          @relation("PaymentCreatedBy", fields: [createdById], references: [id])

  @@unique([organizationId, paymentNumber])
  @@map("payments")
}

// ===== MODULE LABORATOIRE =====

// Types d'examens de laboratoire
model LabTestType {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  name            String    // Ex: "Hémogramme complet", "Glycémie"
  code            String    // Ex: "HEM001", "GLY001"
  category        String    // Ex: "Hématologie", "Biochimie", "Parasitologie"

  // Tarification
  price           Float     // Prix de l'examen en FCFA
  currency        String    @default("XOF")

  // Détails médicaux
  description     String?   // Description de l'examen
  normalValues    String?   // Valeurs normales de référence (JSON)
  sampleType      String?   // Type d'échantillon (sang, urine, selles, etc.)
  preparation     String?   // Préparation nécessaire (à jeun, etc.)

  // Métadonnées
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  labOrders       LabOrder[]

  @@unique([organizationId, code])
  @@map("lab_test_types")
}

// Prescriptions d'examens de laboratoire
model LabOrder {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Numérotation
  orderNumber     String    // LAB-YYYYMMDD-XXXX

  // Relations principales
  patientId       String
  patient         Patient   @relation(fields: [patientId], references: [id])
  doctorId        String
  doctor          User      @relation(fields: [doctorId], references: [id])
  consultationId  String?
  consultation    Consultation? @relation(fields: [consultationId], references: [id])
  testTypeId      String
  testType        LabTestType @relation(fields: [testTypeId], references: [id])

  // Statut et urgence
  status          LabOrderStatus @default(PENDING_PAYMENT)
  urgency         LabUrgency @default(NORMAL)

  // Informations cliniques
  clinicalInfo    String?   // Informations cliniques du médecin
  instructions    String?   // Instructions spéciales

  // Dates importantes
  orderDate       DateTime  @default(now())
  sampleDate      DateTime? // Date de prélèvement
  resultDate      DateTime? // Date des résultats

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  result          LabResult?
  payment         LabPayment?

  @@unique([organizationId, orderNumber])
  @@map("lab_orders")
}

// Résultats d'examens de laboratoire
model LabResult {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Relation avec la prescription
  labOrderId      String    @unique
  labOrder        LabOrder  @relation(fields: [labOrderId], references: [id])

  // Résultats
  values          Json      // Résultats structurés (ex: {"hemoglobine": 12.5, "globules_blancs": 8000})
  interpretation  String?   // Interprétation du biologiste
  conclusion      String?   // Conclusion

  // Personnel
  technician      String?   // Technicien ayant effectué l'analyse
  validatedBy     String?   // Biologiste validateur
  validatedAt     DateTime? // Date de validation

  // Informations additionnelles
  comments        String?   // Commentaires
  attachments     String?   // URLs des fichiers joints (JSON)

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("lab_results")
}

// Paiements pour examens de laboratoire
model LabPayment {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Numérotation
  paymentNumber   String    // LABPAY-YYYYMMDD-XXXX

  // Relation avec la prescription
  labOrderId      String    @unique
  labOrder        LabOrder  @relation(fields: [labOrderId], references: [id])

  // Montant et paiement
  amount          Float
  currency        String    @default("XOF")
  paymentMethod   PaymentMethod
  paymentDate     DateTime  @default(now())

  // Informations additionnelles
  reference       String?   // Référence de transaction
  notes           String?   // Notes

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@unique([organizationId, paymentNumber])
  @@map("lab_payments")
}

// ===== MODULE PERSONNEL/RH =====

// Départements de l'hôpital
model Department {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  name            String    // Ex: "Médecine générale", "Laboratoire", "Administration"
  code            String    // Ex: "MED", "LAB", "ADM"
  description     String?   // Description du département

  // Responsable du département
  managerId       String?
  manager         Employee? @relation("DepartmentManager", fields: [managerId], references: [id])

  // Métadonnées
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  employees       Employee[] @relation("EmployeeDepartment")
  positions       Position[]

  @@unique([organizationId, code])
  @@map("departments")
}

// Postes/Fonctions dans l'hôpital
model Position {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  title           String    // Ex: "Médecin généraliste", "Infirmier", "Technicien labo"
  code            String    // Ex: "MED_GEN", "INF", "TECH_LAB"
  description     String?   // Description du poste

  // Département
  departmentId    String
  department      Department @relation(fields: [departmentId], references: [id])

  // Salaire de base
  baseSalary      Float?    // Salaire de base en FCFA
  currency        String    @default("XOF")

  // Permissions et accès
  permissions     Json?     // Permissions spécifiques au poste (JSON)

  // Métadonnées
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  employees       Employee[]

  @@unique([organizationId, code])
  @@map("positions")
}

// Employés de l'hôpital
model Employee {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Numérotation
  employeeNumber  String    // EMP-YYYY-XXXX

  // Informations personnelles
  firstName       String
  lastName        String
  email           String?
  phone           String?
  dateOfBirth     DateTime?
  gender          Gender?
  address         String?

  // Informations professionnelles
  departmentId    String
  department      Department @relation("EmployeeDepartment", fields: [departmentId], references: [id])
  positionId      String
  position        Position  @relation(fields: [positionId], references: [id])

  // Dates importantes
  hireDate        DateTime  @default(now())
  contractEndDate DateTime?

  // Salaire et contrat
  currentSalary   Float?    // Salaire actuel en FCFA
  contractType    ContractType @default(CDI)
  workingHours    Float     @default(40) // Heures par semaine

  // Statut
  status          EmployeeStatus @default(ACTIVE)

  // Lien avec le compte utilisateur (optionnel)
  userId          String?   @unique
  user            User?     @relation(fields: [userId], references: [id])

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  managedDepartments Department[] @relation("DepartmentManager")
  attendances     Attendance[]
  leaveRequests   LeaveRequest[]

  @@unique([organizationId, employeeNumber])
  @@map("employees")
}

// Présences/Pointages
model Attendance {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Employé
  employeeId      String
  employee        Employee  @relation(fields: [employeeId], references: [id])

  // Date et heures
  date            DateTime  // Date de travail
  checkIn         DateTime? // Heure d'arrivée
  checkOut        DateTime? // Heure de départ
  breakStart      DateTime? // Début de pause
  breakEnd        DateTime? // Fin de pause

  // Heures calculées
  hoursWorked     Float?    // Heures travaillées (calculé automatiquement)
  overtimeHours   Float?    // Heures supplémentaires

  // Statut et notes
  status          AttendanceStatus @default(PRESENT)
  notes           String?   // Notes ou justifications

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@unique([employeeId, date])
  @@map("attendances")
}

// Demandes de congés
model LeaveRequest {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Numérotation
  requestNumber   String    // LEAVE-YYYY-XXXX

  // Employé
  employeeId      String
  employee        Employee  @relation(fields: [employeeId], references: [id])

  // Type et dates
  leaveType       LeaveType
  startDate       DateTime
  endDate         DateTime
  totalDays       Int       // Nombre de jours demandés

  // Justification
  reason          String    // Raison de la demande
  attachments     String?   // URLs des pièces jointes (JSON)

  // Approbation
  status          LeaveStatus @default(PENDING)
  approvedBy      String?   // ID de l'approbateur
  approvedAt      DateTime?
  rejectionReason String?   // Raison du refus

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@unique([organizationId, requestNumber])
  @@map("leave_requests")
}

// ===== MODULE HOSPITALISATIONS & LITS =====

// Enums pour les hospitalisations
enum RoomType {
  STANDARD      // Chambre standard
  VIP           // Chambre VIP
  ICU           // Soins intensifs
  EMERGENCY     // Urgences
  SURGERY       // Bloc opératoire
  MATERNITY     // Maternité
  PEDIATRIC     // Pédiatrie
  ISOLATION     // Isolement
}

enum BedType {
  STANDARD      // Lit standard
  ELECTRIC      // Lit électrique
  ICU           // Lit de soins intensifs
  PEDIATRIC     // Lit pédiatrique
  MATERNITY     // Lit de maternité
}

enum BedStatus {
  AVAILABLE     // Disponible
  OCCUPIED      // Occupé
  MAINTENANCE   // En maintenance
  RESERVED      // Réservé
  OUT_OF_ORDER  // Hors service
}

enum AdmissionStatus {
  ADMITTED      // Hospitalisé
  DISCHARGED    // Sorti
  TRANSFERRED   // Transféré
  DECEASED      // Décédé
  ESCAPED       // Parti sans autorisation
}

// Chambres d'hôpital
model Room {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  number          String    // Numéro de chambre (ex: "101", "A-205")
  name            String    // Nom de la chambre (ex: "Chambre VIP 1")
  floor           Int       // Étage
  roomType        RoomType  // Type de chambre
  capacity        Int       @default(1) // Nombre de lits maximum

  // Caractéristiques
  hasPrivateBathroom Boolean @default(false)
  hasAirConditioning Boolean @default(false)
  hasTV           Boolean   @default(false)
  hasWifi         Boolean   @default(true)
  description     String?   // Description additionnelle

  // Tarification
  dailyRate       Float?    // Tarif journalier en FCFA
  currency        String    @default("XOF")

  // Statut
  isActive        Boolean   @default(true)
  isAvailable     Boolean   @default(true)

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  beds            Bed[]
  admissions      Admission[]

  @@unique([organizationId, number])
  @@map("rooms")
}

// Lits dans les chambres
model Bed {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  number          String    // Numéro du lit dans la chambre (ex: "A", "1", "2")
  roomId          String
  room            Room      @relation(fields: [roomId], references: [id])

  // Type et caractéristiques
  bedType         BedType   @default(STANDARD)
  isElectric      Boolean   @default(false) // Lit électrique
  hasOxygen       Boolean   @default(false) // Prise d'oxygène
  hasMonitoring   Boolean   @default(false) // Monitoring cardiaque

  // Statut
  status          BedStatus @default(AVAILABLE)
  isActive        Boolean   @default(true)

  // Notes
  description     String?   // Description ou notes
  lastMaintenance DateTime? // Dernière maintenance

  // Métadonnées
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  admissions      Admission[]

  @@unique([roomId, number])
  @@map("beds")
}

// Admissions/Hospitalisations
model Admission {
  id                    String    @id @default(cuid())

  // Multi-tenant
  organizationId        String
  organization          Organization @relation(fields: [organizationId], references: [id])

  // Numérotation
  admissionNumber       String    // ADM-YYYYMMDD-XXXX

  // Patient et médecin
  patientId             String
  patient               Patient   @relation(fields: [patientId], references: [id])
  doctorId              String
  doctor                User      @relation("DoctorAdmissions", fields: [doctorId], references: [id])

  // Chambre et lit
  roomId                String
  room                  Room      @relation(fields: [roomId], references: [id])
  bedId                 String?
  bed                   Bed?      @relation(fields: [bedId], references: [id])

  // Dates importantes
  admissionDate         DateTime  @default(now())
  expectedDischargeDate DateTime? // Date de sortie prévue
  actualDischargeDate   DateTime? // Date de sortie réelle

  // Informations médicales
  reason                String    // Motif d'hospitalisation
  diagnosis             String?   // Diagnostic principal
  secondaryDiagnosis    String?   // Diagnostics secondaires
  allergies             String?   // Allergies connues
  currentMedications    String?   // Médicaments actuels

  // Statut
  status                AdmissionStatus @default(ADMITTED)

  // Contacts d'urgence
  emergencyContactName  String?
  emergencyContactPhone String?
  emergencyContactRelation String?

  // Informations administratives
  insuranceInfo         String?   // Informations d'assurance
  roomRate              Float?    // Tarif de la chambre
  totalCost             Float?    // Coût total estimé

  // Notes et observations
  admissionNotes        String?   // Notes d'admission
  dischargeNotes        String?   // Notes de sortie
  dischargePrescription String?   // Prescription de sortie

  // Métadonnées
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@unique([organizationId, admissionNumber])
  @@map("admissions")
}

// ===== MODULE RAPPORTS =====

model Report {
  id              String    @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id])

  // Informations du rapport
  title           String    // Titre du rapport
  type            String    // Type: financial, patients, consultations, etc.
  format          String    // Format: PDF, Excel
  status          String    @default("pending") // pending, processing, completed, failed

  // Paramètres de génération
  parameters      String?   // JSON des paramètres utilisés

  // Fichier généré
  fileSize        Int?      // Taille en bytes
  downloadUrl     String?   // URL de téléchargement
  content         String?   // Contenu JSON du rapport
  error           String?   // Message d'erreur si échec

  // Métadonnées
  generatedBy     String
  generatedByUser User      @relation(fields: [generatedBy], references: [id])
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("reports")
}

// ===== MODULE PERMISSIONS =====

// Permissions système
model Permission {
  id          String    @id @default(cuid())

  // Définition de la permission
  action      PermissionAction  // CREATE, READ, UPDATE, DELETE, etc.
  resource    PermissionResource // PATIENTS, CONSULTATIONS, etc.
  conditions  String?   // Conditions JSON (ex: own_patients_only)
  description String?   // Description de la permission

  // Métadonnées
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  rolePermissions RolePermission[]
  userPermissions UserPermission[]

  @@unique([action, resource])
  @@map("permissions")
}

// Permissions par rôle
model RolePermission {
  id           String     @id @default(cuid())

  // Relations
  role         UserRole
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String

  // Conditions spécifiques au rôle
  conditions   String?    // JSON des conditions spécifiques
  isGranted    Boolean    @default(true)

  // Métadonnées
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@unique([role, permissionId])
  @@map("role_permissions")
}

// Permissions spécifiques par utilisateur (surcharge)
model UserPermission {
  id           String     @id @default(cuid())

  // Relations
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  permissionId String

  // Surcharge de permission
  isGranted    Boolean    // true = accordé, false = refusé
  conditions   String?    // JSON des conditions spécifiques
  grantedBy    String?    // ID de l'utilisateur qui a accordé
  reason       String?    // Raison de la surcharge
  expiresAt    DateTime?  // Date d'expiration

  // Métadonnées
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@unique([userId, permissionId])
  @@map("user_permissions")
}

// Sessions utilisateur pour audit
model UserSession {
  id           String    @id @default(cuid())

  // Relations
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String

  // Informations de session
  sessionToken String    @unique
  ipAddress    String?
  userAgent    String?
  location     String?   // Géolocalisation approximative

  // Métadonnées
  loginAt      DateTime  @default(now())
  logoutAt     DateTime?
  isActive     Boolean   @default(true)
  expiresAt    DateTime

  @@map("user_sessions")
}

// Journal d'audit des actions
model AuditLog {
  id           String    @id @default(cuid())

  // Multi-tenant
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  // Relations
  user         User?     @relation(fields: [userId], references: [id])
  userId       String?

  // Détails de l'action
  action       String    // Action effectuée
  resource     String    // Ressource concernée
  resourceId   String?   // ID de la ressource
  oldValues    String?   // Anciennes valeurs (JSON)
  newValues    String?   // Nouvelles valeurs (JSON)

  // Contexte
  ipAddress    String?
  userAgent    String?
  method       String?   // GET, POST, PUT, DELETE
  url          String?   // URL de la requête

  // Métadonnées
  success      Boolean   @default(true)
  errorMessage String?   // Message d'erreur si échec
  duration     Int?      // Durée en millisecondes
  createdAt    DateTime  @default(now())

  @@index([organizationId, userId])
  @@index([organizationId, action])
  @@index([organizationId, resource])
  @@index([createdAt])
  @@map("audit_logs")
}