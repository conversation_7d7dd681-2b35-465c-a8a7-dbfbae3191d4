// GlobalCare Solutions - Schéma Prisma
// Plateforme SaaS pour la gestion hospitalière
// Module: Gestion des Patients

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENUMS - Types de données standardisés
// ========================================

enum UserRole {
  SUPER_ADMIN     // Administrateur système
  ADMIN           // Administrateur de l'hôpital
  DOCTOR          // Médecin
  NURSE           // Infirmier(ère)
  PHARMACIST      // Pharmacien
  RECEPTIONIST    // Réceptionniste
  TECHNICIAN      // Technicien
  ACCOUNTANT      // Comptable
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum BloodType {
  A_POSITIVE
  A_NEGATIVE
  B_POSITIVE
  B_NEGATIVE
  AB_POSITIVE
  AB_NEGATIVE
  O_POSITIVE
  O_NEGATIVE
  UNKNOWN
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
  OTHER
}

enum ConsultationStatus {
  PENDING_PAYMENT  // En attente de paiement
  SCHEDULED        // Programmé (après paiement)
  IN_PROGRESS      // En cours
  COMPLETED        // Terminé
  CANCELLED        // Annulé
  NO_SHOW          // Absent
}

enum ConsultationType {
  GENERAL
  EMERGENCY
  FOLLOW_UP
  SPECIALIST
  TELEMEDICINE
}

enum PrescriptionStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  EXPIRED
}

enum MedicationForm {
  TABLET
  CAPSULE
  SYRUP
  INJECTION
  CREAM
  OINTMENT
  DROPS
  SPRAY
  INHALER
  PATCH
  OTHER
}

enum MedicationCategory {
  ANTIBIOTIC
  ANALGESIC
  ANTI_INFLAMMATORY
  ANTIHYPERTENSIVE
  ANTIDIABETIC
  ANTIHISTAMINE
  ANTACID
  VITAMIN
  SUPPLEMENT
  VACCINE
  OTHER
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  CANCELLED
  REFUNDED
}

// ========================================
// MODÈLES - Entités principales (SaaS Multi-Tenant)
// ========================================

// Organisations (Hôpitaux/Cliniques) - Tenants
model Organization {
  id              String   @id @default(cuid())
  name            String
  slug            String   @unique // URL-friendly identifier
  type            String   // hospital, clinic, medical_center

  // Informations de contact
  email           String?
  phone           String?
  address         String?
  city            String?
  country         String   @default("Mali")
  website         String?

  // Configuration
  logo            String?
  timezone        String   @default("Africa/Bamako")
  currency        String   @default("XOF") // Franc CFA
  language        String   @default("fr")

  // Abonnement SaaS
  subscriptionPlan String  @default("basic") // basic, premium, enterprise
  subscriptionStatus String @default("active") // active, suspended, cancelled
  trialEndsAt     DateTime?
  subscriptionEndsAt DateTime?

  // Limites du plan
  maxUsers        Int      @default(10)
  maxPatients     Int      @default(1000)
  maxStorage      Int      @default(5) // GB

  // Métadonnées
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  users           User[]
  patients        Patient[]
  consultations   Consultation[]
  prescriptions   Prescription[]
  medications     Medication[]
  medicalHistory  MedicalHistory[]
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensings     Dispensing[]
  // Relations facturation
  consultationPrices ConsultationPrice[]
  payments        Payment[]

  @@map("organizations")
}

// Utilisateurs du système (Personnel médical uniquement)
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  password    String
  firstName   String
  lastName    String
  phone       String?
  role        UserRole
  isActive    Boolean  @default(true)
  avatar      String?

  // Multi-tenant
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  // Métadonnées
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLogin   DateTime?

  // Relations
  consultations     Consultation[]
  prescriptions     Prescription[]
  createdPatients   Patient[]      @relation("CreatedBy")
  updatedPatients   Patient[]      @relation("UpdatedBy")
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensings       Dispensing[]   @relation("Pharmacist")
  // Relations facturation
  createdPayments   Payment[]      @relation("PaymentCreatedBy")

  @@map("users")
}

// Patients (pas de compte utilisateur)
model Patient {
  id                String        @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization  @relation(fields: [organizationId], references: [id])

  // Informations personnelles
  firstName         String
  lastName          String
  dateOfBirth       DateTime
  gender            Gender
  phone             String?
  email             String?
  address           String?
  city              String?
  country           String        @default("Mali")

  // Informations médicales
  bloodType         BloodType     @default(UNKNOWN)
  allergies         String?       // JSON string des allergies
  chronicDiseases   String?       // JSON string des maladies chroniques
  emergencyContact  String?       // JSON string du contact d'urgence

  // Informations sociales
  maritalStatus     MaritalStatus @default(SINGLE)
  occupation        String?
  insurance         String?       // Informations d'assurance

  // Métadonnées
  patientNumber     String        // Numéro unique du patient (par organisation)
  isActive          Boolean       @default(true)
  notes             String?       // Notes générales
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  createdBy         User          @relation("CreatedBy", fields: [createdById], references: [id])
  createdById       String
  updatedBy         User?         @relation("UpdatedBy", fields: [updatedById], references: [id])
  updatedById       String?

  consultations     Consultation[]
  prescriptions     Prescription[]
  medicalHistory    MedicalHistory[]
  // Relations facturation
  payments          Payment[]

  // Index composé pour numéro patient unique par organisation
  @@unique([organizationId, patientNumber])
  @@map("patients")
}

// Consultations médicales
model Consultation {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  consultationDate  DateTime
  status            ConsultationStatus  @default(SCHEDULED)
  type              ConsultationType    @default(GENERAL)

  // Contenu médical
  chiefComplaint    String?             // Motif de consultation
  symptoms          String?             // Symptômes (JSON)
  diagnosis         String?             // Diagnostic
  treatment         String?             // Traitement prescrit
  notes             String?             // Notes du médecin

  // Données vitales
  weight            Float?              // Poids en kg
  height            Float?              // Taille en cm
  bloodPressure     String?             // Tension artérielle
  temperature       Float?              // Température en °C
  heartRate         Int?                // Fréquence cardiaque

  // Coûts
  consultationFee   Float?              // Frais de consultation
  paymentStatus     PaymentStatus       @default(PENDING)

  // Métadonnées
  duration          Int?                // Durée en minutes
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  patient           Patient             @relation(fields: [patientId], references: [id])
  patientId         String
  doctor            User                @relation(fields: [doctorId], references: [id])
  doctorId          String

  prescriptions     Prescription[]
  // Relations facturation
  payments          Payment[]

  @@map("consultations")
}

// Médicaments disponibles
model Medication {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  name              String              // Nom commercial
  genericName       String?             // Nom générique
  brand             String?             // Marque
  category          MedicationCategory  @default(OTHER)
  form              MedicationForm      @default(TABLET)

  // Détails médicaux
  strength          String?             // Dosage (ex: 500mg, 10ml)
  activeIngredient  String?             // Principe actif
  description       String?             // Description
  sideEffects       String?             // Effets secondaires (JSON)
  contraindications String?             // Contre-indications (JSON)

  // Informations commerciales
  manufacturer      String?             // Fabricant
  barcode           String?             // Code-barres
  price             Float?              // Prix unitaire
  currency          String              @default("XOF")

  // Stock et disponibilité
  stockQuantity     Int                 @default(0)
  minStockLevel     Int                 @default(10)
  maxStockLevel     Int                 @default(1000)
  expiryDate        DateTime?           // Date d'expiration du stock
  batchNumber       String?             // Numéro de lot

  // Métadonnées
  isActive          Boolean             @default(true)
  requiresPrescription Boolean          @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  prescriptionItems PrescriptionItem[]
  // Relations pharmacie
  pharmacyMovements PharmacyMovement[]
  dispensingItems   DispensingItem[]

  @@unique([organizationId, name, strength])
  @@map("medications")
}

// Prescriptions médicales
model Prescription {
  id                String              @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization        @relation(fields: [organizationId], references: [id])

  // Informations de base
  prescriptionDate  DateTime            @default(now())
  status            PrescriptionStatus  @default(ACTIVE)
  prescriptionNumber String             // Numéro unique de prescription

  // Instructions générales
  generalInstructions String?           // Instructions générales
  notes             String?             // Notes du médecin

  // Métadonnées
  issuedAt          DateTime            @default(now())
  expiresAt         DateTime?           // Date d'expiration
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  patient           Patient             @relation(fields: [patientId], references: [id])
  patientId         String
  doctor            User                @relation(fields: [doctorId], references: [id])
  doctorId          String
  consultation      Consultation?       @relation(fields: [consultationId], references: [id])
  consultationId    String?

  // Items de prescription
  items             PrescriptionItem[]
  // Relations pharmacie
  dispensings       Dispensing[]

  @@unique([organizationId, prescriptionNumber])
  @@map("prescriptions")
}

// Items individuels d'une prescription
model PrescriptionItem {
  id                String        @id @default(cuid())

  // Relations
  prescription      Prescription  @relation(fields: [prescriptionId], references: [id], onDelete: Cascade)
  prescriptionId    String
  medication        Medication?   @relation(fields: [medicationId], references: [id])
  medicationId      String?       // Optionnel pour médicaments externes

  // Support des médicaments externes
  isExternal        Boolean       @default(false)
  externalMedicationName     String?  // Nom du médicament externe
  externalMedicationForm     String?  // Forme (comprimé, sirop, etc.)
  externalMedicationStrength String?  // Dosage (500mg, etc.)
  externalMedicationCategory String?  // Catégorie
  estimatedPrice    Float?        // Prix estimé en FCFA

  // Posologie détaillée
  dosage            String        // Ex: "1 comprimé"
  frequency         String        // Ex: "3 fois par jour"
  duration          String        // Ex: "7 jours"
  timing            String?       // Ex: "avant les repas"
  route             String?       // Voie d'administration

  // Instructions spécifiques
  instructions      String?       // Instructions spéciales pour ce médicament
  quantity          Int           // Quantité totale prescrite

  // Dispensation
  dispensedQuantity Int           @default(0)  // Quantité déjà dispensée
  isDispensed       Boolean       @default(false)  // Complètement dispensé

  // Métadonnées
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations avec dispensation
  dispensingItems   DispensingItem[]

  @@map("prescription_items")
}

// Historique médical
model MedicalHistory {
  id                String    @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization @relation(fields: [organizationId], references: [id])

  // Informations de base
  date              DateTime  @default(now())
  type              String    // Type d'événement médical
  description       String    // Description de l'événement

  // Détails
  severity          String?   // Gravité (mild, moderate, severe)
  outcome           String?   // Résultat
  documents         String?   // JSON des documents associés

  // Métadonnées
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  patient           Patient   @relation(fields: [patientId], references: [id])
  patientId         String

  @@map("medical_history")
}

// ===== GESTION PHARMACIE =====

// Mouvements de stock de la pharmacie
model PharmacyMovement {
  id              String        @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization  @relation(fields: [organizationId], references: [id])

  // Médicament concerné
  medicationId    String
  medication      Medication    @relation(fields: [medicationId], references: [id])

  // Type de mouvement
  type            MovementType  // IN (entrée), OUT (sortie), ADJUSTMENT (ajustement)
  quantity        Int           // Quantité (positive pour entrée, négative pour sortie)
  previousStock   Int           // Stock avant le mouvement
  newStock        Int           // Stock après le mouvement

  // Détails du mouvement
  reason          String?       // Raison du mouvement
  reference       String?       // Référence (bon de livraison, etc.)
  unitPrice       Float?        // Prix unitaire lors du mouvement
  totalValue      Float?        // Valeur totale du mouvement

  // Expiration (pour les entrées)
  expiryDate      DateTime?     // Date d'expiration du lot
  batchNumber     String?       // Numéro de lot

  // Relations
  dispensingId    String?       // Si mouvement lié à une dispensation
  dispensing      Dispensing?   @relation(fields: [dispensingId], references: [id])

  // Utilisateur responsable
  userId          String
  user            User          @relation(fields: [userId], references: [id])

  // Métadonnées
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("pharmacy_movements")
}

// Types de mouvements de stock
enum MovementType {
  IN          // Entrée de stock (livraison, retour)
  OUT         // Sortie de stock (dispensation, péremption)
  ADJUSTMENT  // Ajustement d'inventaire
}

// Dispensations de médicaments
model Dispensing {
  id                String            @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization      @relation(fields: [organizationId], references: [id])

  // Numéro unique de dispensation
  dispensingNumber  String            @unique

  // Prescription liée
  prescriptionId    String
  prescription      Prescription      @relation(fields: [prescriptionId], references: [id])

  // Pharmacien responsable
  pharmacistId      String
  pharmacist        User              @relation("Pharmacist", fields: [pharmacistId], references: [id])

  // Statut de la dispensation
  status            DispensingStatus  @default(PENDING)

  // Montants
  totalAmount       Float             @default(0)    // Montant total
  paidAmount        Float             @default(0)    // Montant payé
  discountAmount    Float             @default(0)    // Remise accordée

  // Paiement
  paymentMethod     PaymentMethod?    // Mode de paiement
  paymentReference  String?           // Référence de paiement

  // Notes
  notes             String?           // Notes du pharmacien

  // Métadonnées
  dispensedAt       DateTime          @default(now())
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  items             DispensingItem[]
  movements         PharmacyMovement[]

  @@unique([organizationId, dispensingNumber])
  @@map("dispensings")
}

// Statuts de dispensation
enum DispensingStatus {
  PENDING     // En attente
  PARTIAL     // Partiellement dispensé
  COMPLETED   // Complètement dispensé
  CANCELLED   // Annulé
}

// Modes de paiement
enum PaymentMethod {
  CASH        
  CARD        
  MOBILE      
  INSURANCE   // Assurance
  CREDIT      // Crédit
  BANK_TRANSFER // Virement bancaire
  CHECK       // Chèque
}

enum InvoiceStatus {
  DRAFT
  PENDING
  PAID
  PARTIALLY_PAID
  OVERDUE
  CANCELLED
}


enum ServiceType {
  CONSULTATION
  PHARMACY
  LABORATORY
  IMAGING
  SURGERY
  HOSPITALIZATION
  EMERGENCY
  VACCINATION
  CERTIFICATE
  TRANSPORT
  OTHER
}

// Items individuels d'une dispensation
model DispensingItem {
  id                  String            @id @default(cuid())

  // Relations
  dispensingId        String
  dispensing          Dispensing        @relation(fields: [dispensingId], references: [id], onDelete: Cascade)

  prescriptionItemId  String
  prescriptionItem    PrescriptionItem  @relation(fields: [prescriptionItemId], references: [id])

  medicationId        String
  medication          Medication        @relation(fields: [medicationId], references: [id])

  // Quantités
  prescribedQuantity  Int               // Quantité prescrite
  dispensedQuantity   Int               // Quantité réellement dispensée

  // Prix
  unitPrice           Float             // Prix unitaire au moment de la dispensation
  totalPrice          Float             // Prix total pour cet item

  // Lot et expiration
  batchNumber         String?           // Numéro de lot dispensé
  expiryDate          DateTime?         // Date d'expiration du lot

  // Métadonnées
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt

  @@map("dispensing_items")
}

// ===== GESTION FACTURATION & PAIEMENTS =====

// Grille tarifaire des consultations
model ConsultationPrice {
  id              String           @id @default(cuid())

  // Multi-tenant
  organizationId  String
  organization    Organization     @relation(fields: [organizationId], references: [id])

  // Type de consultation
  consultationType ConsultationType

  // Tarification
  basePrice       Float            // Prix de base
  emergencyPrice  Float?           // Prix d'urgence (majoration)
  insurancePrice  Float?           // Prix pour les assurés

  // Métadonnées
  isActive        Boolean          @default(true)
  description     String?          // Description du tarif
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  @@unique([organizationId, consultationType])
  @@map("consultation_prices")
}

// Paiements
model Payment {
  id                String        @id @default(cuid())

  // Multi-tenant
  organizationId    String
  organization      Organization  @relation(fields: [organizationId], references: [id])

  // Numérotation
  paymentNumber     String        // Numéro de paiement/reçu
  paymentDate       DateTime      @default(now())

  // Patient
  patientId         String
  patient           Patient       @relation(fields: [patientId], references: [id])

  // Consultation liée
  consultationId    String?
  consultation      Consultation? @relation(fields: [consultationId], references: [id])

  // Montants
  amount            Float         // Montant payé
  currency          String        @default("XOF")

  // Mode de paiement
  paymentMethod     PaymentMethod
  paymentReference  String?       // Référence de transaction (mobile money, etc.)

  // Statut
  status            PaymentStatus @default(PAID)

  // Informations additionnelles
  description       String?       // Description du paiement
  notes             String?       // Notes

  // Métadonnées
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  createdById       String
  createdBy         User          @relation("PaymentCreatedBy", fields: [createdById], references: [id])

  @@unique([organizationId, paymentNumber])
  @@map("payments")
}


