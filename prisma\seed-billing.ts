import { PrismaClient, ConsultationType } from "@prisma/client";

const prisma = new PrismaClient();

async function seedBilling() {
  console.log("🏥 Seeding billing data...");

  try {
    // Récupérer la première organisation
    const organization = await prisma.organization.findFirst();
    
    if (!organization) {
      console.log("❌ Aucune organisation trouvée. Veuillez d'abord créer une organisation.");
      return;
    }

    console.log(`📋 Organisation trouvée: ${organization.name}`);

    // Tarifs de consultation pour le Mali (en FCFA)
    const consultationPrices = [
      {
        consultationType: ConsultationType.GENERAL,
        basePrice: 5000, // 5,000 FCFA
        emergencyPrice: 10000, // 10,000 FCFA
        insurancePrice: 3000, // 3,000 FCFA
        description: "Consultation médicale générale standard",
      },
      {
        consultationType: ConsultationType.SPECIALIST,
        basePrice: 15000, // 15,000 FCFA
        emergencyPrice: 25000, // 25,000 FCFA
        insurancePrice: 10000, // 10,000 FCFA
        description: "Consultation avec médecin spécialiste",
      },
      {
        consultationType: ConsultationType.EMERGENCY,
        basePrice: 20000, // 20,000 FCFA
        emergencyPrice: 30000, // 30,000 FCFA
        insurancePrice: 15000, // 15,000 FCFA
        description: "Consultation d'urgence 24h/24",
      },
      {
        consultationType: ConsultationType.FOLLOW_UP,
        basePrice: 3000, // 3,000 FCFA
        emergencyPrice: 6000, // 6,000 FCFA
        insurancePrice: 2000, // 2,000 FCFA
        description: "Consultation de suivi post-traitement",
      },
      {
        consultationType: ConsultationType.TELEMEDICINE,
        basePrice: 4000, // 4,000 FCFA
        emergencyPrice: 8000, // 8,000 FCFA
        insurancePrice: 2500, // 2,500 FCFA
        description: "Téléconsultation à distance",
      },
    ];

    // Créer ou mettre à jour les tarifs
    for (const priceData of consultationPrices) {
      const consultationPrice = await prisma.consultationPrice.upsert({
        where: {
          organizationId_consultationType: {
            organizationId: organization.id,
            consultationType: priceData.consultationType,
          },
        },
        update: {
          basePrice: priceData.basePrice,
          emergencyPrice: priceData.emergencyPrice,
          insurancePrice: priceData.insurancePrice,
          description: priceData.description,
        },
        create: {
          ...priceData,
          organizationId: organization.id,
        },
      });

      console.log(`✅ Tarif créé/mis à jour: ${priceData.consultationType} - ${priceData.basePrice} FCFA`);
    }

    console.log("🎉 Seeding des données de facturation terminé avec succès!");

  } catch (error) {
    console.error("❌ Erreur lors du seeding des données de facturation:", error);
    throw error;
  }
}

// Exécuter le seeding si ce fichier est appelé directement
if (require.main === module) {
  seedBilling()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedBilling };
