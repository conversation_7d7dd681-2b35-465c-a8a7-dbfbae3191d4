import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Médicaments de base pour l'Afrique de l'Ouest
const medications = [
  // Antibiotiques
  {
    name: 'Amoxicilline',
    genericName: 'Amoxicillin',
    category: 'ANTIBIOTIC',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Amoxicillin trihydrate',
    description: 'Antibiotique à large spectre de la famille des pénicillines',
    manufacturer: 'Pharma Mali',
    price: 250,
    stockQuantity: 500,
    minStockLevel: 50,
    maxStockLevel: 1000,
    requiresPrescription: true,
  },
  {
    name: 'Amoxicilline Sirop',
    genericName: 'Amoxicillin',
    category: 'ANTIBIOTIC',
    form: 'SYRUP',
    strength: '125mg/5ml',
    activeIngredient: 'Amoxicillin trihydrate',
    description: 'Antibiotique en sirop pour enfants',
    manufacturer: 'Pharma Mali',
    price: 1200,
    stockQuantity: 200,
    minStockLevel: 20,
    maxStockLevel: 500,
    requiresPrescription: true,
  },
  {
    name: 'Azithromycine',
    genericName: 'Azithromycin',
    category: 'ANTIBIOTIC',
    form: 'TABLET',
    strength: '250mg',
    activeIngredient: 'Azithromycin dihydrate',
    description: 'Antibiotique macrolide',
    manufacturer: 'Cipla',
    price: 500,
    stockQuantity: 300,
    minStockLevel: 30,
    maxStockLevel: 600,
    requiresPrescription: true,
  },
  {
    name: 'Ciprofloxacine',
    genericName: 'Ciprofloxacin',
    category: 'ANTIBIOTIC',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Ciprofloxacin hydrochloride',
    description: 'Antibiotique fluoroquinolone',
    manufacturer: 'Ranbaxy',
    price: 400,
    stockQuantity: 250,
    minStockLevel: 25,
    maxStockLevel: 500,
    requiresPrescription: true,
  },

  // Antalgiques
  {
    name: 'Paracétamol',
    genericName: 'Acetaminophen',
    category: 'ANALGESIC',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Paracetamol',
    description: 'Antalgique et antipyrétique',
    manufacturer: 'Pharma Mali',
    price: 50,
    stockQuantity: 1000,
    minStockLevel: 100,
    maxStockLevel: 2000,
    requiresPrescription: false,
  },
  {
    name: 'Paracétamol Sirop',
    genericName: 'Acetaminophen',
    category: 'ANALGESIC',
    form: 'SYRUP',
    strength: '120mg/5ml',
    activeIngredient: 'Paracetamol',
    description: 'Antalgique et antipyrétique pour enfants',
    manufacturer: 'Pharma Mali',
    price: 800,
    stockQuantity: 300,
    minStockLevel: 30,
    maxStockLevel: 600,
    requiresPrescription: false,
  },
  {
    name: 'Ibuprofène',
    genericName: 'Ibuprofen',
    category: 'ANTI_INFLAMMATORY',
    form: 'TABLET',
    strength: '400mg',
    activeIngredient: 'Ibuprofen',
    description: 'Anti-inflammatoire non stéroïdien',
    manufacturer: 'Cipla',
    price: 150,
    stockQuantity: 400,
    minStockLevel: 40,
    maxStockLevel: 800,
    requiresPrescription: false,
  },
  {
    name: 'Aspirine',
    genericName: 'Acetylsalicylic acid',
    category: 'ANALGESIC',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Acetylsalicylic acid',
    description: 'Antalgique, antipyrétique et anti-inflammatoire',
    manufacturer: 'Bayer',
    price: 100,
    stockQuantity: 600,
    minStockLevel: 60,
    maxStockLevel: 1200,
    requiresPrescription: false,
  },

  // Antihypertenseurs
  {
    name: 'Amlodipine',
    genericName: 'Amlodipine',
    category: 'ANTIHYPERTENSIVE',
    form: 'TABLET',
    strength: '5mg',
    activeIngredient: 'Amlodipine besylate',
    description: 'Inhibiteur calcique pour l\'hypertension',
    manufacturer: 'Pfizer',
    price: 300,
    stockQuantity: 200,
    minStockLevel: 20,
    maxStockLevel: 400,
    requiresPrescription: true,
  },
  {
    name: 'Lisinopril',
    genericName: 'Lisinopril',
    category: 'ANTIHYPERTENSIVE',
    form: 'TABLET',
    strength: '10mg',
    activeIngredient: 'Lisinopril dihydrate',
    description: 'Inhibiteur de l\'ECA pour l\'hypertension',
    manufacturer: 'Merck',
    price: 350,
    stockQuantity: 150,
    minStockLevel: 15,
    maxStockLevel: 300,
    requiresPrescription: true,
  },

  // Antidiabétiques
  {
    name: 'Metformine',
    genericName: 'Metformin',
    category: 'ANTIDIABETIC',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Metformin hydrochloride',
    description: 'Antidiabétique oral',
    manufacturer: 'Teva',
    price: 200,
    stockQuantity: 300,
    minStockLevel: 30,
    maxStockLevel: 600,
    requiresPrescription: true,
  },
  {
    name: 'Glibenclamide',
    genericName: 'Glyburide',
    category: 'ANTIDIABETIC',
    form: 'TABLET',
    strength: '5mg',
    activeIngredient: 'Glibenclamide',
    description: 'Sulfamide hypoglycémiant',
    manufacturer: 'Sanofi',
    price: 180,
    stockQuantity: 250,
    minStockLevel: 25,
    maxStockLevel: 500,
    requiresPrescription: true,
  },

  // Vitamines et suppléments
  {
    name: 'Vitamine C',
    genericName: 'Ascorbic acid',
    category: 'VITAMIN',
    form: 'TABLET',
    strength: '500mg',
    activeIngredient: 'Ascorbic acid',
    description: 'Supplément de vitamine C',
    manufacturer: 'Nature\'s Bounty',
    price: 120,
    stockQuantity: 400,
    minStockLevel: 40,
    maxStockLevel: 800,
    requiresPrescription: false,
  },
  {
    name: 'Fer + Acide Folique',
    genericName: 'Iron + Folic acid',
    category: 'SUPPLEMENT',
    form: 'TABLET',
    strength: '60mg + 400mcg',
    activeIngredient: 'Ferrous sulfate + Folic acid',
    description: 'Supplément de fer et acide folique',
    manufacturer: 'UNICEF',
    price: 80,
    stockQuantity: 500,
    minStockLevel: 50,
    maxStockLevel: 1000,
    requiresPrescription: false,
  },
  {
    name: 'Multivitamines',
    genericName: 'Multivitamin',
    category: 'VITAMIN',
    form: 'TABLET',
    strength: 'Complex',
    activeIngredient: 'Vitamines A, B, C, D, E',
    description: 'Complexe multivitaminé',
    manufacturer: 'Centrum',
    price: 250,
    stockQuantity: 200,
    minStockLevel: 20,
    maxStockLevel: 400,
    requiresPrescription: false,
  },

  // Antihistaminiques
  {
    name: 'Loratadine',
    genericName: 'Loratadine',
    category: 'ANTIHISTAMINE',
    form: 'TABLET',
    strength: '10mg',
    activeIngredient: 'Loratadine',
    description: 'Antihistaminique non sédatif',
    manufacturer: 'Claritin',
    price: 200,
    stockQuantity: 150,
    minStockLevel: 15,
    maxStockLevel: 300,
    requiresPrescription: false,
  },
  {
    name: 'Cétirizine',
    genericName: 'Cetirizine',
    category: 'ANTIHISTAMINE',
    form: 'TABLET',
    strength: '10mg',
    activeIngredient: 'Cetirizine hydrochloride',
    description: 'Antihistaminique H1',
    manufacturer: 'Zyrtec',
    price: 180,
    stockQuantity: 200,
    minStockLevel: 20,
    maxStockLevel: 400,
    requiresPrescription: false,
  },

  // Antiacides
  {
    name: 'Oméprazole',
    genericName: 'Omeprazole',
    category: 'ANTACID',
    form: 'CAPSULE',
    strength: '20mg',
    activeIngredient: 'Omeprazole',
    description: 'Inhibiteur de la pompe à protons',
    manufacturer: 'AstraZeneca',
    price: 300,
    stockQuantity: 180,
    minStockLevel: 18,
    maxStockLevel: 360,
    requiresPrescription: true,
  },
  {
    name: 'Ranitidine',
    genericName: 'Ranitidine',
    category: 'ANTACID',
    form: 'TABLET',
    strength: '150mg',
    activeIngredient: 'Ranitidine hydrochloride',
    description: 'Antagoniste des récepteurs H2',
    manufacturer: 'GSK',
    price: 150,
    stockQuantity: 220,
    minStockLevel: 22,
    maxStockLevel: 440,
    requiresPrescription: false,
  },

  // Crèmes et pommades
  {
    name: 'Bétaméthasone Crème',
    genericName: 'Betamethasone',
    category: 'ANTI_INFLAMMATORY',
    form: 'CREAM',
    strength: '0.1%',
    activeIngredient: 'Betamethasone valerate',
    description: 'Corticostéroïde topique',
    manufacturer: 'GSK',
    price: 800,
    stockQuantity: 100,
    minStockLevel: 10,
    maxStockLevel: 200,
    requiresPrescription: true,
  },
  {
    name: 'Néomycine Pommade',
    genericName: 'Neomycin',
    category: 'ANTIBIOTIC',
    form: 'OINTMENT',
    strength: '0.5%',
    activeIngredient: 'Neomycin sulfate',
    description: 'Antibiotique topique',
    manufacturer: 'Johnson & Johnson',
    price: 600,
    stockQuantity: 120,
    minStockLevel: 12,
    maxStockLevel: 240,
    requiresPrescription: false,
  }
]

async function seedMedications() {
  try {
    console.log('🌱 Début du seeding des médicaments...')

    // Récupérer la première organisation
    const organization = await prisma.organization.findFirst()
    
    if (!organization) {
      console.error('❌ Aucune organisation trouvée. Veuillez d\'abord créer une organisation.')
      return
    }

    console.log(`📋 Organisation trouvée: ${organization.name}`)

    // Supprimer les médicaments existants pour cette organisation
    await prisma.medication.deleteMany({
      where: {
        organizationId: organization.id
      }
    })

    console.log('🗑️ Médicaments existants supprimés')

    // Créer les nouveaux médicaments
    for (const medicationData of medications) {
      await prisma.medication.create({
        data: {
          ...medicationData,
          organizationId: organization.id,
        }
      })
    }

    console.log(`✅ ${medications.length} médicaments créés avec succès!`)
    
    // Afficher un résumé par catégorie
    const categories = await prisma.medication.groupBy({
      by: ['category'],
      where: {
        organizationId: organization.id
      },
      _count: {
        category: true
      }
    })

    console.log('\n📊 Résumé par catégorie:')
    categories.forEach(cat => {
      console.log(`  - ${cat.category}: ${cat._count.category} médicaments`)
    })

  } catch (error) {
    console.error('💥 Erreur lors du seeding des médicaments:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Exécuter le seeding si ce fichier est appelé directement
if (require.main === module) {
  seedMedications()
}

export { seedMedications }
