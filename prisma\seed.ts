import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding de la base de données...')

  // 1. Créer l'organisation
  console.log('🏥 Création de l\'organisation...')
  const organization = await prisma.organization.upsert({
    where: { slug: 'hopital-mali-demo' },
    update: {},
    create: {
      name: 'Hôpital du Mali - Demo',
      slug: 'hopital-mali-demo',
      type: 'hospital',
      email: '<EMAIL>',
      phone: '+223 20 22 27 12',
      address: 'Avenue de la Liberté, Bamako',
      city: 'Bamako',
      country: 'Mali',
      website: 'https://hopital-mali.ml',
      timezone: 'Africa/Bamako',
      currency: 'XOF',
      language: 'fr',
      subscriptionPlan: 'premium',
      subscriptionStatus: 'active',
      maxUsers: 50,
      maxPatients: 5000,
      maxStorage: 100,
      isActive: true,
    },
  })

  console.log(`✅ Organisation créée: ${organization.name}`)

  // 2. Créer l'utilisateur administrateur
  console.log('👤 Création de l\'utilisateur administrateur...')
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Dr. Amadou',
      lastName: 'TRAORE',
      phone: '+223 70 12 34 56',
      role: 'ADMIN',
      isActive: true,
      organizationId: organization.id,
    },
  })

  console.log(`✅ Utilisateur admin créé: ${adminUser.firstName} ${adminUser.lastName}`)

  // 3. Créer des médecins
  console.log('👨‍⚕️ Création des médecins...')
  const doctors = [
    {
      email: '<EMAIL>',
      firstName: 'Dr. Fatou',
      lastName: 'KONE',
      phone: '+223 70 11 22 33',
      role: 'DOCTOR' as const,
    },
    {
      email: '<EMAIL>',
      firstName: 'Dr. Ibrahim',
      lastName: 'DIALLO',
      phone: '+223 70 44 55 66',
      role: 'DOCTOR' as const,
    },
    {
      email: '<EMAIL>',
      firstName: 'Dr. Aminata',
      lastName: 'SANGARE',
      phone: '+223 70 77 88 99',
      role: 'DOCTOR' as const,
    },
  ]

  for (const doctorData of doctors) {
    await prisma.user.upsert({
      where: { email: doctorData.email },
      update: {},
      create: {
        ...doctorData,
        password: hashedPassword,
        isActive: true,
        organizationId: organization.id,
      },
    })
  }

  console.log(`✅ ${doctors.length} médecins créés`)

  // 4. Créer des patients de test
  console.log('🧑‍🤝‍🧑 Création des patients de test...')
  const patients = [
    {
      firstName: 'Mamadou',
      lastName: 'SIDIBE',
      dateOfBirth: new Date('1985-03-15'),
      gender: 'MALE' as const,
      phone: '+223 65 11 22 33',
      email: '<EMAIL>',
      address: 'Quartier Hippodrome, Bamako',
      city: 'Bamako',
      bloodType: 'O_POSITIVE' as const,
      patientNumber: 'PAT-001',
    },
    {
      firstName: 'Aissata',
      lastName: 'COULIBALY',
      dateOfBirth: new Date('1992-07-22'),
      gender: 'FEMALE' as const,
      phone: '+223 65 44 55 66',
      email: '<EMAIL>',
      address: 'Quartier ACI 2000, Bamako',
      city: 'Bamako',
      bloodType: 'A_POSITIVE' as const,
      patientNumber: 'PAT-002',
    },
    {
      firstName: 'Sekou',
      lastName: 'KEITA',
      dateOfBirth: new Date('1978-11-08'),
      gender: 'MALE' as const,
      phone: '+223 65 77 88 99',
      address: 'Quartier Magnambougou, Bamako',
      city: 'Bamako',
      bloodType: 'B_POSITIVE' as const,
      patientNumber: 'PAT-003',
    },
    {
      firstName: 'Mariam',
      lastName: 'TOURE',
      dateOfBirth: new Date('1995-01-30'),
      gender: 'FEMALE' as const,
      phone: '+223 65 12 34 56',
      email: '<EMAIL>',
      address: 'Quartier Lafiabougou, Bamako',
      city: 'Bamako',
      bloodType: 'AB_POSITIVE' as const,
      patientNumber: 'PAT-004',
    },
    {
      firstName: 'Boubacar',
      lastName: 'DEMBELE',
      dateOfBirth: new Date('1988-09-12'),
      gender: 'MALE' as const,
      phone: '+223 65 98 76 54',
      address: 'Quartier Kalaban Coura, Bamako',
      city: 'Bamako',
      bloodType: 'O_NEGATIVE' as const,
      patientNumber: 'PAT-005',
    },
  ]

  for (const patientData of patients) {
    await prisma.patient.upsert({
      where: { 
        organizationId_patientNumber: {
          organizationId: organization.id,
          patientNumber: patientData.patientNumber
        }
      },
      update: {},
      create: {
        ...patientData,
        organizationId: organization.id,
        createdById: adminUser.id,
      },
    })
  }

  console.log(`✅ ${patients.length} patients créés`)

  // 5. Créer des médicaments
  console.log('💊 Création des médicaments...')
  const medications = [
    {
      name: 'Paracétamol',
      genericName: 'Acetaminophen',
      category: 'ANALGESIC' as const,
      form: 'TABLET' as const,
      strength: '500mg',
      activeIngredient: 'Paracetamol',
      description: 'Antalgique et antipyrétique',
      manufacturer: 'Pharma Mali',
      price: 50,
      stockQuantity: 1000,
      requiresPrescription: false,
    },
    {
      name: 'Amoxicilline',
      genericName: 'Amoxicillin',
      category: 'ANTIBIOTIC' as const,
      form: 'TABLET' as const,
      strength: '500mg',
      activeIngredient: 'Amoxicillin trihydrate',
      description: 'Antibiotique à large spectre',
      manufacturer: 'Pharma Mali',
      price: 250,
      stockQuantity: 500,
      requiresPrescription: true,
    },
    {
      name: 'Ibuprofène',
      genericName: 'Ibuprofen',
      category: 'ANTI_INFLAMMATORY' as const,
      form: 'TABLET' as const,
      strength: '400mg',
      activeIngredient: 'Ibuprofen',
      description: 'Anti-inflammatoire non stéroïdien',
      manufacturer: 'Cipla',
      price: 150,
      stockQuantity: 400,
      requiresPrescription: false,
    },
    {
      name: 'Amlodipine',
      genericName: 'Amlodipine',
      category: 'ANTIHYPERTENSIVE' as const,
      form: 'TABLET' as const,
      strength: '5mg',
      activeIngredient: 'Amlodipine besylate',
      description: 'Inhibiteur calcique pour l\'hypertension',
      manufacturer: 'Pfizer',
      price: 300,
      stockQuantity: 200,
      requiresPrescription: true,
    },
    {
      name: 'Metformine',
      genericName: 'Metformin',
      category: 'ANTIDIABETIC' as const,
      form: 'TABLET' as const,
      strength: '500mg',
      activeIngredient: 'Metformin hydrochloride',
      description: 'Antidiabétique oral',
      manufacturer: 'Teva',
      price: 200,
      stockQuantity: 300,
      requiresPrescription: true,
    },
  ]

  for (const medicationData of medications) {
    await prisma.medication.upsert({
      where: {
        organizationId_name_strength: {
          organizationId: organization.id,
          name: medicationData.name,
          strength: medicationData.strength || '',
        }
      },
      update: {},
      create: {
        ...medicationData,
        organizationId: organization.id,
      },
    })
  }

  console.log(`✅ ${medications.length} médicaments créés`)

  console.log('\n🎉 Seeding terminé avec succès!')
  console.log('\n📋 Informations de connexion:')
  console.log('  Email: <EMAIL>')
  console.log('  Mot de passe: admin123')
  console.log('\n🌐 Application disponible sur: http://localhost:3001')
}

main()
  .catch((e) => {
    console.error('💥 Erreur lors du seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
