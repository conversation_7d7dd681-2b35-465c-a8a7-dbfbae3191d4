const fs = require('fs');
const path = require('path');

// Mapping des fichiers vers leurs permissions
const FILE_PERMISSIONS = {
  'appointments.ts': {
    resource: 'CONSULTATIONS',
    actions: ['CREATE', 'READ', 'UPDATE', 'DELETE']
  },
  'medications.ts': {
    resource: 'MEDICATIONS',
    actions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  'pharmacy.ts': {
    resource: 'PHARMACY',
    actions: ['READ', 'MANAGE']
  },
  'laboratory.ts': {
    resource: 'LABORATORY',
    actions: ['CREATE', 'READ', 'UPDATE', 'APPROVE', 'PRINT']
  },
  'hospitalization.ts': {
    resource: 'HOSPITALIZATION',
    actions: ['CREATE', 'READ', 'UPDATE', 'MANAGE']
  },
  'employees.ts': {
    resource: 'EMPLOYEES',
    actions: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE']
  },
  'departments.ts': {
    resource: 'DEPARTMENTS',
    actions: ['CREATE', 'READ', 'UPDATE', 'DELETE']
  },
  'payments.ts': {
    resource: 'PAYMENTS',
    actions: ['CREATE', 'READ', 'UPDATE', 'MANAGE']
  },
  'reports.ts': {
    resource: 'REPORTS',
    actions: ['CREATE', 'READ', 'EXPORT']
  },
  'analytics.ts': {
    resource: 'ANALYTICS',
    actions: ['READ', 'EXPORT']
  }
};

// Fonctions à protéger par action
const FUNCTION_PATTERNS = {
  'CREATE': ['create', 'add', 'new'],
  'READ': ['get', 'find', 'list', 'fetch'],
  'UPDATE': ['update', 'edit', 'modify'],
  'DELETE': ['delete', 'remove'],
  'MANAGE': ['manage'],
  'APPROVE': ['approve', 'validate'],
  'EXPORT': ['export'],
  'PRINT': ['print']
};

function addImportIfMissing(content) {
  if (!content.includes('import { checkPermission, logAction } from "./permissions"')) {
    // Trouver la dernière ligne d'import
    const lines = content.split('\n');
    let lastImportIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ') && !lines[i].includes('./permissions')) {
        lastImportIndex = i;
      }
    }
    
    if (lastImportIndex !== -1) {
      lines.splice(lastImportIndex + 1, 0, 'import { checkPermission, logAction } from "./permissions";');
      return lines.join('\n');
    }
  }
  return content;
}

function getActionForFunction(functionName) {
  for (const [action, patterns] of Object.entries(FUNCTION_PATTERNS)) {
    for (const pattern of patterns) {
      if (functionName.toLowerCase().includes(pattern)) {
        return action;
      }
    }
  }
  return null;
}

function addPermissionCheck(content, functionName, action, resource) {
  const permissionCheck = `
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const can${action.charAt(0) + action.slice(1).toLowerCase()} = await checkPermission(
      session.user.id,
      "${action}",
      "${resource}"${action !== 'CREATE' && action !== 'READ' ? ',\n      id' : ''}
    );

    if (!can${action.charAt(0) + action.slice(1).toLowerCase()}) {
      await logAction("${action}", "${resource}", ${action !== 'CREATE' && action !== 'READ' ? 'id' : 'undefined'}, undefined, ${action === 'CREATE' || action === 'UPDATE' ? 'data' : 'undefined'}, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de ${getActionDescription(action)} ${getResourceDescription(resource)}");
    }`;

  // Trouver où insérer la vérification (après la validation de session)
  const sessionCheckPattern = /if \(!session\?\.\s*user\?\.\s*organizationId\) \{[\s\S]*?\}/;
  const match = content.match(sessionCheckPattern);
  
  if (match) {
    const insertIndex = content.indexOf(match[0]) + match[0].length;
    return content.slice(0, insertIndex) + permissionCheck + content.slice(insertIndex);
  }
  
  return content;
}

function getActionDescription(action) {
  const descriptions = {
    'CREATE': 'créer',
    'READ': 'consulter',
    'UPDATE': 'modifier',
    'DELETE': 'supprimer',
    'MANAGE': 'gérer',
    'APPROVE': 'approuver',
    'EXPORT': 'exporter',
    'PRINT': 'imprimer'
  };
  return descriptions[action] || action.toLowerCase();
}

function getResourceDescription(resource) {
  const descriptions = {
    'PATIENTS': 'les patients',
    'CONSULTATIONS': 'les consultations',
    'PRESCRIPTIONS': 'les prescriptions',
    'MEDICATIONS': 'les médicaments',
    'PHARMACY': 'la pharmacie',
    'LABORATORY': 'le laboratoire',
    'HOSPITALIZATION': 'les hospitalisations',
    'EMPLOYEES': 'les employés',
    'DEPARTMENTS': 'les départements',
    'PAYMENTS': 'les paiements',
    'REPORTS': 'les rapports',
    'ANALYTICS': 'les analytics'
  };
  return descriptions[resource] || resource.toLowerCase();
}

function processFile(filePath) {
  const fileName = path.basename(filePath);
  const permissions = FILE_PERMISSIONS[fileName];
  
  if (!permissions) {
    console.log(`⏭️  Ignoré: ${fileName} (pas de permissions définies)`);
    return;
  }

  if (!fs.existsSync(filePath)) {
    console.log(`⏭️  Ignoré: ${fileName} (fichier non trouvé)`);
    return;
  }

  console.log(`🔧 Traitement: ${fileName}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Ajouter l'import si manquant
  content = addImportIfMissing(content);
  
  // Trouver toutes les fonctions exportées
  const functionRegex = /export\s+async\s+function\s+(\w+)/g;
  let match;
  const functions = [];
  
  while ((match = functionRegex.exec(content)) !== null) {
    functions.push(match[1]);
  }
  
  console.log(`   Fonctions trouvées: ${functions.join(', ')}`);
  
  // Ajouter les vérifications de permissions
  for (const functionName of functions) {
    const action = getActionForFunction(functionName);
    if (action && permissions.actions.includes(action)) {
      console.log(`   ✅ Ajout permission ${action} pour ${functionName}`);
      content = addPermissionCheck(content, functionName, action, permissions.resource);
    } else {
      console.log(`   ⏭️  Ignoré ${functionName} (action non reconnue ou non autorisée)`);
    }
  }
  
  // Sauvegarder le fichier modifié
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`   💾 Sauvegardé: ${fileName}`);
}

function main() {
  console.log('🔐 Application des permissions sur les Server Actions...\n');
  
  const actionsDir = path.join(__dirname, '..', 'src', 'lib', 'actions');
  
  for (const fileName of Object.keys(FILE_PERMISSIONS)) {
    const filePath = path.join(actionsDir, fileName);
    processFile(filePath);
    console.log('');
  }
  
  console.log('✅ Permissions appliquées sur tous les fichiers !');
}

if (require.main === module) {
  main();
}
