const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDoctorPermissions() {
  try {
    console.log('🔍 Vérification des permissions DOCTOR...');

    // Vérifier les permissions du rôle DOCTOR
    const doctorPermissions = await prisma.rolePermission.findMany({
      where: {
        role: 'DOCTOR',
        isGranted: true,
      },
      include: {
        permission: true,
      },
    });

    console.log(`\n📋 Permissions DOCTOR (${doctorPermissions.length}):`);
    doctorPermissions.forEach(rp => {
      console.log(`  ✅ ${rp.permission.action} sur ${rp.permission.resource}`);
    });

    // Vérifier spécifiquement les permissions PATIENTS
    const patientPermissions = doctorPermissions.filter(rp => 
      rp.permission.resource === 'PATIENTS'
    );

    console.log(`\n👥 Permissions PATIENTS pour DOCTOR:`);
    if (patientPermissions.length > 0) {
      patientPermissions.forEach(rp => {
        console.log(`  ✅ ${rp.permission.action} sur PATIENTS`);
      });
    } else {
      console.log(`  ❌ Aucune permission PATIENTS`);
    }

    // Vérifier l'utilisateur <EMAIL>
    const doctorUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (doctorUser) {
      console.log(`\n👤 Utilisateur <EMAIL>:`);
      console.log(`   Rôle: ${doctorUser.role}`);
      console.log(`   Actif: ${doctorUser.isActive}`);
    } else {
      console.log(`\n❌ Utilisateur <EMAIL> non trouvé`);
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDoctorPermissions();
