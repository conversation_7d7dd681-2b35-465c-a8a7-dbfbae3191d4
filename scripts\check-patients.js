// Script pour vérifier les patients dans la base de données
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkPatients() {
  try {
    console.log('🔍 Vérification des patients dans la base de données...')
    
    // Compter tous les patients
    const totalPatients = await prisma.patient.count()
    console.log(`📊 Total des patients: ${totalPatients}`)
    
    // Récupérer tous les patients
    const patients = await prisma.patient.findMany({
      include: {
        organization: {
          select: {
            name: true
          }
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        _count: {
          select: {
            consultations: true,
            prescriptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    console.log('\n👥 Liste des patients:')
    patients.forEach((patient, index) => {
      console.log(`${index + 1}. ${patient.firstName} ${patient.lastName}`)
      console.log(`   📋 Numéro: ${patient.patientNumber}`)
      console.log(`   🏥 Organisation: ${patient.organization.name}`)
      console.log(`   📅 Créé le: ${patient.createdAt.toLocaleDateString('fr-FR')}`)
      console.log(`   ✅ Actif: ${patient.isActive ? 'Oui' : 'Non'}`)
      console.log(`   📞 Téléphone: ${patient.phone || 'Non renseigné'}`)
      console.log(`   📧 Email: ${patient.email || 'Non renseigné'}`)
      console.log('   ---')
    })
    
    // Vérifier les organisations
    const organizations = await prisma.organization.findMany({
      include: {
        _count: {
          select: {
            patients: true,
            users: true
          }
        }
      }
    })
    
    console.log('\n🏢 Organisations:')
    organizations.forEach((org, index) => {
      console.log(`${index + 1}. ${org.name} (${org.slug})`)
      console.log(`   👥 ${org._count.patients} patients`)
      console.log(`   👨‍⚕️ ${org._count.users} utilisateurs`)
      console.log('   ---')
    })
    
  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkPatients()
