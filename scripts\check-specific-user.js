const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSpecificUser() {
  try {
    console.log('🔍 Vérification de l\'utilisateur <EMAIL>...');

    // Trouver l'utilisateur spécifique
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        organization: true,
      },
    });

    if (!user) {
      console.log('❌ Utilisateur non trouvé');
      return;
    }

    console.log(`👤 Utilisateur trouvé:`);
    console.log(`   Nom: ${user.firstName} ${user.lastName}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Rôle: ${user.role}`);
    console.log(`   Actif: ${user.isActive}`);
    console.log(`   ID: ${user.id}`);
    console.log(`   Organisation: ${user.organization.name}`);

    // Vérifier si le rôle est correct
    if (user.role === 'ADMIN' || user.role === 'SUPER_ADMIN') {
      console.log('✅ Le rôle est correct pour accéder aux permissions');
    } else {
      console.log('❌ Le rôle n\'est pas suffisant pour accéder aux permissions');
      console.log('🔧 Promotion en cours...');
      
      // Promouvoir en ADMIN
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { role: 'ADMIN' },
      });
      
      console.log(`✅ Utilisateur promu au rôle: ${updatedUser.role}`);
    }

    // Vérifier les permissions ADMIN dans la base
    const adminPermissions = await prisma.rolePermission.count({
      where: {
        role: 'ADMIN',
        isGranted: true,
      },
    });

    console.log(`\n📊 Permissions ADMIN configurées: ${adminPermissions}`);

    // Vérifier spécifiquement les permissions ROLES
    const rolesPermissions = await prisma.rolePermission.findMany({
      where: {
        role: 'ADMIN',
        permission: {
          resource: 'ROLES',
        },
        isGranted: true,
      },
      include: {
        permission: true,
      },
    });

    console.log(`\n🔐 Permissions ROLES pour ADMIN:`);
    if (rolesPermissions.length > 0) {
      rolesPermissions.forEach(rp => {
        console.log(`   ✅ ${rp.permission.action} sur ROLES`);
      });
    } else {
      console.log('   ❌ Aucune permission ROLES trouvée');
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpecificUser();
