import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkUserPermissions() {
  try {
    console.log('🔍 Vérification des utilisateurs et permissions...');

    // Récupérer l'organisation
    const organization = await prisma.organization.findFirst({
      where: { slug: 'hopital-mali-demo' },
    });

    if (!organization) {
      throw new Error('Organisation non trouvée');
    }

    console.log(`🏥 Organisation: ${organization.name}`);

    // Récupérer tous les utilisateurs
    const users = await prisma.user.findMany({
      where: {
        organizationId: organization.id,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
      },
      orderBy: {
        role: 'asc',
      },
    });

    console.log(`\n👥 Utilisateurs trouvés (${users.length}):`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (${user.email})`);
      console.log(`   Rôle: ${user.role} | Actif: ${user.isActive}`);
      console.log(`   ID: ${user.id}`);
    });

    // Vérifier les permissions ROLES pour chaque utilisateur
    console.log(`\n🔐 Vérification des permissions ROLES:`);
    
    for (const user of users) {
      // Vérifier les permissions du rôle
      const rolePermissions = await prisma.rolePermission.findMany({
        where: {
          role: user.role,
        },
        include: {
          permission: true,
        },
      });

      const rolesPermissions = rolePermissions.filter(rp => 
        rp.permission.resource === 'ROLES' && rp.isGranted
      );

      console.log(`\n${user.firstName} ${user.lastName} (${user.role}):`);
      if (rolesPermissions.length > 0) {
        rolesPermissions.forEach(rp => {
          console.log(`  ✅ ${rp.permission.action} sur ROLES`);
        });
      } else {
        console.log(`  ❌ Aucune permission sur ROLES`);
      }

      // Vérifier les permissions spécifiques utilisateur
      const userPermissions = await prisma.userPermission.findMany({
        where: {
          userId: user.id,
        },
        include: {
          permission: true,
        },
      });

      if (userPermissions.length > 0) {
        console.log(`  Permissions spécifiques:`);
        userPermissions.forEach(up => {
          console.log(`    ${up.isGranted ? '✅' : '❌'} ${up.permission.action} sur ${up.permission.resource}`);
        });
      }
    }

    // Vérifier les permissions disponibles pour ROLES
    console.log(`\n📋 Permissions disponibles pour ROLES:`);
    const rolesPermissions = await prisma.permission.findMany({
      where: {
        resource: 'ROLES',
        isActive: true,
      },
    });

    rolesPermissions.forEach(perm => {
      console.log(`  - ${perm.action}: ${perm.description}`);
    });

  } catch (error) {
    console.error('❌ Error checking permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  checkUserPermissions()
    .then(() => {
      console.log('\n🎉 Check completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Check failed:', error);
      process.exit(1);
    });
}

export default checkUserPermissions;
