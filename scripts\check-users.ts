import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('🔍 Vérification des utilisateurs...');

    // Récupérer tous les utilisateurs
    const users = await prisma.user.findMany({
      include: {
        organization: true,
      },
    });

    console.log(`👥 ${users.length} utilisateur(s) trouvé(s):`);

    for (const user of users) {
      console.log(`
📧 Email: ${user.email}
👤 Nom: ${user.firstName} ${user.lastName}
🏢 Organisation: ${user.organization.name}
🔑 Rôle: ${user.role}
✅ Actif: ${user.isActive ? 'Oui' : 'Non'}
🏢 Org Active: ${user.organization.isActive ? 'Oui' : 'Non'}
📊 Statut Abonnement: ${user.organization.subscriptionStatus}
🔐 Mot de passe hashé: ${user.password ? 'Oui' : 'Non'}
📅 Dernière connexion: ${user.lastLogin || 'Jamais'}
      `);
    }

    // Vérifier s'il y a un utilisateur admin
    const adminUsers = users.filter(u => u.role === 'ADMIN' || u.role === 'SUPER_ADMIN');
    console.log(`🔑 ${adminUsers.length} administrateur(s) trouvé(s)`);

    // Vérifier les organisations actives
    const activeOrgs = users.filter(u => u.organization.isActive && u.organization.subscriptionStatus === 'active');
    console.log(`🏢 ${activeOrgs.length} utilisateur(s) avec organisation active`);

    // Vérifier les utilisateurs actifs
    const activeUsers = users.filter(u => u.isActive);
    console.log(`✅ ${activeUsers.length} utilisateur(s) actif(s)`);

    if (users.length === 0) {
      console.log('❌ Aucun utilisateur trouvé. Vous devez créer un utilisateur.');
      return;
    }

    // Test de connexion avec le premier utilisateur admin
    if (adminUsers.length > 0) {
      const testUser = adminUsers[0];
      console.log(`\n🧪 Test de connexion pour: ${testUser.email}`);
      
      // Demander le mot de passe pour test (simulation)
      console.log('💡 Pour tester la connexion, utilisez ces informations:');
      console.log(`📧 Email: ${testUser.email}`);
      console.log('🔐 Mot de passe: [le mot de passe que vous avez défini]');
      
      // Vérifier si le mot de passe est bien hashé
      if (testUser.password && testUser.password.startsWith('$2')) {
        console.log('✅ Le mot de passe semble être correctement hashé (bcrypt)');
      } else {
        console.log('❌ Le mot de passe ne semble pas être hashé correctement');
      }
    }

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Fonction pour créer un utilisateur de test si nécessaire
async function createTestUser() {
  try {
    console.log('🔧 Création d\'un utilisateur de test...');

    // Vérifier s'il y a déjà une organisation
    let organization = await prisma.organization.findFirst();
    
    if (!organization) {
      // Créer une organisation de test
      organization = await prisma.organization.create({
        data: {
          name: 'Hôpital de Test',
          slug: 'hopital-test',
          email: '<EMAIL>',
          phone: '+223 70 00 00 00',
          address: 'Bamako, Mali',
          subscriptionPlan: 'premium',
          subscriptionStatus: 'active',
          isActive: true,
        },
      });
      console.log('🏢 Organisation de test créée');
    }

    // Vérifier s'il y a déjà un admin
    const existingAdmin = await prisma.user.findFirst({
      where: {
        organizationId: organization.id,
        role: 'ADMIN',
      },
    });

    if (!existingAdmin) {
      // Créer un utilisateur admin de test
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Admin',
          lastName: 'Test',
          role: 'ADMIN',
          organizationId: organization.id,
          isActive: true,
        },
      });

      console.log('👤 Utilisateur admin de test créé:');
      console.log(`📧 Email: <EMAIL>`);
      console.log(`🔐 Mot de passe: admin123`);
      console.log(`🔑 Rôle: ADMIN`);
    } else {
      console.log('👤 Un utilisateur admin existe déjà');
    }

  } catch (error) {
    console.error('❌ Erreur lors de la création:', error);
  }
}

// Exécuter selon l'argument
const action = process.argv[2];

if (action === 'create') {
  createTestUser()
    .then(() => {
      console.log('✅ Création terminée');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
} else {
  checkUsers()
    .then(() => {
      console.log('✅ Vérification terminée');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}
