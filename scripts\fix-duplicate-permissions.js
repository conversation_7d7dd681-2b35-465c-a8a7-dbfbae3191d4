const fs = require('fs');
const path = require('path');

// Fichiers traités par le script automatique qui peuvent avoir des doublons
const FILES_TO_FIX = [
  'appointments.ts',
  'pharmacy.ts',
  'laboratory.ts',
  'hospitalization.ts',
  'reports.ts',
  'analytics.ts'
];

function cleanDuplicatePermissions(content, fileName) {
  console.log(`🔧 Nettoyage de ${fileName}...`);
  
  // Compter les vérifications de permissions avant nettoyage
  const beforeCount = (content.match(/const\s+can\w+\s*=\s*await\s+checkPermission\(/g) || []).length;
  
  // Nettoyer les fonctions une par une
  let cleanedContent = content;
  
  // Pattern pour trouver les fonctions avec leurs permissions
  const functionRegex = /export\s+async\s+function\s+(\w+)\s*\([^)]*\)\s*\{([\s\S]*?)(?=export\s+async\s+function|\n\n\/\/|$)/g;
  
  let match;
  const functions = [];
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1];
    const functionBody = match[2];
    
    // Vérifier s'il y a des permissions dupliquées dans cette fonction
    const permissionChecks = functionBody.match(/\/\/ 🔐 VÉRIFICATION DES PERMISSIONS[\s\S]*?throw new Error\([^)]+\);\s*\}/g) || [];
    
    if (permissionChecks.length > 1) {
      console.log(`   ⚠️  ${functionName}: ${permissionChecks.length} vérifications trouvées`);
      
      // Garder seulement la première vérification de permission
      const firstPermissionCheck = permissionChecks[0];
      
      // Supprimer toutes les vérifications de permissions
      let cleanedFunctionBody = functionBody.replace(/\/\/ 🔐 VÉRIFICATION DES PERMISSIONS[\s\S]*?throw new Error\([^)]+\);\s*\}/g, '');
      
      // Réinsérer la première vérification après la validation de session
      const sessionCheckPattern = /if\s*\(\s*!session\?\.\s*user\?\.\s*organizationId\s*\)\s*\{[\s\S]*?\}/;
      const sessionMatch = cleanedFunctionBody.match(sessionCheckPattern);
      
      if (sessionMatch) {
        const insertIndex = cleanedFunctionBody.indexOf(sessionMatch[0]) + sessionMatch[0].length;
        cleanedFunctionBody = cleanedFunctionBody.slice(0, insertIndex) + '\n\n    ' + firstPermissionCheck + cleanedFunctionBody.slice(insertIndex);
      }
      
      // Remplacer dans le contenu global
      const fullFunctionPattern = new RegExp(`export\\s+async\\s+function\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{[\\s\\S]*?(?=export\\s+async\\s+function|\\n\\n\\/\\/|$)`, 'g');
      cleanedContent = cleanedContent.replace(fullFunctionPattern, `export async function ${functionName}${match[0].match(/\([^)]*\)/)[0]} {${cleanedFunctionBody}`);
    }
  }
  
  // Compter après nettoyage
  const afterCount = (cleanedContent.match(/const\s+can\w+\s*=\s*await\s+checkPermission\(/g) || []).length;
  
  console.log(`   ✅ ${beforeCount} → ${afterCount} vérifications de permissions`);
  
  return cleanedContent;
}

function fixSpecificPatterns(content, fileName) {
  console.log(`🔧 Correction des patterns spécifiques pour ${fileName}...`);
  
  // Supprimer les blocs de permissions complètement dupliqués
  content = content.replace(/(\s*\/\/ 🔐 VÉRIFICATION DES PERMISSIONS[\s\S]*?throw new Error\([^)]+\);\s*\})\s*\1+/g, '$1');
  
  // Corriger les variables avec des noms similaires mais différents
  content = content.replace(/const\s+canRead\s*=[\s\S]*?const\s+canRead\s*=/g, (match) => {
    const parts = match.split('const canRead =');
    return 'const canRead =' + parts[1] + 'const canRead2 =';
  });
  
  // Corriger les variables canUpdate dupliquées
  content = content.replace(/const\s+canUpdate\s*=[\s\S]*?const\s+canUpdate\s*=/g, (match) => {
    const parts = match.split('const canUpdate =');
    return 'const canUpdate =' + parts[1] + 'const canUpdate2 =';
  });
  
  // Corriger les variables canCreate dupliquées
  content = content.replace(/const\s+canCreate\s*=[\s\S]*?const\s+canCreate\s*=/g, (match) => {
    const parts = match.split('const canCreate =');
    return 'const canCreate =' + parts[1] + 'const canCreate2 =';
  });
  
  return content;
}

function cleanFile(fileName) {
  const filePath = path.join(__dirname, '..', 'src', 'lib', 'actions', fileName);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⏭️  ${fileName} - Fichier non trouvé`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Vérifier s'il y a des doublons
  const duplicateVarRegex = /const\s+(can\w+)\s*=.*?\n[\s\S]*?const\s+\1\s*=/;
  const hasDuplicates = duplicateVarRegex.test(content);
  
  if (!hasDuplicates) {
    console.log(`✅ ${fileName} - Déjà propre`);
    return;
  }
  
  console.log(`🔧 ${fileName} - Nettoyage nécessaire`);
  
  // Nettoyer les permissions dupliquées
  content = cleanDuplicatePermissions(content, fileName);
  
  // Appliquer des corrections spécifiques
  content = fixSpecificPatterns(content, fileName);
  
  // Vérification finale
  const stillHasDuplicates = duplicateVarRegex.test(content);
  
  if (stillHasDuplicates) {
    console.log(`⚠️  ${fileName} - Il reste encore des doublons, nettoyage manuel nécessaire`);
    
    // Approche plus agressive : supprimer tous les blocs dupliqués
    const lines = content.split('\n');
    const cleanedLines = [];
    let skipUntilNextFunction = false;
    let permissionBlockCount = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.includes('// 🔐 VÉRIFICATION DES PERMISSIONS')) {
        permissionBlockCount++;
        if (permissionBlockCount > 1) {
          skipUntilNextFunction = true;
          continue;
        }
      }
      
      if (skipUntilNextFunction) {
        if (line.includes('export async function') || line.includes('// =====') || line.trim() === '') {
          skipUntilNextFunction = false;
          permissionBlockCount = 0;
        } else {
          continue;
        }
      }
      
      cleanedLines.push(line);
    }
    
    content = cleanedLines.join('\n');
  }
  
  // Sauvegarder le fichier nettoyé
  fs.writeFileSync(filePath, content, 'utf8');
  
  // Vérification finale
  const finalCheck = duplicateVarRegex.test(content);
  if (finalCheck) {
    console.log(`❌ ${fileName} - Échec du nettoyage automatique`);
  } else {
    console.log(`✅ ${fileName} - Nettoyé avec succès`);
  }
}

function main() {
  console.log('🧹 Nettoyage des permissions dupliquées...\n');
  
  for (const fileName of FILES_TO_FIX) {
    cleanFile(fileName);
    console.log('');
  }
  
  console.log('🎉 Nettoyage terminé !');
  console.log('\n🔍 Vérification finale...');
  
  // Vérification finale de tous les fichiers
  let allClean = true;
  for (const fileName of FILES_TO_FIX) {
    const filePath = path.join(__dirname, '..', 'src', 'lib', 'actions', fileName);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const duplicateVarRegex = /const\s+(can\w+)\s*=.*?\n[\s\S]*?const\s+\1\s*=/;
      if (duplicateVarRegex.test(content)) {
        console.log(`❌ ${fileName} - Contient encore des doublons`);
        allClean = false;
      } else {
        console.log(`✅ ${fileName} - Propre`);
      }
    }
  }
  
  if (allClean) {
    console.log('\n🎉 Tous les fichiers sont propres !');
  } else {
    console.log('\n⚠️  Certains fichiers nécessitent un nettoyage manuel');
  }
}

if (require.main === module) {
  main();
}
