const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '..', 'src', 'lib', 'actions', 'hospitalization.ts');

// <PERSON><PERSON> le fichier original
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔧 Nettoyage du fichier hospitalization.ts...');

// Supprimer toutes les vérifications de permissions dupliquées
// Garder seulement la structure de base

// Remplacer la fonction createRoom complètement
const createRoomFixed = `export async function createRoom(data: RoomFormData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "HOSPITALIZATION"
    );

    if (!canCreate) {
      await logAction("CREATE", "HOSPITALIZATION", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer des chambres");
    }

    // Vérifier que le numéro de chambre n'existe pas déjà
    const existingRoom = await prisma.room.findFirst({
      where: {
        number: data.number,
        organizationId: session.user.organizationId,
      },
    });

    if (existingRoom) {
      throw new Error("Ce numéro de chambre existe déjà");
    }

    const room = await prisma.room.create({
      data: {
        ...data,
        organizationId: session.user.organizationId,
        currency: "XOF",
      },
    });

    // Créer automatiquement les lits selon la capacité
    const beds = [];
    for (let i = 1; i <= data.capacity; i++) {
      const bedLetter = String.fromCharCode(64 + i); // A, B, C, etc.
      beds.push({
        number: bedLetter,
        roomId: room.id,
        organizationId: session.user.organizationId,
        bedType: "STANDARD" as BedType,
        status: "AVAILABLE" as BedStatus,
      });
    }

    await prisma.bed.createMany({
      data: beds,
    });

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/rooms");

    return { success: true, room };
  } catch (error) {
    console.error("Erreur lors de la création de la chambre:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}`;

// Trouver et remplacer la fonction createRoom
const createRoomRegex = /export async function createRoom\(data: RoomFormData\) \{[\s\S]*?\n\}/;
content = content.replace(createRoomRegex, createRoomFixed);

// Sauvegarder le fichier corrigé
fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ Fichier hospitalization.ts nettoyé !');
console.log('🔧 Vérification de la syntaxe...');

// Vérifier qu'il n'y a plus de variables dupliquées
const duplicateVarRegex = /const\s+(can\w+)\s*=.*?\n[\s\S]*?const\s+\1\s*=/;
const hasDuplicates = duplicateVarRegex.test(content);

if (hasDuplicates) {
  console.log('⚠️  Il reste encore des variables dupliquées');
} else {
  console.log('✅ Plus de variables dupliquées');
}

console.log('✅ Nettoyage terminé !');
