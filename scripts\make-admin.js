const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function makeAdmin() {
  try {
    console.log('🔧 Promotion du premier utilisateur en ADMIN...');

    // Trouver le premier utilisateur
    const user = await prisma.user.findFirst({
      orderBy: { createdAt: 'asc' },
    });

    if (!user) {
      console.log('❌ Aucun utilisateur trouvé');
      return;
    }

    console.log(`👤 Utilisateur trouvé: ${user.firstName} ${user.lastName} (${user.email})`);
    console.log(`🔄 Rôle actuel: ${user.role}`);

    // Mettre à jour le rôle
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { role: 'ADMIN' },
    });

    console.log(`✅ Rôle mis à jour: ${updatedUser.role}`);
    console.log(`🎉 ${user.firstName} ${user.lastName} est maintenant ADMIN !`);

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

makeAdmin();
