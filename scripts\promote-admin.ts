import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function promoteToAdmin(email?: string) {
  try {
    console.log('🔧 Promotion d\'un utilisateur en ADMIN...');

    // Si pas d'email fourni, prendre le premier utilisateur trouvé
    let targetEmail = email;
    
    if (!targetEmail) {
      const firstUser = await prisma.user.findFirst({
        orderBy: { createdAt: 'asc' },
      });
      
      if (!firstUser) {
        throw new Error('Aucun utilisateur trouvé');
      }
      
      targetEmail = firstUser.email;
      console.log(`📧 Aucun email fourni, utilisation du premier utilisateur: ${targetEmail}`);
    }

    // Trouver l'utilisateur
    const user = await prisma.user.findUnique({
      where: { email: targetEmail },
      include: {
        organization: true,
      },
    });

    if (!user) {
      throw new Error(`Utilisateur non trouvé: ${targetEmail}`);
    }

    console.log(`👤 Utilisateur trouvé: ${user.firstName} ${user.lastName}`);
    console.log(`🏥 Organisation: ${user.organization.name}`);
    console.log(`🔄 Rôle actuel: ${user.role}`);

    // Mettre à jour le rôle en ADMIN
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { role: 'ADMIN' },
    });

    console.log(`✅ Rôle mis à jour: ${updatedUser.role}`);

    // Vérifier les permissions ADMIN
    const adminPermissions = await prisma.rolePermission.findMany({
      where: {
        role: 'ADMIN',
      },
      include: {
        permission: true,
      },
    });

    console.log(`\n🔐 Permissions ADMIN disponibles (${adminPermissions.length}):`);
    
    const rolesPermissions = adminPermissions.filter(rp => 
      rp.permission.resource === 'ROLES' && rp.isGranted
    );

    console.log(`\n📋 Permissions sur ROLES:`);
    rolesPermissions.forEach(rp => {
      console.log(`  ✅ ${rp.permission.action}: ${rp.permission.description}`);
    });

    if (rolesPermissions.length === 0) {
      console.log(`  ❌ Aucune permission sur ROLES trouvée`);
      
      // Créer les permissions manquantes pour ADMIN sur ROLES
      console.log(`\n🔧 Création des permissions ROLES pour ADMIN...`);
      
      const rolesPermissionsToCreate = await prisma.permission.findMany({
        where: {
          resource: 'ROLES',
          isActive: true,
        },
      });

      for (const permission of rolesPermissionsToCreate) {
        await prisma.rolePermission.upsert({
          where: {
            role_permissionId: {
              role: 'ADMIN',
              permissionId: permission.id,
            },
          },
          update: {
            isGranted: true,
          },
          create: {
            role: 'ADMIN',
            permissionId: permission.id,
            isGranted: true,
          },
        });
        
        console.log(`  ✅ Permission créée: ${permission.action} sur ROLES`);
      }
    }

    console.log(`\n🎉 ${user.firstName} ${user.lastName} est maintenant ADMIN avec toutes les permissions !`);
    console.log(`📧 Email: ${user.email}`);
    console.log(`🔑 Peut maintenant accéder à /dashboard/settings/permissions`);

  } catch (error) {
    console.error('❌ Error promoting user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Récupérer l'email depuis les arguments de ligne de commande
const email = process.argv[2];

// Exécuter le script si appelé directement
if (require.main === module) {
  promoteToAdmin(email)
    .then(() => {
      console.log('\n🎉 Promotion completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Promotion failed:', error);
      process.exit(1);
    });
}

export default promoteToAdmin;
