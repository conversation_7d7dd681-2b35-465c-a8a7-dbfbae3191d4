import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedAttendanceData() {
  try {
    console.log('🌱 Début du seeding des données de pointage...');

    // Récupérer l'organisation et les employés existants
    const organization = await prisma.organization.findFirst();
    
    if (!organization) {
      console.log('❌ Aucune organisation trouvée.');
      return;
    }

    const employees = await prisma.employee.findMany({
      where: { organizationId: organization.id },
      take: 5, // Prendre les 5 premiers employés
    });

    if (employees.length === 0) {
      console.log('❌ Aucun employé trouvé.');
      return;
    }

    console.log(`📋 Organisation: ${organization.name}`);
    console.log(`👥 ${employees.length} employés trouvés`);

    // Créer des données de pointage pour les 7 derniers jours
    const attendanceRecords = [];
    
    for (let dayOffset = 6; dayOffset >= 0; dayOffset--) {
      const date = new Date();
      date.setDate(date.getDate() - dayOffset);
      date.setHours(0, 0, 0, 0); // Début de journée
      
      console.log(`📅 Création des pointages pour le ${date.toLocaleDateString('fr-FR')}`);
      
      for (let i = 0; i < employees.length; i++) {
        const employee = employees[i];
        
        // Simuler différents scénarios de pointage
        const scenario = (dayOffset + i) % 4;
        
        let checkIn: Date | undefined;
        let checkOut: Date | undefined;
        let breakStart: Date | undefined;
        let breakEnd: Date | undefined;
        let status: 'PRESENT' | 'ABSENT' | 'LATE' = 'PRESENT';
        let hoursWorked: number | undefined;
        let overtimeHours: number | undefined;
        
        switch (scenario) {
          case 0: // Journée normale
            checkIn = new Date(date);
            checkIn.setHours(8, 0, 0, 0); // 8h00
            
            breakStart = new Date(date);
            breakStart.setHours(12, 0, 0, 0); // 12h00
            
            breakEnd = new Date(date);
            breakEnd.setHours(13, 0, 0, 0); // 13h00
            
            checkOut = new Date(date);
            checkOut.setHours(17, 0, 0, 0); // 17h00
            
            hoursWorked = 8; // 8h travaillées (9h - 1h de pause)
            status = 'PRESENT';
            break;
            
          case 1: // Retard
            checkIn = new Date(date);
            checkIn.setHours(8, 30, 0, 0); // 8h30 (retard)
            
            breakStart = new Date(date);
            breakStart.setHours(12, 0, 0, 0);
            
            breakEnd = new Date(date);
            breakEnd.setHours(13, 0, 0, 0);
            
            checkOut = new Date(date);
            checkOut.setHours(17, 30, 0, 0); // Compense le retard
            
            hoursWorked = 8;
            status = 'LATE';
            break;
            
          case 2: // Heures supplémentaires
            checkIn = new Date(date);
            checkIn.setHours(7, 30, 0, 0); // Plus tôt
            
            breakStart = new Date(date);
            breakStart.setHours(12, 0, 0, 0);
            
            breakEnd = new Date(date);
            breakEnd.setHours(13, 0, 0, 0);
            
            checkOut = new Date(date);
            checkOut.setHours(18, 30, 0, 0); // Plus tard
            
            hoursWorked = 10; // 10h travaillées
            overtimeHours = 2; // 2h supplémentaires
            status = 'PRESENT';
            break;
            
          case 3: // Absent
            status = 'ABSENT';
            break;
        }
        
        try {
          const attendance = await prisma.attendance.create({
            data: {
              employeeId: employee.id,
              organizationId: organization.id,
              date: date,
              checkIn,
              checkOut,
              breakStart,
              breakEnd,
              hoursWorked,
              overtimeHours,
              status,
              notes: scenario === 1 ? 'Retard justifié' : 
                     scenario === 2 ? 'Heures supplémentaires autorisées' :
                     scenario === 3 ? 'Absence non justifiée' : undefined,
            },
          });
          
          attendanceRecords.push(attendance);
        } catch (error) {
          // Ignorer les doublons (contrainte unique sur employeeId + date)
          console.log(`⚠️ Pointage déjà existant pour ${employee.firstName} ${employee.lastName} le ${date.toLocaleDateString('fr-FR')}`);
        }
      }
    }

    console.log(`✅ ${attendanceRecords.length} enregistrements de pointage créés`);

    // Statistiques
    const stats = {
      total: attendanceRecords.length,
      present: attendanceRecords.filter(r => r.status === 'PRESENT').length,
      late: attendanceRecords.filter(r => r.status === 'LATE').length,
      absent: attendanceRecords.filter(r => r.status === 'ABSENT').length,
      overtime: attendanceRecords.filter(r => r.overtimeHours && r.overtimeHours > 0).length,
    };

    console.log('🎉 Seeding des données de pointage terminé avec succès !');
    console.log(`
📊 Résumé:
- ${stats.total} pointages créés
- ${stats.present} présences normales
- ${stats.late} retards
- ${stats.absent} absences
- ${stats.overtime} avec heures supplémentaires
    `);

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedAttendanceData()
    .then(() => {
      console.log('✅ Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

export { seedAttendanceData };
