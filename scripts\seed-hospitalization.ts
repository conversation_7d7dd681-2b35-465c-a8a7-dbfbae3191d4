import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seedHospitalization() {
  try {
    console.log("🏥 Seeding hospitalization data...");

    // Récupérer l'organisation existante
    const organization = await prisma.organization.findFirst({
      where: { slug: "hopital-mali-demo" },
    });

    if (!organization) {
      throw new Error("Organisation non trouvée");
    }

    // Récupérer quelques patients et médecins existants
    const patients = await prisma.patient.findMany({
      where: { organizationId: organization.id },
      take: 10,
    });

    const doctors = await prisma.user.findMany({
      where: {
        organizationId: organization.id,
        role: "DOCTOR",
      },
      take: 5,
    });

    if (patients.length === 0 || doctors.length === 0) {
      console.log(
        "⚠️ Pas assez de patients ou médecins. Exécutez d'abord le seed principal."
      );
      return;
    }

    console.log(`👥 ${patients.length} patients trouvés`);
    console.log(`👨‍⚕️ ${doctors.length} médecins trouvés`);

    // 1. Créer des chambres
    console.log("🏠 Création des chambres...");

    const roomsData = [
      // Étage 1 - Chambres standard
      {
        number: "101",
        name: "Chambre Standard 101",
        floor: 1,
        type: "STANDARD",
        capacity: 2,
        rate: 25000,
      },
      {
        number: "102",
        name: "Chambre Standard 102",
        floor: 1,
        type: "STANDARD",
        capacity: 2,
        rate: 25000,
      },
      {
        number: "103",
        name: "Chambre Standard 103",
        floor: 1,
        type: "STANDARD",
        capacity: 1,
        rate: 30000,
      },
      {
        number: "104",
        name: "Chambre VIP 104",
        floor: 1,
        type: "VIP",
        capacity: 1,
        rate: 75000,
      },

      // Étage 2 - Soins intensifs
      {
        number: "201",
        name: "Soins Intensifs 201",
        floor: 2,
        type: "ICU",
        capacity: 1,
        rate: 150000,
      },
      {
        number: "202",
        name: "Soins Intensifs 202",
        floor: 2,
        type: "ICU",
        capacity: 1,
        rate: 150000,
      },
      {
        number: "203",
        name: "Chambre Standard 203",
        floor: 2,
        type: "STANDARD",
        capacity: 2,
        rate: 25000,
      },
      {
        number: "204",
        name: "Chambre Standard 204",
        floor: 2,
        type: "STANDARD",
        capacity: 2,
        rate: 25000,
      },

      // Étage 3 - Maternité et pédiatrie
      {
        number: "301",
        name: "Maternité 301",
        floor: 3,
        type: "MATERNITY",
        capacity: 1,
        rate: 50000,
      },
      {
        number: "302",
        name: "Maternité 302",
        floor: 3,
        type: "MATERNITY",
        capacity: 1,
        rate: 50000,
      },
      {
        number: "303",
        name: "Pédiatrie 303",
        floor: 3,
        type: "PEDIATRIC",
        capacity: 2,
        rate: 35000,
      },
      {
        number: "304",
        name: "Pédiatrie 304",
        floor: 3,
        type: "PEDIATRIC",
        capacity: 2,
        rate: 35000,
      },
    ];

    const createdRooms = [];
    for (const roomData of roomsData) {
      const room = await prisma.room.upsert({
        where: {
          organizationId_number: {
            organizationId: organization.id,
            number: roomData.number,
          },
        },
        update: {},
        create: {
          number: roomData.number,
          name: roomData.name,
          floor: roomData.floor,
          roomType: roomData.type as any,
          capacity: roomData.capacity,
          dailyRate: roomData.rate,
          hasPrivateBathroom:
            roomData.type === "VIP" || roomData.type === "ICU",
          hasAirConditioning:
            roomData.type === "VIP" ||
            roomData.type === "ICU" ||
            roomData.type === "MATERNITY",
          hasTV: roomData.type !== "ICU",
          hasWifi: true,
          organizationId: organization.id,
        },
      });
      createdRooms.push(room);
    }

    // 2. Créer des lits pour chaque chambre
    console.log("🛏️ Création des lits...");

    const createdBeds = [];
    for (const room of createdRooms) {
      for (let i = 1; i <= room.capacity; i++) {
        const bedLetter = String.fromCharCode(64 + i); // A, B, C, etc.
        const bed = await prisma.bed.upsert({
          where: {
            roomId_number: {
              roomId: room.id,
              number: bedLetter,
            },
          },
          update: {},
          create: {
            number: bedLetter,
            roomId: room.id,
            bedType:
              room.roomType === "ICU"
                ? "ICU"
                : room.roomType === "PEDIATRIC"
                ? "PEDIATRIC"
                : room.roomType === "MATERNITY"
                ? "MATERNITY"
                : "STANDARD",
            isElectric: room.roomType === "ICU" || room.roomType === "VIP",
            hasOxygen: room.roomType === "ICU",
            hasMonitoring: room.roomType === "ICU",
            status: "AVAILABLE",
            organizationId: organization.id,
          },
        });
        createdBeds.push(bed);
      }
    }

    // 3. Créer des admissions
    console.log("📋 Création des admissions...");

    const admissionsData = [
      {
        patientIndex: 0,
        doctorIndex: 0,
        roomNumber: "101",
        bedLetter: "A",
        reason: "Chirurgie programmée - Appendicectomie",
        diagnosis: "Appendicite aiguë",
        status: "ADMITTED",
        daysAgo: 2,
      },
      {
        patientIndex: 1,
        doctorIndex: 1,
        roomNumber: "201",
        bedLetter: "A",
        reason: "Surveillance post-opératoire",
        diagnosis: "Post-chirurgie cardiaque",
        status: "ADMITTED",
        daysAgo: 5,
      },
      {
        patientIndex: 2,
        doctorIndex: 2,
        roomNumber: "301",
        bedLetter: "A",
        reason: "Accouchement",
        diagnosis: "Grossesse à terme",
        status: "ADMITTED",
        daysAgo: 1,
      },
      {
        patientIndex: 3,
        doctorIndex: 0,
        roomNumber: "102",
        bedLetter: "B",
        reason: "Observation médicale",
        diagnosis: "Pneumonie",
        status: "ADMITTED",
        daysAgo: 3,
      },
      {
        patientIndex: 4,
        doctorIndex: 1,
        roomNumber: "104",
        bedLetter: "A",
        reason: "Traitement VIP",
        diagnosis: "Diabète décompensé",
        status: "ADMITTED",
        daysAgo: 1,
      },
      // Quelques admissions sorties
      {
        patientIndex: 5,
        doctorIndex: 2,
        roomNumber: "103",
        bedLetter: "A",
        reason: "Chirurgie mineure",
        diagnosis: "Hernie inguinale",
        status: "DISCHARGED",
        daysAgo: 7,
        dischargeDaysAgo: 2,
      },
      {
        patientIndex: 6,
        doctorIndex: 0,
        roomNumber: "302",
        bedLetter: "A",
        reason: "Accouchement",
        diagnosis: "Accouchement normal",
        status: "DISCHARGED",
        daysAgo: 10,
        dischargeDaysAgo: 7,
      },
    ];

    for (const admissionData of admissionsData) {
      const room = createdRooms.find(
        (r) => r.number === admissionData.roomNumber
      );
      if (!room) {
        console.log(`⚠️ Chambre ${admissionData.roomNumber} non trouvée`);
        continue;
      }

      const bed = createdBeds.find(
        (b) => b.roomId === room.id && b.number === admissionData.bedLetter
      );

      if (!bed) {
        console.log(
          `⚠️ Lit ${admissionData.bedLetter} non trouvé dans la chambre ${admissionData.roomNumber}`
        );
        continue;
      }

      const admissionDate = new Date();
      admissionDate.setDate(admissionDate.getDate() - admissionData.daysAgo);

      let actualDischargeDate = null;
      if (
        admissionData.status === "DISCHARGED" &&
        admissionData.dischargeDaysAgo
      ) {
        actualDischargeDate = new Date();
        actualDischargeDate.setDate(
          actualDischargeDate.getDate() - admissionData.dischargeDaysAgo
        );
      }

      // Générer le numéro d'admission
      const dateStr = admissionDate
        .toISOString()
        .slice(0, 10)
        .replace(/-/g, "");
      const sequence = Math.floor(Math.random() * 999) + 1;
      const admissionNumber = `ADM-${dateStr}-${sequence
        .toString()
        .padStart(3, "0")}`;

      // Vérifier que les indices sont valides
      if (
        admissionData.patientIndex >= patients.length ||
        admissionData.doctorIndex >= doctors.length
      ) {
        console.log(
          `⚠️ Index invalide pour l'admission ${admissionData.roomNumber}`
        );
        continue;
      }

      await prisma.admission.create({
        data: {
          admissionNumber,
          patientId: patients[admissionData.patientIndex].id,
          doctorId: doctors[admissionData.doctorIndex].id,
          roomId: room.id,
          bedId: bed.id,
          admissionDate,
          actualDischargeDate,
          reason: admissionData.reason,
          diagnosis: admissionData.diagnosis,
          status: admissionData.status as any,
          emergencyContactName: "Contact Famille",
          emergencyContactPhone: "+223 70 00 00 00",
          emergencyContactRelation: "Époux/Épouse",
          admissionNotes: `Admission pour ${admissionData.reason.toLowerCase()}`,
          organizationId: organization.id,
        },
      });

      // Mettre à jour le statut du lit
      if (admissionData.status === "ADMITTED") {
        await prisma.bed.update({
          where: { id: bed.id },
          data: { status: "OCCUPIED" },
        });
      }
    }

    console.log("✅ Hospitalization data seeded successfully!");
    console.log(`📊 Created:`);
    console.log(`   - ${createdRooms.length} rooms`);
    console.log(`   - ${createdBeds.length} beds`);
    console.log(`   - ${admissionsData.length} admissions`);
  } catch (error) {
    console.error("❌ Error seeding hospitalization data:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedHospitalization()
    .then(() => {
      console.log("🎉 Hospitalization seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Hospitalization seeding failed:", error);
      process.exit(1);
    });
}

export default seedHospitalization;
