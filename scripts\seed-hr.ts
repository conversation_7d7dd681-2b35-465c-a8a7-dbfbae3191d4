import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedHRData() {
  try {
    console.log('🌱 Début du seeding des données RH...');

    // Récupérer l'organisation existante (supposons qu'il y en a une)
    const organization = await prisma.organization.findFirst();
    
    if (!organization) {
      console.log('❌ Aucune organisation trouvée. Veuillez d\'abord créer une organisation.');
      return;
    }

    console.log(`📋 Organisation trouvée: ${organization.name}`);

    // 1. Créer des départements
    console.log('🏢 Création des départements...');
    
    const departments = await Promise.all([
      prisma.department.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'MED' } },
        update: {},
        create: {
          name: 'Médecine générale',
          code: 'MED',
          description: 'Consultations de médecine générale et soins primaires',
          organizationId: organization.id,
        },
      }),
      prisma.department.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'LAB' } },
        update: {},
        create: {
          name: 'Laboratoire',
          code: 'LAB',
          description: 'Analyses médicales et examens de laboratoire',
          organizationId: organization.id,
        },
      }),
      prisma.department.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'PHAR' } },
        update: {},
        create: {
          name: 'Pharmacie',
          code: 'PHAR',
          description: 'Gestion des médicaments et dispensation',
          organizationId: organization.id,
        },
      }),
      prisma.department.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'ADM' } },
        update: {},
        create: {
          name: 'Administration',
          code: 'ADM',
          description: 'Gestion administrative et financière',
          organizationId: organization.id,
        },
      }),
      prisma.department.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'URG' } },
        update: {},
        create: {
          name: 'Urgences',
          code: 'URG',
          description: 'Soins d\'urgence et réanimation',
          organizationId: organization.id,
        },
      }),
    ]);

    console.log(`✅ ${departments.length} départements créés`);

    // 2. Créer des postes
    console.log('💼 Création des postes...');
    
    const positions = await Promise.all([
      // Médecine générale
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'MED_GEN' } },
        update: {},
        create: {
          title: 'Médecin généraliste',
          code: 'MED_GEN',
          description: 'Médecin spécialisé en médecine générale',
          departmentId: departments[0].id,
          baseSalary: 800000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'INF_MED' } },
        update: {},
        create: {
          title: 'Infirmier(ère)',
          code: 'INF_MED',
          description: 'Infirmier en médecine générale',
          departmentId: departments[0].id,
          baseSalary: 350000,
          organizationId: organization.id,
        },
      }),
      // Laboratoire
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'TECH_LAB' } },
        update: {},
        create: {
          title: 'Technicien de laboratoire',
          code: 'TECH_LAB',
          description: 'Technicien spécialisé en analyses médicales',
          departmentId: departments[1].id,
          baseSalary: 400000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'BIOL' } },
        update: {},
        create: {
          title: 'Biologiste',
          code: 'BIOL',
          description: 'Biologiste médical responsable des analyses',
          departmentId: departments[1].id,
          baseSalary: 700000,
          organizationId: organization.id,
        },
      }),
      // Pharmacie
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'PHARM' } },
        update: {},
        create: {
          title: 'Pharmacien',
          code: 'PHARM',
          description: 'Pharmacien responsable de la dispensation',
          departmentId: departments[2].id,
          baseSalary: 600000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'PREP_PHARM' } },
        update: {},
        create: {
          title: 'Préparateur en pharmacie',
          code: 'PREP_PHARM',
          description: 'Préparateur en pharmacie',
          departmentId: departments[2].id,
          baseSalary: 300000,
          organizationId: organization.id,
        },
      }),
      // Administration
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'DIR_ADM' } },
        update: {},
        create: {
          title: 'Directeur administratif',
          code: 'DIR_ADM',
          description: 'Directeur des services administratifs',
          departmentId: departments[3].id,
          baseSalary: 900000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'SEC' } },
        update: {},
        create: {
          title: 'Secrétaire',
          code: 'SEC',
          description: 'Secrétaire administrative',
          departmentId: departments[3].id,
          baseSalary: 250000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'COMPT' } },
        update: {},
        create: {
          title: 'Comptable',
          code: 'COMPT',
          description: 'Comptable de l\'établissement',
          departmentId: departments[3].id,
          baseSalary: 450000,
          organizationId: organization.id,
        },
      }),
      // Urgences
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'MED_URG' } },
        update: {},
        create: {
          title: 'Médecin urgentiste',
          code: 'MED_URG',
          description: 'Médecin spécialisé en médecine d\'urgence',
          departmentId: departments[4].id,
          baseSalary: 850000,
          organizationId: organization.id,
        },
      }),
      prisma.position.upsert({
        where: { organizationId_code: { organizationId: organization.id, code: 'INF_URG' } },
        update: {},
        create: {
          title: 'Infirmier(ère) urgentiste',
          code: 'INF_URG',
          description: 'Infirmier spécialisé en soins d\'urgence',
          departmentId: departments[4].id,
          baseSalary: 400000,
          organizationId: organization.id,
        },
      }),
    ]);

    console.log(`✅ ${positions.length} postes créés`);

    // 3. Créer des employés
    console.log('👥 Création des employés...');
    
    const currentYear = new Date().getFullYear();
    
    const employees = await Promise.all([
      // Médecins
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0001` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0001`,
          firstName: 'Amadou',
          lastName: 'Traoré',
          email: '<EMAIL>',
          phone: '+223 70 12 34 56',
          gender: 'MALE',
          dateOfBirth: new Date('1980-05-15'),
          address: 'Quartier Hippodrome, Bamako',
          departmentId: departments[0].id,
          positionId: positions[0].id,
          hireDate: new Date('2020-01-15'),
          currentSalary: 800000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0002` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0002`,
          firstName: 'Fatoumata',
          lastName: 'Diallo',
          email: '<EMAIL>',
          phone: '+223 70 23 45 67',
          gender: 'FEMALE',
          dateOfBirth: new Date('1985-08-22'),
          address: 'Quartier ACI 2000, Bamako',
          departmentId: departments[4].id,
          positionId: positions[9].id,
          hireDate: new Date('2021-03-10'),
          currentSalary: 850000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      // Infirmiers
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0003` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0003`,
          firstName: 'Mariam',
          lastName: 'Koné',
          email: '<EMAIL>',
          phone: '+223 70 34 56 78',
          gender: 'FEMALE',
          dateOfBirth: new Date('1990-12-03'),
          address: 'Quartier Magnambougou, Bamako',
          departmentId: departments[0].id,
          positionId: positions[1].id,
          hireDate: new Date('2022-06-01'),
          currentSalary: 350000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0004` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0004`,
          firstName: 'Ibrahim',
          lastName: 'Sangaré',
          email: '<EMAIL>',
          phone: '+223 70 45 67 89',
          gender: 'MALE',
          dateOfBirth: new Date('1988-04-18'),
          address: 'Quartier Lafiabougou, Bamako',
          departmentId: departments[4].id,
          positionId: positions[10].id,
          hireDate: new Date('2021-09-15'),
          currentSalary: 400000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      // Techniciens et autres
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0005` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0005`,
          firstName: 'Seydou',
          lastName: 'Coulibaly',
          email: '<EMAIL>',
          phone: '+223 70 56 78 90',
          gender: 'MALE',
          dateOfBirth: new Date('1992-07-25'),
          address: 'Quartier Kalaban Coura, Bamako',
          departmentId: departments[1].id,
          positionId: positions[2].id,
          hireDate: new Date('2023-01-10'),
          currentSalary: 400000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0006` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0006`,
          firstName: 'Aminata',
          lastName: 'Sidibé',
          email: '<EMAIL>',
          phone: '+223 70 67 89 01',
          gender: 'FEMALE',
          dateOfBirth: new Date('1987-11-12'),
          address: 'Quartier Badalabougou, Bamako',
          departmentId: departments[2].id,
          positionId: positions[4].id,
          hireDate: new Date('2020-08-20'),
          currentSalary: 600000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0007` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0007`,
          firstName: 'Ousmane',
          lastName: 'Dembélé',
          email: '<EMAIL>',
          phone: '+223 70 78 90 12',
          gender: 'MALE',
          dateOfBirth: new Date('1983-02-28'),
          address: 'Quartier Titibougou, Bamako',
          departmentId: departments[3].id,
          positionId: positions[6].id,
          hireDate: new Date('2019-05-01'),
          currentSalary: 900000,
          contractType: 'CDI',
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
      prisma.employee.upsert({
        where: { organizationId_employeeNumber: { organizationId: organization.id, employeeNumber: `EMP-${currentYear}-0008` } },
        update: {},
        create: {
          employeeNumber: `EMP-${currentYear}-0008`,
          firstName: 'Aïssata',
          lastName: 'Touré',
          email: '<EMAIL>',
          phone: '+223 70 89 01 23',
          gender: 'FEMALE',
          dateOfBirth: new Date('1995-09-14'),
          address: 'Quartier Sogoniko, Bamako',
          departmentId: departments[3].id,
          positionId: positions[7].id,
          hireDate: new Date('2023-04-15'),
          currentSalary: 250000,
          contractType: 'CDD',
          contractEndDate: new Date('2024-04-14'),
          workingHours: 40,
          status: 'ACTIVE',
          organizationId: organization.id,
        },
      }),
    ]);

    console.log(`✅ ${employees.length} employés créés`);

    // 4. Assigner des managers aux départements
    console.log('👨‍💼 Assignment des managers...');
    
    await Promise.all([
      prisma.department.update({
        where: { id: departments[0].id },
        data: { managerId: employees[0].id }, // Dr. Amadou Traoré pour Médecine
      }),
      prisma.department.update({
        where: { id: departments[1].id },
        data: { managerId: employees[4].id }, // Seydou Coulibaly pour Laboratoire
      }),
      prisma.department.update({
        where: { id: departments[2].id },
        data: { managerId: employees[5].id }, // Aminata Sidibé pour Pharmacie
      }),
      prisma.department.update({
        where: { id: departments[3].id },
        data: { managerId: employees[6].id }, // Ousmane Dembélé pour Administration
      }),
      prisma.department.update({
        where: { id: departments[4].id },
        data: { managerId: employees[1].id }, // Dr. Fatoumata Diallo pour Urgences
      }),
    ]);

    console.log('✅ Managers assignés aux départements');

    console.log('🎉 Seeding des données RH terminé avec succès !');
    console.log(`
📊 Résumé:
- ${departments.length} départements créés
- ${positions.length} postes créés  
- ${employees.length} employés créés
- 5 managers assignés
    `);

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedHRData()
    .then(() => {
      console.log('✅ Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

export { seedHRData };
