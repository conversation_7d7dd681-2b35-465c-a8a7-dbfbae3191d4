import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seedLeaveRequests() {
  try {
    console.log("🌱 Début du seeding des demandes de congés...");

    // Récupérer l'organisation et les employés existants
    const organization = await prisma.organization.findFirst();

    if (!organization) {
      console.log("❌ Aucune organisation trouvée.");
      return;
    }

    const employees = await prisma.employee.findMany({
      where: { organizationId: organization.id },
      take: 5, // Prendre les 5 premiers employés
    });

    if (employees.length === 0) {
      console.log("❌ Aucun employé trouvé.");
      return;
    }

    console.log(`📋 Organisation: ${organization.name}`);
    console.log(`👥 ${employees.length} employés trouvés`);

    // Types de congés disponibles
    const leaveTypes = ["ANNUAL", "SICK", "MATERNITY", "EMERGENCY", "UNPAID"];

    // Vérifier le nombre de demandes existantes pour générer des numéros uniques
    const existingCount = await prisma.leaveRequest.count({
      where: { organizationId: organization.id },
    });

    // Créer des demandes de congés variées
    const currentYear = new Date().getFullYear();

    // Créer les demandes une par une pour éviter les conflits
    const leaveRequests = [];

    // Demande 1 - Congé annuel en attente
    try {
      const leave1 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(
            existingCount + 1
          ).padStart(4, "0")}`,
          employeeId: employees[0].id,
          organizationId: organization.id,
          leaveType: "ANNUAL",
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
          totalDays: 5,
          reason: "Congés annuels pour repos familial",
          status: "PENDING",
        },
      });
      leaveRequests.push(leave1);
    } catch (error) {
      console.log("Demande 1 déjà existante, ignorée");
    }

    // Demande 2 - Congé maladie approuvé
    try {
      const leave2 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(
            existingCount + 2
          ).padStart(4, "0")}`,
          employeeId: employees[1].id,
          organizationId: organization.id,
          leaveType: "SICK",
          startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          totalDays: 3,
          reason: "Arrêt maladie prescrit par le médecin",
          status: "APPROVED",
        },
      });
      leaveRequests.push(leave2);
    } catch (error) {
      console.log("Demande 2 déjà existante, ignorée");
    }

    // Demande 3 - Congé d'urgence en attente
    try {
      const leave3 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(
            existingCount + 3
          ).padStart(4, "0")}`,
          employeeId: employees[2].id,
          organizationId: organization.id,
          leaveType: "EMERGENCY",
          startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          totalDays: 2,
          reason: "Urgence familiale - décès dans la famille",
          status: "PENDING",
        },
      });
      leaveRequests.push(leave3);
    } catch (error) {
      console.log("Demande 3 déjà existante, ignorée");
    }

    console.log(`✅ ${leaveRequests.length} demandes de congés créées`);

    // Statistiques
    const stats = {
      total: leaveRequests.length,
      pending: leaveRequests.filter((r) => r.status === "PENDING").length,
      approved: leaveRequests.filter((r) => r.status === "APPROVED").length,
      rejected: leaveRequests.filter((r) => r.status === "REJECTED").length,
    };

    console.log("🎉 Seeding des demandes de congés terminé avec succès !");
    console.log(`
📊 Résumé:
- ${stats.total} demandes créées
- ${stats.pending} en attente
- ${stats.approved} approuvées
- ${stats.rejected} refusées
    `);
  } catch (error) {
    console.error("❌ Erreur lors du seeding:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedLeaveRequests()
    .then(() => {
      console.log("✅ Script terminé");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Erreur:", error);
      process.exit(1);
    });
}

export { seedLeaveRequests };
