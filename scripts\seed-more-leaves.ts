import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedMoreLeaveRequests() {
  try {
    console.log('🌱 Ajout de congés supplémentaires pour le calendrier...');

    // Récupérer l'organisation et les employés existants
    const organization = await prisma.organization.findFirst();
    
    if (!organization) {
      console.log('❌ Aucune organisation trouvée.');
      return;
    }

    const employees = await prisma.employee.findMany({
      where: { organizationId: organization.id },
    });

    if (employees.length === 0) {
      console.log('❌ Aucun employé trouvé.');
      return;
    }

    console.log(`📋 Organisation: ${organization.name}`);
    console.log(`👥 ${employees.length} employés trouvés`);

    // Vérifier le nombre de demandes existantes pour générer des numéros uniques
    const existingCount = await prisma.leaveRequest.count({
      where: { organizationId: organization.id },
    });
    
    const currentYear = new Date().getFullYear();
    const leaveRequests = [];
    
    // Créer des congés étalés sur les 3 prochains mois
    const today = new Date();
    
    // Congé 1 - Semaine prochaine (Congé annuel approuvé)
    try {
      const leave1 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(existingCount + 1).padStart(4, "0")}`,
          employeeId: employees[0].id,
          organizationId: organization.id,
          leaveType: "ANNUAL",
          startDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // +7 jours
          endDate: new Date(today.getTime() + 11 * 24 * 60 * 60 * 1000), // +11 jours
          totalDays: 5,
          reason: "Congés annuels - vacances en famille",
          status: "APPROVED",
        },
      });
      leaveRequests.push(leave1);
    } catch (error) {
      console.log("Congé 1 déjà existant, ignoré");
    }

    // Congé 2 - Dans 2 semaines (Congé maladie en attente)
    try {
      const leave2 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(existingCount + 2).padStart(4, "0")}`,
          employeeId: employees[1].id,
          organizationId: organization.id,
          leaveType: "SICK",
          startDate: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000), // +14 jours
          endDate: new Date(today.getTime() + 16 * 24 * 60 * 60 * 1000), // +16 jours
          totalDays: 3,
          reason: "Consultation médicale et repos",
          status: "PENDING",
        },
      });
      leaveRequests.push(leave2);
    } catch (error) {
      console.log("Congé 2 déjà existant, ignoré");
    }

    // Congé 3 - Dans 3 semaines (Congé d'urgence approuvé)
    try {
      const leave3 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(existingCount + 3).padStart(4, "0")}`,
          employeeId: employees[2].id,
          organizationId: organization.id,
          leaveType: "EMERGENCY",
          startDate: new Date(today.getTime() + 21 * 24 * 60 * 60 * 1000), // +21 jours
          endDate: new Date(today.getTime() + 22 * 24 * 60 * 60 * 1000), // +22 jours
          totalDays: 2,
          reason: "Urgence familiale",
          status: "APPROVED",
        },
      });
      leaveRequests.push(leave3);
    } catch (error) {
      console.log("Congé 3 déjà existant, ignoré");
    }

    // Congé 4 - Mois prochain (Congé annuel en attente)
    try {
      const leave4 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(existingCount + 4).padStart(4, "0")}`,
          employeeId: employees[3].id,
          organizationId: organization.id,
          leaveType: "ANNUAL",
          startDate: new Date(today.getTime() + 35 * 24 * 60 * 60 * 1000), // +35 jours
          endDate: new Date(today.getTime() + 42 * 24 * 60 * 60 * 1000), // +42 jours
          totalDays: 8,
          reason: "Congés annuels - voyage",
          status: "PENDING",
        },
      });
      leaveRequests.push(leave4);
    } catch (error) {
      console.log("Congé 4 déjà existant, ignoré");
    }

    // Congé 5 - Dans 6 semaines (Congé sans solde refusé)
    try {
      const leave5 = await prisma.leaveRequest.create({
        data: {
          requestNumber: `LEAVE-${currentYear}-${String(existingCount + 5).padStart(4, "0")}`,
          employeeId: employees[4].id,
          organizationId: organization.id,
          leaveType: "UNPAID",
          startDate: new Date(today.getTime() + 42 * 24 * 60 * 60 * 1000), // +42 jours
          endDate: new Date(today.getTime() + 56 * 24 * 60 * 60 * 1000), // +56 jours
          totalDays: 15,
          reason: "Congé sans solde pour formation personnelle",
          status: "REJECTED",
          rejectionReason: "Période de forte activité",
        },
      });
      leaveRequests.push(leave5);
    } catch (error) {
      console.log("Congé 5 déjà existant, ignoré");
    }

    // Congé 6 - Dans 2 mois (Congé maternité approuvé)
    if (employees.length > 5) {
      try {
        const leave6 = await prisma.leaveRequest.create({
          data: {
            requestNumber: `LEAVE-${currentYear}-${String(existingCount + 6).padStart(4, "0")}`,
            employeeId: employees[5].id,
            organizationId: organization.id,
            leaveType: "MATERNITY",
            startDate: new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000), // +60 jours
            endDate: new Date(today.getTime() + 150 * 24 * 60 * 60 * 1000), // +150 jours
            totalDays: 90,
            reason: "Congé maternité",
            status: "APPROVED",
          },
        });
        leaveRequests.push(leave6);
      } catch (error) {
        console.log("Congé 6 déjà existant, ignoré");
      }
    }

    // Congé 7 - Chevauchement avec d'autres (Congé annuel approuvé)
    if (employees.length > 6) {
      try {
        const leave7 = await prisma.leaveRequest.create({
          data: {
            requestNumber: `LEAVE-${currentYear}-${String(existingCount + 7).padStart(4, "0")}`,
            employeeId: employees[6].id,
            organizationId: organization.id,
            leaveType: "ANNUAL",
            startDate: new Date(today.getTime() + 8 * 24 * 60 * 60 * 1000), // +8 jours (chevauche avec congé 1)
            endDate: new Date(today.getTime() + 12 * 24 * 60 * 60 * 1000), // +12 jours
            totalDays: 5,
            reason: "Congés annuels - repos",
            status: "APPROVED",
          },
        });
        leaveRequests.push(leave7);
      } catch (error) {
        console.log("Congé 7 déjà existant, ignoré");
      }
    }

    console.log(`✅ ${leaveRequests.length} nouveaux congés créés`);

    // Statistiques
    const allLeaves = await prisma.leaveRequest.findMany({
      where: { organizationId: organization.id },
    });

    const stats = {
      total: allLeaves.length,
      pending: allLeaves.filter(r => r.status === 'PENDING').length,
      approved: allLeaves.filter(r => r.status === 'APPROVED').length,
      rejected: allLeaves.filter(r => r.status === 'REJECTED').length,
    };

    console.log('🎉 Ajout de congés supplémentaires terminé avec succès !');
    console.log(`
📊 Résumé total:
- ${stats.total} demandes au total
- ${stats.pending} en attente
- ${stats.approved} approuvées
- ${stats.rejected} refusées
    `);

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedMoreLeaveRequests()
    .then(() => {
      console.log('✅ Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

export { seedMoreLeaveRequests };
