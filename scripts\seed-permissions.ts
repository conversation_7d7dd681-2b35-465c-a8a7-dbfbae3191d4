import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedPermissions() {
  try {
    console.log('🔐 Seeding permissions system...');

    // 1. <PERSON><PERSON><PERSON> toutes les permissions de base
    console.log('📋 Création des permissions de base...');
    
    const permissions = [
      // PATIENTS
      { action: 'CREATE', resource: 'PATIENTS', description: 'Créer de nouveaux patients' },
      { action: 'READ', resource: 'PATIENTS', description: 'Voir les informations des patients' },
      { action: 'UPDATE', resource: 'PATIENTS', description: 'Modifier les informations des patients' },
      { action: 'DELETE', resource: 'PATIENTS', description: 'Supprimer des patients' },
      
      // CONSULTATIONS
      { action: 'CREATE', resource: 'CONSULTATIONS', description: 'Créer des consultations' },
      { action: 'READ', resource: 'CONSULTATIONS', description: 'Voir les consultations' },
      { action: 'UPDATE', resource: 'CONSULTATIONS', description: 'Modifier les consultations' },
      { action: 'DELETE', resource: 'CONSULTATIONS', description: 'Supprimer des consultations' },
      
      // PRESCRIPTIONS
      { action: 'CREATE', resource: 'PRESCRIPTIONS', description: 'Créer des prescriptions' },
      { action: 'READ', resource: 'PRESCRIPTIONS', description: 'Voir les prescriptions' },
      { action: 'UPDATE', resource: 'PRESCRIPTIONS', description: 'Modifier les prescriptions' },
      { action: 'DELETE', resource: 'PRESCRIPTIONS', description: 'Supprimer des prescriptions' },
      { action: 'PRINT', resource: 'PRESCRIPTIONS', description: 'Imprimer les prescriptions' },
      
      // MEDICATIONS
      { action: 'CREATE', resource: 'MEDICATIONS', description: 'Ajouter des médicaments' },
      { action: 'READ', resource: 'MEDICATIONS', description: 'Voir les médicaments' },
      { action: 'UPDATE', resource: 'MEDICATIONS', description: 'Modifier les médicaments' },
      { action: 'DELETE', resource: 'MEDICATIONS', description: 'Supprimer des médicaments' },
      { action: 'MANAGE', resource: 'MEDICATIONS', description: 'Gérer le stock de médicaments' },
      
      // PHARMACY
      { action: 'READ', resource: 'PHARMACY', description: 'Voir la pharmacie' },
      { action: 'MANAGE', resource: 'PHARMACY', description: 'Gérer la pharmacie' },
      
      // LABORATORY
      { action: 'CREATE', resource: 'LABORATORY', description: 'Créer des ordres de laboratoire' },
      { action: 'READ', resource: 'LABORATORY', description: 'Voir les analyses' },
      { action: 'UPDATE', resource: 'LABORATORY', description: 'Modifier les analyses' },
      { action: 'APPROVE', resource: 'LABORATORY', description: 'Valider les résultats' },
      { action: 'PRINT', resource: 'LABORATORY', description: 'Imprimer les résultats' },
      
      // HOSPITALIZATION
      { action: 'CREATE', resource: 'HOSPITALIZATION', description: 'Créer des admissions' },
      { action: 'READ', resource: 'HOSPITALIZATION', description: 'Voir les hospitalisations' },
      { action: 'UPDATE', resource: 'HOSPITALIZATION', description: 'Modifier les hospitalisations' },
      { action: 'MANAGE', resource: 'HOSPITALIZATION', description: 'Gérer les chambres et lits' },
      
      // EMPLOYEES
      { action: 'CREATE', resource: 'EMPLOYEES', description: 'Ajouter des employés' },
      { action: 'READ', resource: 'EMPLOYEES', description: 'Voir les employés' },
      { action: 'UPDATE', resource: 'EMPLOYEES', description: 'Modifier les employés' },
      { action: 'DELETE', resource: 'EMPLOYEES', description: 'Supprimer des employés' },
      { action: 'MANAGE', resource: 'EMPLOYEES', description: 'Gérer les employés' },
      
      // DEPARTMENTS
      { action: 'CREATE', resource: 'DEPARTMENTS', description: 'Créer des départements' },
      { action: 'READ', resource: 'DEPARTMENTS', description: 'Voir les départements' },
      { action: 'UPDATE', resource: 'DEPARTMENTS', description: 'Modifier les départements' },
      { action: 'DELETE', resource: 'DEPARTMENTS', description: 'Supprimer des départements' },
      
      // PAYMENTS
      { action: 'CREATE', resource: 'PAYMENTS', description: 'Créer des paiements' },
      { action: 'READ', resource: 'PAYMENTS', description: 'Voir les paiements' },
      { action: 'UPDATE', resource: 'PAYMENTS', description: 'Modifier les paiements' },
      { action: 'MANAGE', resource: 'PAYMENTS', description: 'Gérer les paiements' },
      
      // REPORTS
      { action: 'CREATE', resource: 'REPORTS', description: 'Générer des rapports' },
      { action: 'READ', resource: 'REPORTS', description: 'Voir les rapports' },
      { action: 'EXPORT', resource: 'REPORTS', description: 'Exporter les rapports' },
      
      // ANALYTICS
      { action: 'READ', resource: 'ANALYTICS', description: 'Voir les analytics' },
      { action: 'EXPORT', resource: 'ANALYTICS', description: 'Exporter les analytics' },
      
      // SETTINGS
      { action: 'READ', resource: 'SETTINGS', description: 'Voir les paramètres' },
      { action: 'UPDATE', resource: 'SETTINGS', description: 'Modifier les paramètres' },
      { action: 'MANAGE', resource: 'SETTINGS', description: 'Gérer les paramètres' },
      
      // USERS
      { action: 'CREATE', resource: 'USERS', description: 'Créer des utilisateurs' },
      { action: 'READ', resource: 'USERS', description: 'Voir les utilisateurs' },
      { action: 'UPDATE', resource: 'USERS', description: 'Modifier les utilisateurs' },
      { action: 'DELETE', resource: 'USERS', description: 'Supprimer des utilisateurs' },
      { action: 'MANAGE', resource: 'USERS', description: 'Gérer les utilisateurs' },
      
      // ROLES
      { action: 'READ', resource: 'ROLES', description: 'Voir les rôles' },
      { action: 'MANAGE', resource: 'ROLES', description: 'Gérer les rôles et permissions' },
    ];

    for (const perm of permissions) {
      await prisma.permission.upsert({
        where: {
          action_resource: {
            action: perm.action as any,
            resource: perm.resource as any,
          },
        },
        update: {
          description: perm.description,
        },
        create: {
          action: perm.action as any,
          resource: perm.resource as any,
          description: perm.description,
        },
      });
    }

    console.log(`✅ ${permissions.length} permissions créées`);

    // 2. Définir les permissions par rôle
    console.log('👥 Attribution des permissions par rôle...');

    const rolePermissions = {
      SUPER_ADMIN: [], // Super admin a tous les droits par défaut
      
      ADMIN: [
        // Accès complet à la plupart des modules
        'READ:PATIENTS', 'CREATE:PATIENTS', 'UPDATE:PATIENTS',
        'READ:CONSULTATIONS', 'CREATE:CONSULTATIONS', 'UPDATE:CONSULTATIONS',
        'READ:PRESCRIPTIONS', 'CREATE:PRESCRIPTIONS', 'UPDATE:PRESCRIPTIONS', 'PRINT:PRESCRIPTIONS',
        'READ:MEDICATIONS', 'CREATE:MEDICATIONS', 'UPDATE:MEDICATIONS', 'MANAGE:MEDICATIONS',
        'READ:PHARMACY', 'MANAGE:PHARMACY',
        'READ:LABORATORY', 'CREATE:LABORATORY', 'UPDATE:LABORATORY', 'APPROVE:LABORATORY', 'PRINT:LABORATORY',
        'READ:HOSPITALIZATION', 'CREATE:HOSPITALIZATION', 'UPDATE:HOSPITALIZATION', 'MANAGE:HOSPITALIZATION',
        'READ:EMPLOYEES', 'CREATE:EMPLOYEES', 'UPDATE:EMPLOYEES', 'MANAGE:EMPLOYEES',
        'READ:DEPARTMENTS', 'CREATE:DEPARTMENTS', 'UPDATE:DEPARTMENTS',
        'READ:PAYMENTS', 'CREATE:PAYMENTS', 'UPDATE:PAYMENTS', 'MANAGE:PAYMENTS',
        'READ:REPORTS', 'CREATE:REPORTS', 'EXPORT:REPORTS',
        'READ:ANALYTICS', 'EXPORT:ANALYTICS',
        'READ:SETTINGS', 'UPDATE:SETTINGS', 'MANAGE:SETTINGS',
        'READ:USERS', 'CREATE:USERS', 'UPDATE:USERS', 'MANAGE:USERS',
        'READ:ROLES', 'MANAGE:ROLES',
      ],
      
      DOCTOR: [
        'READ:PATIENTS', 'CREATE:PATIENTS', 'UPDATE:PATIENTS',
        'READ:CONSULTATIONS', 'CREATE:CONSULTATIONS', 'UPDATE:CONSULTATIONS',
        'READ:PRESCRIPTIONS', 'CREATE:PRESCRIPTIONS', 'UPDATE:PRESCRIPTIONS', 'PRINT:PRESCRIPTIONS',
        'READ:MEDICATIONS',
        'READ:LABORATORY', 'CREATE:LABORATORY', 'PRINT:LABORATORY',
        'READ:HOSPITALIZATION', 'CREATE:HOSPITALIZATION', 'UPDATE:HOSPITALIZATION',
        'READ:EMPLOYEES',
        'READ:PAYMENTS',
        'READ:REPORTS',
        'READ:ANALYTICS',
      ],
      
      NURSE: [
        'READ:PATIENTS', 'UPDATE:PATIENTS',
        'READ:CONSULTATIONS', 'UPDATE:CONSULTATIONS',
        'READ:PRESCRIPTIONS',
        'READ:MEDICATIONS',
        'READ:LABORATORY',
        'READ:HOSPITALIZATION', 'UPDATE:HOSPITALIZATION',
        'READ:EMPLOYEES',
        'READ:PAYMENTS',
      ],
      
      PHARMACIST: [
        'READ:PATIENTS',
        'READ:PRESCRIPTIONS', 'PRINT:PRESCRIPTIONS',
        'READ:MEDICATIONS', 'CREATE:MEDICATIONS', 'UPDATE:MEDICATIONS', 'MANAGE:MEDICATIONS',
        'READ:PHARMACY', 'MANAGE:PHARMACY',
        'READ:PAYMENTS', 'CREATE:PAYMENTS',
      ],
      
      RECEPTIONIST: [
        'READ:PATIENTS', 'CREATE:PATIENTS', 'UPDATE:PATIENTS',
        'READ:CONSULTATIONS', 'CREATE:CONSULTATIONS',
        'READ:PAYMENTS', 'CREATE:PAYMENTS', 'UPDATE:PAYMENTS',
        'READ:HOSPITALIZATION',
      ],
      
      TECHNICIAN: [
        'READ:PATIENTS',
        'READ:LABORATORY', 'UPDATE:LABORATORY',
        'READ:HOSPITALIZATION',
      ],
      
      LAB_TECHNICIAN: [
        'READ:PATIENTS',
        'READ:LABORATORY', 'UPDATE:LABORATORY', 'APPROVE:LABORATORY', 'PRINT:LABORATORY',
      ],
      
      ACCOUNTANT: [
        'READ:PATIENTS',
        'READ:PAYMENTS', 'CREATE:PAYMENTS', 'UPDATE:PAYMENTS', 'MANAGE:PAYMENTS',
        'READ:REPORTS', 'CREATE:REPORTS', 'EXPORT:REPORTS',
        'READ:ANALYTICS', 'EXPORT:ANALYTICS',
      ],
    };

    // Créer les permissions pour chaque rôle
    for (const [role, perms] of Object.entries(rolePermissions)) {
      for (const permString of perms) {
        const [action, resource] = permString.split(':');
        
        const permission = await prisma.permission.findFirst({
          where: {
            action: action as any,
            resource: resource as any,
          },
        });

        if (permission) {
          await prisma.rolePermission.upsert({
            where: {
              role_permissionId: {
                role: role as any,
                permissionId: permission.id,
              },
            },
            update: {
              isGranted: true,
            },
            create: {
              role: role as any,
              permissionId: permission.id,
              isGranted: true,
            },
          });
        }
      }
      
      console.log(`✅ Permissions configurées pour le rôle ${role}`);
    }

    console.log('✅ Permissions system seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedPermissions()
    .then(() => {
      console.log('🎉 Permissions seeding completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Permissions seeding failed:', error);
      process.exit(1);
    });
}

export default seedPermissions;
