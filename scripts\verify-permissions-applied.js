const fs = require("fs");
const path = require("path");

// Fichiers d'actions à vérifier
const ACTION_FILES = [
  "patients-new.ts",
  "consultations.ts",
  "prescriptions.ts",
  "billing.ts",
  "appointments.ts",
  "medications.ts",
  "pharmacy.ts",
  "laboratory.ts",
  "hospitalization.ts",
  "hr.ts",
  "reports.ts",
  "analytics.ts",
];

function checkFilePermissions(filePath) {
  const fileName = path.basename(filePath);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${fileName} - Fichier non trouvé`);
    return { fileName, status: "NOT_FOUND", details: [] };
  }

  const content = fs.readFileSync(filePath, "utf8");

  // Vérifier l'import des permissions
  const hasPermissionImport = content.includes(
    'import { checkPermission, logAction } from "./permissions"'
  );

  // Trouver toutes les fonctions exportées
  const functionRegex = /export\s+async\s+function\s+(\w+)/g;
  let match;
  const functions = [];

  while ((match = functionRegex.exec(content)) !== null) {
    functions.push(match[1]);
  }

  // Vérifier les vérifications de permissions
  const permissionChecks = [];
  const permissionCheckRegex =
    /const\s+can\w+\s*=\s*await\s+checkPermission\(/g;

  while ((match = permissionCheckRegex.exec(content)) !== null) {
    permissionChecks.push(match[0]);
  }

  // Vérifier les logs d'audit
  const auditLogs = [];
  const auditLogRegex = /await\s+logAction\(/g;

  while ((match = auditLogRegex.exec(content)) !== null) {
    auditLogs.push(match[0]);
  }

  const details = {
    hasPermissionImport,
    functionsCount: functions.length,
    permissionChecksCount: permissionChecks.length,
    auditLogsCount: auditLogs.length,
    functions,
  };

  let status = "GOOD";
  if (!hasPermissionImport) {
    status = "MISSING_IMPORT";
  } else if (permissionChecks.length === 0) {
    status = "NO_PERMISSIONS";
  } else if (permissionChecks.length < functions.length / 2) {
    status = "PARTIAL_PERMISSIONS";
  }

  return { fileName, status, details };
}

function main() {
  console.log("🔍 Vérification de l'application des permissions...\n");

  const actionsDir = path.join(__dirname, "..", "src", "lib", "actions");
  const results = [];

  for (const fileName of ACTION_FILES) {
    const filePath = path.join(actionsDir, fileName);
    const result = checkFilePermissions(filePath);
    results.push(result);

    const statusEmoji = {
      GOOD: "✅",
      PARTIAL_PERMISSIONS: "⚠️",
      NO_PERMISSIONS: "❌",
      MISSING_IMPORT: "❌",
      NOT_FOUND: "❌",
    };

    console.log(`${statusEmoji[result.status]} ${result.fileName}`);
    console.log(
      `   Import permissions: ${
        result.details.hasPermissionImport ? "✅" : "❌"
      }`
    );
    console.log(`   Fonctions: ${result.details.functionsCount}`);
    console.log(
      `   Vérifications permissions: ${result.details.permissionChecksCount}`
    );
    console.log(`   Logs d'audit: ${result.details.auditLogsCount}`);

    if (result.status === "PARTIAL_PERMISSIONS") {
      console.log(
        `   ⚠️  Seulement ${result.details.permissionChecksCount}/${result.details.functionsCount} fonctions protégées`
      );
    }

    if (result.details.functions && result.details.functions.length > 0) {
      console.log(`   Fonctions: ${result.details.functions.join(", ")}`);
    }

    console.log("");
  }

  // Résumé
  const summary = results.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {});

  console.log("📊 RÉSUMÉ:");
  console.log(`   ✅ Bien protégés: ${summary.GOOD || 0}`);
  console.log(
    `   ⚠️  Partiellement protégés: ${summary.PARTIAL_PERMISSIONS || 0}`
  );
  console.log(
    `   ❌ Non protégés: ${
      (summary.NO_PERMISSIONS || 0) + (summary.MISSING_IMPORT || 0)
    }`
  );
  console.log(`   ❌ Non trouvés: ${summary.NOT_FOUND || 0}`);

  const totalProtected =
    (summary.GOOD || 0) + (summary.PARTIAL_PERMISSIONS || 0);
  const totalFiles = results.length;
  const protectionRate = Math.round((totalProtected / totalFiles) * 100);

  console.log(
    `\n🎯 Taux de protection: ${protectionRate}% (${totalProtected}/${totalFiles})`
  );

  if (protectionRate < 100) {
    console.log("\n⚠️  Des fichiers nécessitent encore des permissions !");
  } else {
    console.log("\n🎉 Tous les fichiers sont protégés !");
  }
}

if (require.main === module) {
  main();
}
