"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  DollarSign,
  Activity,
  Clock,
  Heart,
  Download,
  FileText,
  Filter,
  RefreshCw,
} from "lucide-react";

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("30d");
  const [activeTab, setActiveTab] = useState("overview");

  // Données de démonstration pour les KPIs
  const kpis = [
    {
      title: "Revenus du Mois",
      value: "12,450,000",
      unit: "XOF",
      change: "+15.2%",
      trend: "up",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Nouveaux Patients",
      value: "156",
      unit: "patients",
      change: "+8.1%",
      trend: "up",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Consultations",
      value: "342",
      unit: "consultations",
      change: "+12.5%",
      trend: "up",
      icon: Activity,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Taux de Satisfaction",
      value: "94.8",
      unit: "%",
      change: "-1.2%",
      trend: "down",
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Temps d'Attente Moyen",
      value: "18",
      unit: "minutes",
      change: "-5.3%",
      trend: "up",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Taux d'Occupation",
      value: "84.6",
      unit: "%",
      change: "+3.2%",
      trend: "up",
      icon: Calendar,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
    },
  ];

  const departmentStats = [
    {
      name: "Médecine Générale",
      patients: 145,
      revenue: "3,200,000",
      satisfaction: 95.2,
      trend: "up",
    },
    {
      name: "Pédiatrie",
      patients: 89,
      revenue: "1,800,000",
      satisfaction: 97.1,
      trend: "up",
    },
    {
      name: "Gynécologie",
      patients: 67,
      revenue: "2,100,000",
      satisfaction: 93.8,
      trend: "down",
    },
    {
      name: "Chirurgie",
      patients: 34,
      revenue: "4,500,000",
      satisfaction: 92.5,
      trend: "up",
    },
    {
      name: "Laboratoire",
      patients: 234,
      revenue: "850,000",
      satisfaction: 96.3,
      trend: "up",
    },
  ];

  const monthlyData = [
    { month: "Jan", patients: 120, revenue: 8500000, consultations: 280 },
    { month: "Fév", patients: 135, revenue: 9200000, consultations: 310 },
    { month: "Mar", patients: 142, revenue: 9800000, consultations: 325 },
    { month: "Avr", patients: 158, revenue: 10500000, consultations: 340 },
    { month: "Mai", patients: 167, revenue: 11200000, consultations: 365 },
    { month: "Juin", patients: 156, revenue: 12450000, consultations: 342 },
  ];

  const getTrendIcon = (trend: string) => {
    return trend === "up" ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    );
  };

  const getTrendColor = (trend: string) => {
    return trend === "up" ? "text-green-600" : "text-red-600";
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Dashboard & Statistiques
            </h1>
            <p className="text-gray-600 mt-2">
              KPIs en temps réel et analyses de performance
            </p>
          </div>
          <div className="flex space-x-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 derniers jours</SelectItem>
                <SelectItem value="30d">30 derniers jours</SelectItem>
                <SelectItem value="90d">3 derniers mois</SelectItem>
                <SelectItem value="1y">Cette année</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>

        {/* KPIs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {kpis.map((kpi, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {kpi.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${kpi.bgColor}`}>
                  <kpi.icon className={`h-4 w-4 ${kpi.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {kpi.value}
                  <span className="text-sm font-normal text-gray-500 ml-1">
                    {kpi.unit}
                  </span>
                </div>
                <div className="flex items-center mt-2">
                  {getTrendIcon(kpi.trend)}
                  <span className={`text-sm ml-1 ${getTrendColor(kpi.trend)}`}>
                    {kpi.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">
                    vs mois dernier
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tabs pour différentes vues */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="departments">Départements</TabsTrigger>
            <TabsTrigger value="trends">Tendances</TabsTrigger>
            <TabsTrigger value="reports">Rapports</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Graphique des revenus */}
              <Card>
                <CardHeader>
                  <CardTitle>Évolution des Revenus</CardTitle>
                  <CardDescription>
                    Revenus mensuels sur les 6 derniers mois
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Graphique des revenus</p>
                      <p className="text-sm text-gray-400">
                        Intégration Chart.js à venir
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Graphique des patients */}
              <Card>
                <CardHeader>
                  <CardTitle>Nouveaux Patients</CardTitle>
                  <CardDescription>
                    Évolution du nombre de nouveaux patients
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <div className="text-center">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Graphique des patients</p>
                      <p className="text-sm text-gray-400">
                        Intégration Chart.js à venir
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="departments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance par Département</CardTitle>
                <CardDescription>
                  Statistiques détaillées par service médical
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {departmentStats.map((dept, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{dept.name}</h3>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                          <span>{dept.patients} patients</span>
                          <span>{dept.revenue} XOF</span>
                          <span>{dept.satisfaction}% satisfaction</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getTrendIcon(dept.trend)}
                        <Badge variant="outline">
                          {dept.satisfaction}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Analyse des Tendances</CardTitle>
                <CardDescription>
                  Évolution des métriques clés sur la période sélectionnée
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96 flex items-center justify-center bg-gray-50 rounded">
                  <div className="text-center">
                    <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg mb-2">Analyse des tendances</p>
                    <p className="text-sm text-gray-400">
                      Graphiques interactifs avec Chart.js/Recharts à implémenter
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Rapports Disponibles</CardTitle>
                <CardDescription>
                  Générez et exportez des rapports détaillés
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <FileText className="h-6 w-6 mb-2" />
                    Rapport Mensuel
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    Analyse Financière
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    Rapport Patients
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Activity className="h-6 w-6 mb-2" />
                    Performance Médicale
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
