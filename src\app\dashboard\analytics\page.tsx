"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  DollarSign,
  Activity,
  Clock,
  Heart,
  Download,
  FileText,
  Filter,
  RefreshCw,
} from "lucide-react";
import {
  getMainKPIs,
  getDepartmentStats,
  getMonthlyData,
} from "@/lib/actions/analytics";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieC<PERSON>,
  Pie,
  <PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("30d");
  const [activeTab, setActiveTab] = useState("overview");
  const [kpis, setKpis] = useState<any[]>([]);
  const [departmentStats, setDepartmentStats] = useState<any[]>([]);
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadAnalytics() {
      try {
        setLoading(true);
        const [kpisResult, deptResult, monthlyResult] = await Promise.all([
          getMainKPIs({ timeRange: timeRange as any }),
          getDepartmentStats({ timeRange: timeRange as any }),
          getMonthlyData({ timeRange: "1y" }),
        ]);

        if (kpisResult.success) {
          setKpis(kpisResult.kpis || []);
        } else {
          toast.error("Erreur lors du chargement des KPIs");
        }

        if (deptResult.success) {
          setDepartmentStats(deptResult.departmentStats || []);
        } else {
          toast.error("Erreur lors du chargement des stats départements");
        }

        if (monthlyResult.success) {
          setMonthlyData(monthlyResult.monthlyData || []);
        } else {
          toast.error("Erreur lors du chargement des données mensuelles");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadAnalytics();
  }, [timeRange]);

  // Transformer les KPIs pour l'affichage
  const displayKpis = kpis.map((kpi, index) => {
    const icons = [DollarSign, Users, Activity, Heart, Clock, Calendar];
    const colors = [
      { color: "text-green-600", bgColor: "bg-green-100" },
      { color: "text-blue-600", bgColor: "bg-blue-100" },
      { color: "text-purple-600", bgColor: "bg-purple-100" },
      { color: "text-red-600", bgColor: "bg-red-100" },
      { color: "text-orange-600", bgColor: "bg-orange-100" },
      { color: "text-indigo-600", bgColor: "bg-indigo-100" },
    ];

    return {
      ...kpi,
      icon: icons[index] || Activity,
      ...(colors[index] || colors[0]),
    };
  });

  const getTrendIcon = (trend: string) => {
    return trend === "up" ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    );
  };

  const getTrendColor = (trend: string) => {
    return trend === "up" ? "text-green-600" : "text-red-600";
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des analytics...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Dashboard & Statistiques
            </h1>
            <p className="text-gray-600 mt-2">
              KPIs en temps réel et analyses de performance
            </p>
          </div>
          <div className="flex space-x-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 derniers jours</SelectItem>
                <SelectItem value="30d">30 derniers jours</SelectItem>
                <SelectItem value="90d">3 derniers mois</SelectItem>
                <SelectItem value="1y">Cette année</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>

        {/* KPIs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayKpis.map((kpi, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {kpi.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${kpi.bgColor}`}>
                  <kpi.icon className={`h-4 w-4 ${kpi.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {kpi.value}
                  <span className="text-sm font-normal text-gray-500 ml-1">
                    {kpi.unit}
                  </span>
                </div>
                <div className="flex items-center mt-2">
                  {getTrendIcon(kpi.trend)}
                  <span className={`text-sm ml-1 ${getTrendColor(kpi.trend)}`}>
                    {kpi.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">
                    vs mois dernier
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tabs pour différentes vues */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="departments">Départements</TabsTrigger>
            <TabsTrigger value="trends">Tendances</TabsTrigger>
            <TabsTrigger value="reports">Rapports</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Graphique des revenus */}
              <Card>
                <CardHeader>
                  <CardTitle>Évolution des Revenus</CardTitle>
                  <CardDescription>
                    Revenus mensuels sur les 6 derniers mois
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={monthlyData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any) => [
                            `${value.toLocaleString()} XOF`,
                            "Revenus",
                          ]}
                        />
                        <Area
                          type="monotone"
                          dataKey="revenue"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.1}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Graphique des patients */}
              <Card>
                <CardHeader>
                  <CardTitle>Nouveaux Patients</CardTitle>
                  <CardDescription>
                    Évolution du nombre de nouveaux patients
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={monthlyData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any) => [
                            `${value} patients`,
                            "Nouveaux patients",
                          ]}
                        />
                        <Line
                          type="monotone"
                          dataKey="patients"
                          stroke="#10b981"
                          strokeWidth={3}
                          dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="departments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance par Département</CardTitle>
                <CardDescription>
                  Statistiques détaillées par service médical
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Graphique en barres des départements */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={departmentStats}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="name"
                          angle={-45}
                          textAnchor="end"
                          height={80}
                          fontSize={12}
                        />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any, name: string) => [
                            name === "revenue"
                              ? `${value.toLocaleString()} XOF`
                              : value,
                            name === "revenue"
                              ? "Revenus"
                              : name === "patients"
                              ? "Patients"
                              : "Consultations",
                          ]}
                        />
                        <Legend />
                        <Bar
                          dataKey="patients"
                          fill="#3b82f6"
                          name="Patients"
                        />
                        <Bar
                          dataKey="consultations"
                          fill="#10b981"
                          name="Consultations"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Liste détaillée */}
                  <div className="space-y-4">
                    {departmentStats.map((dept, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">
                            {dept.name}
                          </h3>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                            <span>{dept.patients} patients</span>
                            <span>{dept.revenue.toLocaleString()} XOF</span>
                            <span>
                              {dept.satisfaction.toFixed(1)}% satisfaction
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTrendIcon(dept.trend)}
                          <Badge variant="outline">
                            {dept.satisfaction.toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Graphique combiné */}
              <Card>
                <CardHeader>
                  <CardTitle>Évolution Globale</CardTitle>
                  <CardDescription>
                    Patients, consultations et admissions par mois
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={monthlyData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="patients"
                          stroke="#3b82f6"
                          strokeWidth={2}
                          name="Patients"
                        />
                        <Line
                          type="monotone"
                          dataKey="consultations"
                          stroke="#10b981"
                          strokeWidth={2}
                          name="Consultations"
                        />
                        <Line
                          type="monotone"
                          dataKey="admissions"
                          stroke="#f59e0b"
                          strokeWidth={2}
                          name="Admissions"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Graphique en secteurs des activités */}
              <Card>
                <CardHeader>
                  <CardTitle>Répartition des Activités</CardTitle>
                  <CardDescription>
                    Distribution des services ce mois
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            {
                              name: "Consultations",
                              value:
                                monthlyData[monthlyData.length - 1]
                                  ?.consultations || 0,
                              fill: "#3b82f6",
                            },
                            {
                              name: "Laboratoire",
                              value:
                                monthlyData[monthlyData.length - 1]?.labTests ||
                                0,
                              fill: "#10b981",
                            },
                            {
                              name: "Admissions",
                              value:
                                monthlyData[monthlyData.length - 1]
                                  ?.admissions || 0,
                              fill: "#f59e0b",
                            },
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) =>
                            `${name} ${(percent * 100).toFixed(0)}%`
                          }
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        />
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Rapports Disponibles</CardTitle>
                <CardDescription>
                  Générez et exportez des rapports détaillés
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <FileText className="h-6 w-6 mb-2" />
                    Rapport Mensuel
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    Analyse Financière
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    Rapport Patients
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Activity className="h-6 w-6 mb-2" />
                    Performance Médicale
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
