"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Save,
  Calendar,
  Clock,
  User,
  Stethoscope,
  AlertCircle,
  Loader2,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import {
  getAppointmentById,
  updateAppointment,
  getAvailableDoctors,
} from "@/lib/actions/appointments";
import { getPatients } from "@/lib/actions/patients-new";

// Types
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  patientNumber: string;
  phone?: string | null;
  email?: string | null;
}

interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface AppointmentData {
  id: string;
  consultationDate: string | Date;
  status: string;
  type: string;
  chiefComplaint?: string;
  consultationFee?: number;
  notes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
  };
  doctor: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

export default function EditAppointmentPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [appointment, setAppointment] = useState<AppointmentData | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // États pour le formulaire
  const [formData, setFormData] = useState({
    patientId: "",
    doctorId: "",
    consultationDate: "",
    type: "GENERAL" as const,
    chiefComplaint: "",
    consultationFee: "",
    notes: "",
  });

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      if (!params.id) return;

      try {
        console.log("🔄 Chargement du rendez-vous:", params.id);

        const [appointmentResult, patientsResult, doctorsResult] =
          await Promise.all([
            getAppointmentById(params.id as string),
            getPatients({}),
            getAvailableDoctors(),
          ]);

        console.log("📊 Résultats:", {
          appointmentResult,
          patientsResult,
          doctorsResult,
        });

        if (appointmentResult.success && appointmentResult.appointment) {
          const apt = appointmentResult.appointment;
          setAppointment(apt as any);

          // Pré-remplir le formulaire
          const consultationDate =
            typeof apt.consultationDate === "string"
              ? apt.consultationDate
              : apt.consultationDate.toISOString();

          setFormData({
            patientId: apt.patient.id,
            doctorId: apt.doctor.id,
            consultationDate: consultationDate.slice(0, 16), // Format datetime-local
            type: apt.type as any,
            chiefComplaint: apt.chiefComplaint || "",
            consultationFee: apt.consultationFee?.toString() || "",
            notes: apt.notes || "",
          });
        } else {
          console.error(
            "❌ Erreur lors du chargement du rendez-vous:",
            appointmentResult.error
          );
          router.push("/dashboard/appointments");
          return;
        }

        if (patientsResult.success && patientsResult.patients) {
          setPatients(patientsResult.patients);
        }

        if (doctorsResult.success && doctorsResult.doctors) {
          setDoctors(doctorsResult.doctors);
        }
      } catch (error) {
        console.error("💥 Exception lors du chargement:", error);
        router.push("/dashboard/appointments");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [params.id, router]);

  // Gérer les changements de formulaire
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Sauvegarder les modifications
  const handleSave = async () => {
    if (!appointment) return;

    setSaving(true);
    try {
      const updateData = {
        patientId: formData.patientId,
        doctorId: formData.doctorId,
        consultationDate: new Date(formData.consultationDate),
        type: formData.type,
        chiefComplaint: formData.chiefComplaint || undefined,
        consultationFee: formData.consultationFee
          ? parseFloat(formData.consultationFee)
          : undefined,
        notes: formData.notes || undefined,
      };

      console.log("💾 Sauvegarde des modifications:", updateData);

      const result = await updateAppointment(appointment.id, updateData);

      if (result.success) {
        console.log("✅ Rendez-vous mis à jour");
        router.push("/dashboard/appointments");
      } else {
        console.error("❌ Erreur lors de la sauvegarde:", result.error);
        alert("Erreur lors de la sauvegarde: " + result.error);
      }
    } catch (error) {
      console.error("💥 Exception lors de la sauvegarde:", error);
      alert("Une erreur est survenue lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  // Obtenir la couleur du badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SCHEDULED: { label: "Programmé", className: "bg-blue-100 text-blue-800" },
      IN_PROGRESS: {
        label: "En cours",
        className: "bg-yellow-100 text-yellow-800",
      },
      COMPLETED: { label: "Terminé", className: "bg-green-100 text-green-800" },
      CANCELLED: { label: "Annulé", className: "bg-red-100 text-red-800" },
      NO_SHOW: { label: "Absent", className: "bg-gray-100 text-gray-800" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] ||
      statusConfig.SCHEDULED;
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement du rendez-vous...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!appointment) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Rendez-vous non trouvé
              </h3>
              <p className="text-gray-600 mb-4">
                Ce rendez-vous n'existe pas ou vous n'avez pas les permissions
                pour le modifier.
              </p>
              <Button asChild>
                <Link href="/dashboard/appointments">
                  Retour aux rendez-vous
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/appointments">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Modifier le Rendez-vous
              </h1>
              <p className="text-gray-600 mt-2">
                {appointment.patient.firstName} {appointment.patient.lastName} •{" "}
                {appointment.patient.patientNumber}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(appointment.status)}
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Sauvegarder
            </Button>
          </div>
        </div>

        {/* Formulaire de modification */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations du rendez-vous */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Informations du Rendez-vous
              </CardTitle>
              <CardDescription>
                Modifiez les détails du rendez-vous
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="patient">Patient</Label>
                <Select
                  value={formData.patientId}
                  onValueChange={(value) =>
                    handleInputChange("patientId", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un patient" />
                  </SelectTrigger>
                  <SelectContent>
                    {patients.map((patient) => (
                      <SelectItem key={patient.id} value={patient.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {patient.firstName} {patient.lastName}
                          </span>
                          <span className="text-sm text-gray-500">
                            {patient.patientNumber} •{" "}
                            {patient.phone || patient.email || "Pas de contact"}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="doctor">Médecin</Label>
                <Select
                  value={formData.doctorId}
                  onValueChange={(value) =>
                    handleInputChange("doctorId", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un médecin" />
                  </SelectTrigger>
                  <SelectContent>
                    {doctors.map((doctor) => (
                      <SelectItem key={doctor.id} value={doctor.id}>
                        Dr. {doctor.firstName} {doctor.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="consultationDate">Date et heure</Label>
                <Input
                  id="consultationDate"
                  type="datetime-local"
                  value={formData.consultationDate}
                  onChange={(e) =>
                    handleInputChange("consultationDate", e.target.value)
                  }
                />
              </div>

              <div>
                <Label htmlFor="type">Type de consultation</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GENERAL">
                      Consultation générale
                    </SelectItem>
                    <SelectItem value="EMERGENCY">Urgence</SelectItem>
                    <SelectItem value="FOLLOW_UP">Suivi</SelectItem>
                    <SelectItem value="SPECIALIST">Spécialiste</SelectItem>
                    <SelectItem value="TELEMEDICINE">Télémédecine</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Détails médicaux */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Stethoscope className="h-5 w-5 mr-2" />
                Détails Médicaux
              </CardTitle>
              <CardDescription>
                Motif et informations complémentaires
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="chiefComplaint">Motif de consultation</Label>
                <Textarea
                  id="chiefComplaint"
                  placeholder="Décrivez le motif de la consultation..."
                  value={formData.chiefComplaint}
                  onChange={(e) =>
                    handleInputChange("chiefComplaint", e.target.value)
                  }
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="consultationFee">Tarif (FCFA)</Label>
                <Input
                  id="consultationFee"
                  type="number"
                  placeholder="Ex: 15000"
                  value={formData.consultationFee}
                  onChange={(e) =>
                    handleInputChange("consultationFee", e.target.value)
                  }
                  min="0"
                  step="500"
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Notes additionnelles..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <Card>
          <CardContent className="flex items-center justify-between py-4">
            <div className="text-sm text-gray-600">
              Dernière modification: {new Date().toLocaleDateString("fr-FR")}
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/dashboard/appointments">Annuler</Link>
              </Button>
              <Button onClick={handleSave} disabled={saving}>
                {saving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Sauvegarder les modifications
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
