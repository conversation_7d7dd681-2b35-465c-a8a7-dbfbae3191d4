"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar,
  ArrowLeft,
  User,
  Clock,
  Stethoscope,
  DollarSign,
  Save,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import {
  createAppointment,
  getAvailableDoctors,
} from "@/lib/actions/appointments";
import { getPatients } from "@/lib/actions/patients-new";

// Types
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  patientNumber: string;
  phone?: string | null;
  email?: string | null;
}

interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
}

export default function NewAppointmentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  // Récupérer l'ID du patient depuis l'URL si présent
  const preSelectedPatientId = searchParams.get("patientId");

  // Données du formulaire
  const [formData, setFormData] = useState({
    patientId: "",
    doctorId: "",
    consultationDate: "",
    type: "GENERAL" as const,
    chiefComplaint: "",
    consultationFee: "",
    notes: "",
  });

  // Charger les patients et médecins
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log("🔄 Chargement des patients et médecins...");

        const [patientsResult, doctorsResult] = await Promise.all([
          getPatients({}),
          getAvailableDoctors(),
        ]);

        console.log("📊 Résultat patients:", patientsResult);
        console.log("📊 Résultat médecins:", doctorsResult);

        if (patientsResult.success && patientsResult.patients) {
          console.log("✅ Patients chargés:", patientsResult.patients.length);
          setPatients(patientsResult.patients);

          // Pré-sélectionner le patient si l'ID est dans l'URL
          if (preSelectedPatientId) {
            const patientExists = patientsResult.patients.find(
              (p) => p.id === preSelectedPatientId
            );
            if (patientExists) {
              setFormData((prev) => ({
                ...prev,
                patientId: preSelectedPatientId,
              }));
              console.log(
                "✅ Patient pré-sélectionné:",
                patientExists.firstName,
                patientExists.lastName
              );
            }
          }
        } else {
          console.error("❌ Erreur patients:", patientsResult.error);
        }

        if (doctorsResult.success && doctorsResult.doctors) {
          console.log("✅ Médecins chargés:", doctorsResult.doctors.length);
          setDoctors(doctorsResult.doctors);
        } else {
          console.error("❌ Erreur médecins:", doctorsResult.error);
        }
      } catch (error) {
        console.error("💥 Erreur lors du chargement des données:", error);
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [preSelectedPatientId]);

  // Gérer les changements du formulaire
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Soumettre le formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const appointmentData = {
        ...formData,
        consultationFee: formData.consultationFee
          ? parseFloat(formData.consultationFee)
          : undefined,
      };

      const result = await createAppointment(appointmentData);

      if (result.success) {
        router.push("/dashboard/appointments");
      } else {
        console.error(
          "Erreur lors de la création du rendez-vous:",
          result.error
        );
        alert("Erreur lors de la création du rendez-vous: " + result.error);
      }
    } catch (error) {
      console.error("Erreur lors de la soumission:", error);
      alert("Une erreur est survenue lors de la création du rendez-vous");
    } finally {
      setLoading(false);
    }
  };

  // Obtenir la date/heure minimale (maintenant)
  const getMinDateTime = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30); // Au moins 30 minutes dans le futur
    return now.toISOString().slice(0, 16); // Format YYYY-MM-DDTHH:MM
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/appointments">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Nouveau Rendez-vous
              </h1>
              <p className="text-gray-600 mt-2">
                Programmer un nouveau rendez-vous médical
              </p>
            </div>
          </div>
        </div>

        {loadingData ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement des données...
            </CardContent>
          </Card>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Informations du patient */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Patient
                  </CardTitle>
                  <CardDescription>
                    Sélectionnez le patient pour ce rendez-vous
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="patientId">Patient *</Label>
                    <Select
                      value={formData.patientId}
                      onValueChange={(value) =>
                        handleInputChange("patientId", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={`Sélectionner un patient (${patients.length} disponibles)`}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {patients.length === 0 ? (
                          <div className="p-2 text-sm text-gray-500 text-center">
                            Aucun patient trouvé
                          </div>
                        ) : (
                          patients.map((patient) => (
                            <SelectItem key={patient.id} value={patient.id}>
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {patient.firstName} {patient.lastName}
                                </span>
                                <span className="text-sm text-gray-500">
                                  {patient.patientNumber} •{" "}
                                  {patient.phone ||
                                    patient.email ||
                                    "Pas de contact"}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="chiefComplaint">
                      Motif de consultation
                    </Label>
                    <Textarea
                      id="chiefComplaint"
                      placeholder="Décrivez brièvement le motif de la consultation..."
                      value={formData.chiefComplaint}
                      onChange={(e) =>
                        handleInputChange("chiefComplaint", e.target.value)
                      }
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Informations du médecin et planning */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Stethoscope className="h-5 w-5 mr-2" />
                    Médecin et Planning
                  </CardTitle>
                  <CardDescription>
                    Choisissez le médecin et l'horaire du rendez-vous
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="doctorId">Médecin *</Label>
                    <Select
                      value={formData.doctorId}
                      onValueChange={(value) =>
                        handleInputChange("doctorId", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un médecin" />
                      </SelectTrigger>
                      <SelectContent>
                        {doctors.map((doctor) => (
                          <SelectItem key={doctor.id} value={doctor.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                Dr. {doctor.firstName} {doctor.lastName}
                              </span>
                              <span className="text-sm text-gray-500">
                                {doctor.role === "DOCTOR"
                                  ? "Médecin"
                                  : "Administrateur"}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="consultationDate">Date et heure *</Label>
                    <Input
                      id="consultationDate"
                      type="datetime-local"
                      value={formData.consultationDate}
                      onChange={(e) =>
                        handleInputChange("consultationDate", e.target.value)
                      }
                      min={getMinDateTime()}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="type">Type de consultation</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) =>
                        handleInputChange("type", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="GENERAL">
                          Consultation générale
                        </SelectItem>
                        <SelectItem value="EMERGENCY">Urgence</SelectItem>
                        <SelectItem value="FOLLOW_UP">Suivi</SelectItem>
                        <SelectItem value="SPECIALIST">Spécialiste</SelectItem>
                        <SelectItem value="TELEMEDICINE">
                          Télémédecine
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Informations complémentaires */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Informations Complémentaires
                </CardTitle>
                <CardDescription>
                  Tarifs et notes additionnelles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="consultationFee">Tarif (FCFA)</Label>
                    <Input
                      id="consultationFee"
                      type="number"
                      placeholder="Ex: 15000"
                      value={formData.consultationFee}
                      onChange={(e) =>
                        handleInputChange("consultationFee", e.target.value)
                      }
                      min="0"
                      step="500"
                    />
                  </div>

                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Notes additionnelles..."
                      value={formData.notes}
                      onChange={(e) =>
                        handleInputChange("notes", e.target.value)
                      }
                      rows={2}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-4">
              <Button variant="outline" asChild>
                <Link href="/dashboard/appointments">Annuler</Link>
              </Button>
              <Button
                type="submit"
                disabled={
                  loading ||
                  !formData.patientId ||
                  !formData.doctorId ||
                  !formData.consultationDate
                }
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Création...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Créer le Rendez-vous
                  </>
                )}
              </Button>
            </div>
          </form>
        )}
      </div>
    </DashboardLayout>
  );
}
