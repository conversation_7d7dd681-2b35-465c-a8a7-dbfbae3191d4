"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Calendar,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  X,
  Clock,
  User,
  Stethoscope,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  CalendarDays,
  Phone,
  Mail,
  CreditCard,
} from "lucide-react";
import Link from "next/link";
import {
  getAppointments,
  updateAppointmentStatus,
} from "@/lib/actions/appointments";
import { startConsultation } from "@/lib/actions/consultations";
import {
  calculateAge,
  formatGender,
  getPatientInitials,
} from "@/lib/utils/patient-utils";

// Types pour les rendez-vous
interface Appointment {
  id: string;
  consultationDate: string;
  status: string;
  type: string;
  chiefComplaint?: string;
  consultationFee?: number;
  duration?: number;
  notes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
    dateOfBirth: string;
    gender: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
  };
}

export default function AppointmentsPage() {
  const { data: session } = useSession();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [dateFilter, setDateFilter] = useState<string>("");

  const loadAppointments = useCallback(async () => {
    setLoading(true);
    try {
      console.log("🔄 Chargement des rendez-vous...");
      const result = await getAppointments({
        search: searchTerm || undefined,
        status: (statusFilter as any) || undefined,
        type: (typeFilter as any) || undefined,
        dateFrom: dateFilter || undefined,
      });

      console.log("📊 Résultat du chargement:", result);

      if (result.success && result.appointments) {
        console.log("✅ Rendez-vous chargés:", result.appointments.length);
        setAppointments(result.appointments);
      } else {
        console.error(
          "❌ Erreur lors du chargement des rendez-vous:",
          result.error
        );
        setAppointments([]);
      }
    } catch (error) {
      console.error("💥 Exception lors du chargement des rendez-vous:", error);
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter, typeFilter, dateFilter]);

  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Obtenir la couleur du badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING_PAYMENT: {
        label: "En attente de paiement",
        className: "bg-orange-100 text-orange-800",
      },
      SCHEDULED: { label: "Programmé", className: "bg-blue-100 text-blue-800" },
      IN_PROGRESS: {
        label: "En cours",
        className: "bg-yellow-100 text-yellow-800",
      },
      COMPLETED: { label: "Terminé", className: "bg-green-100 text-green-800" },
      CANCELLED: { label: "Annulé", className: "bg-red-100 text-red-800" },
      NO_SHOW: { label: "Absent", className: "bg-gray-100 text-gray-800" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] ||
      statusConfig.SCHEDULED;
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Obtenir la couleur du badge de type
  const getTypeBadge = (type: string) => {
    const typeConfig = {
      GENERAL: { label: "Générale", className: "bg-blue-100 text-blue-800" },
      EMERGENCY: { label: "Urgence", className: "bg-red-100 text-red-800" },
      FOLLOW_UP: { label: "Suivi", className: "bg-green-100 text-green-800" },
      SPECIALIST: {
        label: "Spécialiste",
        className: "bg-purple-100 text-purple-800",
      },
      TELEMEDICINE: {
        label: "Télémédecine",
        className: "bg-orange-100 text-orange-800",
      },
    };

    const config =
      typeConfig[type as keyof typeof typeConfig] || typeConfig.GENERAL;
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Formater la date et l'heure
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const isToday = date.toDateString() === today.toDateString();
    const isTomorrow = date.toDateString() === tomorrow.toDateString();

    const timeStr = date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (isToday) {
      return `Aujourd'hui à ${timeStr}`;
    } else if (isTomorrow) {
      return `Demain à ${timeStr}`;
    } else {
      return `${date.toLocaleDateString("fr-FR")} à ${timeStr}`;
    }
  };

  // Mettre à jour le statut d'un rendez-vous
  const handleStatusUpdate = async (
    appointmentId: string,
    newStatus: string
  ) => {
    try {
      const result = await updateAppointmentStatus(
        appointmentId,
        newStatus as any
      );
      if (result.success) {
        loadAppointments(); // Recharger la liste
      } else {
        console.error("Erreur lors de la mise à jour:", result.error);
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour du statut:", error);
    }
  };

  // Démarrer une consultation depuis un rendez-vous
  const handleStartConsultation = async (appointmentId: string) => {
    try {
      const result = await startConsultation(appointmentId);
      if (result.success) {
        loadAppointments(); // Recharger la liste
        // Rediriger vers la consultation
        window.location.href = `/dashboard/consultations/${appointmentId}`;
      } else {
        console.error(
          "Erreur lors du démarrage de la consultation:",
          result.error
        );
        alert("Erreur lors du démarrage de la consultation: " + result.error);
      }
    } catch (error) {
      console.error("Erreur lors du démarrage de la consultation:", error);
      alert("Une erreur est survenue");
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Gestion des Rendez-vous
            </h1>
            <p className="text-gray-600 mt-2">
              Planifiez et gérez les rendez-vous médicaux
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={loadAppointments}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Actualiser
            </Button>
            <Button asChild>
              <Link href="/dashboard/appointments/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Rendez-vous
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Rendez-vous
              </CardTitle>
              <CalendarDays className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{appointments.length}</div>
              <p className="text-xs text-muted-foreground">
                Tous les rendez-vous
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                En attente paiement
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  appointments.filter((a) => a.status === "PENDING_PAYMENT")
                    .length
                }
              </div>
              <p className="text-xs text-muted-foreground">À payer</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En cours</CardTitle>
              <Stethoscope className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {appointments.filter((a) => a.status === "IN_PROGRESS").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Consultations actives
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminés</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {appointments.filter((a) => a.status === "COMPLETED").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Consultations finalisées
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filtres et recherche */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher par patient, médecin ou motif..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tous les statuts</SelectItem>
                  <SelectItem value="PENDING_PAYMENT">
                    En attente de paiement
                  </SelectItem>
                  <SelectItem value="SCHEDULED">Programmé</SelectItem>
                  <SelectItem value="IN_PROGRESS">En cours</SelectItem>
                  <SelectItem value="COMPLETED">Terminé</SelectItem>
                  <SelectItem value="CANCELLED">Annulé</SelectItem>
                  <SelectItem value="NO_SHOW">Absent</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tous les types</SelectItem>
                  <SelectItem value="GENERAL">Générale</SelectItem>
                  <SelectItem value="EMERGENCY">Urgence</SelectItem>
                  <SelectItem value="FOLLOW_UP">Suivi</SelectItem>
                  <SelectItem value="SPECIALIST">Spécialiste</SelectItem>
                  <SelectItem value="TELEMEDICINE">Télémédecine</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-full md:w-48"
              />
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("");
                  setTypeFilter("");
                  setDateFilter("");
                }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Réinitialiser
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Liste des rendez-vous */}
        <Card>
          <CardHeader>
            <CardTitle>Rendez-vous ({appointments.length})</CardTitle>
            <CardDescription>
              Liste de tous les rendez-vous programmés
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Chargement des rendez-vous...
              </div>
            ) : appointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun rendez-vous trouvé
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter || typeFilter || dateFilter
                    ? "Aucun rendez-vous ne correspond à vos critères de recherche."
                    : "Commencez par programmer votre premier rendez-vous."}
                </p>
                <Button asChild>
                  <Link href="/dashboard/appointments/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Nouveau Rendez-vous
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {appointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src="" />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {getPatientInitials(
                            appointment.patient.firstName,
                            appointment.patient.lastName
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-gray-900">
                            {appointment.patient.firstName}{" "}
                            {appointment.patient.lastName}
                          </h3>
                          {getStatusBadge(appointment.status)}
                          {getTypeBadge(appointment.type)}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span className="font-mono">
                            {appointment.patient.patientNumber}
                          </span>
                          <span>
                            {calculateAge(appointment.patient.dateOfBirth)} ans
                          </span>
                          <span>
                            {formatGender(appointment.patient.gender)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDateTime(appointment.consultationDate)}
                          </div>
                          <div className="flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            Dr. {appointment.doctor.firstName}{" "}
                            {appointment.doctor.lastName}
                          </div>
                          {appointment.chiefComplaint && (
                            <div className="flex items-center">
                              <Stethoscope className="h-3 w-3 mr-1" />
                              {appointment.chiefComplaint}
                            </div>
                          )}
                        </div>
                        {(appointment.patient.phone ||
                          appointment.patient.email) && (
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                            {appointment.patient.phone && (
                              <div className="flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {appointment.patient.phone}
                              </div>
                            )}
                            {appointment.patient.email && (
                              <div className="flex items-center">
                                <Mail className="h-3 w-3 mr-1" />
                                {appointment.patient.email}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      {appointment.consultationFee && (
                        <div className="text-right text-sm">
                          <div className="text-gray-900 font-medium">
                            {appointment.consultationFee.toLocaleString()} FCFA
                          </div>
                          <div className="text-gray-500">
                            {appointment.duration
                              ? `${appointment.duration} min`
                              : "Durée non définie"}
                          </div>
                        </div>
                      )}

                      {/* Actions rapides pour changer le statut */}
                      {appointment.status === "PENDING_PAYMENT" && (
                        <div className="flex space-x-2">
                          <Button size="sm" variant="default" asChild>
                            <Link
                              href={`/dashboard/billing/payments/new?appointmentId=${appointment.id}&patientId=${appointment.patient.id}&consultationType=${appointment.type}`}
                            >
                              <CreditCard className="h-4 w-4 mr-1" />
                              Payer maintenant
                            </Link>
                          </Button>
                        </div>
                      )}

                      {appointment.status === "SCHEDULED" && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() =>
                              handleStartConsultation(appointment.id)
                            }
                          >
                            <Stethoscope className="h-4 w-4 mr-1" />
                            Consulter
                          </Button>
                        </div>
                      )}

                      {appointment.status === "IN_PROGRESS" && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleStatusUpdate(appointment.id, "COMPLETED")
                            }
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Terminer
                          </Button>
                        </div>
                      )}

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/appointments/${appointment.id}`}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Voir les détails
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/appointments/${appointment.id}/edit`}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Modifier
                            </Link>
                          </DropdownMenuItem>
                          {appointment.status === "SCHEDULED" && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() =>
                                  handleStatusUpdate(
                                    appointment.id,
                                    "CANCELLED"
                                  )
                                }
                              >
                                <X className="h-4 w-4 mr-2" />
                                Annuler
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
