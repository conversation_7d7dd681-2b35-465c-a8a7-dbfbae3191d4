"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  CreditCard,
  DollarSign,
  Receipt,
  Settings,
  TrendingUp,
  Users,
  Calendar,
  Plus,
} from "lucide-react";
import Link from "next/link";
import { getConsultationPrices, getPayments } from "@/lib/actions/billing";
import { ConsultationType, PaymentMethod } from "@prisma/client";

// Types pour les données
interface ConsultationPrice {
  id: string;
  consultationType: ConsultationType;
  basePrice: number;
  emergencyPrice?: number;
  insurancePrice?: number;
  description?: string;
}

interface Payment {
  id: string;
  paymentNumber: string;
  paymentDate: string;
  amount: number;
  paymentMethod: PaymentMethod;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
  };
  consultation?: {
    type: ConsultationType;
    consultationDate: string;
  };
}

// Mapping des types de consultation
const consultationTypeLabels: Record<ConsultationType, string> = {
  GENERAL: "Consultation générale",
  EMERGENCY: "Urgence",
  FOLLOW_UP: "Suivi",
  SPECIALIST: "Spécialiste",
  TELEMEDICINE: "Télémédecine",
};

// Mapping des modes de paiement
const paymentMethodLabels: Record<PaymentMethod, string> = {
  CASH: "Espèces",
  CARD: "Carte bancaire",
  MOBILE: "Paiement mobile",
  INSURANCE: "Assurance",
  CREDIT: "Crédit",
  BANK_TRANSFER: "Virement",
  CHECK: "Chèque",
};

export default function BillingPage() {
  const { data: session } = useSession();
  const [consultationPrices, setConsultationPrices] = useState<
    ConsultationPrice[]
  >([]);
  const [recentPayments, setRecentPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    todayRevenue: 0,
    monthRevenue: 0,
    totalPatients: 0,
    pendingPayments: 0,
  });

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Charger les tarifs
        const pricesResult = await getConsultationPrices();
        if (pricesResult.success) {
          setConsultationPrices(pricesResult.prices || []);
        }

        // Charger les paiements récents
        const paymentsResult = await getPayments();
        if (paymentsResult.success) {
          const payments = paymentsResult.payments || [];
          setRecentPayments(payments.slice(0, 10)); // 10 derniers paiements

          // Calculer les statistiques
          const today = new Date();
          const startOfMonth = new Date(
            today.getFullYear(),
            today.getMonth(),
            1
          );
          const startOfDay = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate()
          );

          const todayRevenue = payments
            .filter((p) => new Date(p.paymentDate) >= startOfDay)
            .reduce((sum, p) => sum + p.amount, 0);

          const monthRevenue = payments
            .filter((p) => new Date(p.paymentDate) >= startOfMonth)
            .reduce((sum, p) => sum + p.amount, 0);

          const uniquePatients = new Set(
            payments.map((p) => p.patient.patientNumber)
          ).size;

          setStats({
            todayRevenue,
            monthRevenue,
            totalPatients: uniquePatients,
            pendingPayments: 0, // À implémenter avec les consultations en attente
          });
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      loadData();
    }
  }, [session]);

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p>Chargement des données de facturation...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Facturation & Paiements
            </h1>
            <p className="text-gray-600 mt-2">
              Gestion des tarifs et suivi des paiements
            </p>
          </div>
          <div className="flex space-x-3">
            <Button asChild>
              <Link href="/dashboard/billing/prices">
                <Settings className="h-4 w-4 mr-2" />
                Gérer les tarifs
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/billing/payments/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau paiement
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Revenus du jour
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.todayRevenue.toLocaleString()} FCFA
              </div>
              <p className="text-xs text-muted-foreground">
                Paiements reçus aujourd'hui
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Revenus du mois
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.monthRevenue.toLocaleString()} FCFA
              </div>
              <p className="text-xs text-muted-foreground">
                Total du mois en cours
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Patients payants
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPatients}</div>
              <p className="text-xs text-muted-foreground">
                Patients ayant effectué un paiement
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Paiements en attente
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingPayments}</div>
              <p className="text-xs text-muted-foreground">
                Consultations non payées
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue="prices" className="space-y-6">
          <TabsList>
            <TabsTrigger value="prices">Grille tarifaire</TabsTrigger>
            <TabsTrigger value="payments">Paiements récents</TabsTrigger>
          </TabsList>

          {/* Grille tarifaire */}
          <TabsContent value="prices">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Receipt className="h-5 w-5 mr-2" />
                  Tarifs des consultations
                </CardTitle>
                <CardDescription>
                  Tarifs configurés pour chaque type de consultation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {consultationPrices.length === 0 ? (
                  <div className="text-center py-8">
                    <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun tarif configuré
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Configurez les tarifs pour commencer à facturer les
                      consultations.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/billing/prices">
                        Configurer les tarifs
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Type de consultation</TableHead>
                        <TableHead>Prix de base</TableHead>
                        <TableHead>Prix urgence</TableHead>
                        <TableHead>Prix assurance</TableHead>
                        <TableHead>Description</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {consultationPrices.map((price) => (
                        <TableRow key={price.id}>
                          <TableCell>
                            <Badge variant="outline">
                              {consultationTypeLabels[price.consultationType]}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium">
                            {price.basePrice.toLocaleString()} FCFA
                          </TableCell>
                          <TableCell>
                            {price.emergencyPrice
                              ? `${price.emergencyPrice.toLocaleString()} FCFA`
                              : "-"}
                          </TableCell>
                          <TableCell>
                            {price.insurancePrice
                              ? `${price.insurancePrice.toLocaleString()} FCFA`
                              : "-"}
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {price.description || "-"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Paiements récents */}
          <TabsContent value="payments">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Paiements récents
                </CardTitle>
                <CardDescription>
                  Les 10 derniers paiements reçus
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentPayments.length === 0 ? (
                  <div className="text-center py-8">
                    <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun paiement enregistré
                    </h3>
                    <p className="text-gray-600">
                      Les paiements apparaîtront ici une fois enregistrés.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Paiement</TableHead>
                        <TableHead>Patient</TableHead>
                        <TableHead>Montant</TableHead>
                        <TableHead>Mode</TableHead>
                        <TableHead>Type consultation</TableHead>
                        <TableHead>Date</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentPayments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-mono text-sm">
                            {payment.paymentNumber}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">
                                {payment.patient.firstName}{" "}
                                {payment.patient.lastName}
                              </p>
                              <p className="text-sm text-gray-500">
                                {payment.patient.patientNumber}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {payment.amount.toLocaleString()} FCFA
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {paymentMethodLabels[payment.paymentMethod]}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {payment.consultation ? (
                              <Badge variant="outline">
                                {
                                  consultationTypeLabels[
                                    payment.consultation.type
                                  ]
                                }
                              </Badge>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {new Date(payment.paymentDate).toLocaleDateString(
                              "fr-FR"
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
