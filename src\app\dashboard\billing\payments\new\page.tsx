"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, CreditCard, Search, User } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { createPayment, getConsultationPrice } from "@/lib/actions/billing";
import { getAllPatients } from "@/lib/actions/patients";
import {
  updateAppointmentStatus,
  getPendingPaymentAppointments,
} from "@/lib/actions/appointments";
import { ConsultationType, PaymentMethod } from "@prisma/client";

// Types
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  patientNumber: string;
  phone?: string | null;
  email?: string | null;
}

interface PendingAppointment {
  id: string;
  consultationDate: Date;
  type: ConsultationType;
  chiefComplaint?: string | null;
  consultationFee?: number | null;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string | null;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
  };
}

interface PaymentFormData {
  patientId: string;
  consultationType: ConsultationType;
  amount: string;
  paymentMethod: PaymentMethod;
  paymentReference: string;
  description: string;
  notes: string;
}

// Mapping des types de consultation
const consultationTypeLabels: Record<ConsultationType, string> = {
  GENERAL: "Consultation générale",
  EMERGENCY: "Urgence",
  FOLLOW_UP: "Suivi",
  SPECIALIST: "Spécialiste",
  TELEMEDICINE: "Télémédecine",
};

// Mapping des modes de paiement
const paymentMethodLabels: Record<PaymentMethod, string> = {
  CASH: "Espèces",
  CARD: "Carte bancaire",
  MOBILE: "Paiement mobile",
  INSURANCE: "Assurance",
  CREDIT: "Crédit",
  BANK_TRANSFER: "Virement",
  CHECK: "Chèque",
};

const consultationTypes = Object.keys(
  consultationTypeLabels
) as ConsultationType[];
const paymentMethods = Object.keys(paymentMethodLabels) as PaymentMethod[];

export default function NewPaymentPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [patientSearch, setPatientSearch] = useState("");
  const [pendingAppointments, setPendingAppointments] = useState<
    PendingAppointment[]
  >([]);
  const [showAllPatients, setShowAllPatients] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [suggestedAmount, setSuggestedAmount] = useState<number | null>(null);
  const [appointmentId, setAppointmentId] = useState<string | null>(null);

  const [formData, setFormData] = useState<PaymentFormData>({
    patientId: "",
    consultationType: "GENERAL",
    amount: "",
    paymentMethod: "CASH",
    paymentReference: "",
    description: "",
    notes: "",
  });

  // Charger les rendez-vous en attente de paiement
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Charger les rendez-vous en attente de paiement
        const appointmentsResult = await getPendingPaymentAppointments();
        if (appointmentsResult.success) {
          setPendingAppointments(appointmentsResult.appointments || []);
        }

        // Si on a besoin de tous les patients (mode manuel)
        if (showAllPatients) {
          const patientsResult = await getAllPatients();
          if (patientsResult.success) {
            setPatients(patientsResult.patients || []);
            setFilteredPatients(patientsResult.patients || []);
          }
        }
      } catch (error) {
        console.error("Erreur:", error);
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      loadData();
    }
  }, [session, showAllPatients]);

  // Pré-remplir avec les paramètres URL si fournis
  useEffect(() => {
    const patientId = searchParams.get("patientId");
    const consultationType = searchParams.get(
      "consultationType"
    ) as ConsultationType;
    const appointmentIdParam = searchParams.get("appointmentId");

    if (appointmentIdParam) {
      setAppointmentId(appointmentIdParam);
    }

    if (patientId) {
      setFormData((prev) => ({ ...prev, patientId }));
      const patient = patients.find((p) => p.id === patientId);
      if (patient) {
        setSelectedPatient(patient);
      }
    }

    if (consultationType && consultationTypes.includes(consultationType)) {
      setFormData((prev) => ({ ...prev, consultationType }));
    }
  }, [searchParams, patients]);

  // Filtrer les patients selon la recherche
  useEffect(() => {
    if (!patientSearch) {
      setFilteredPatients(patients);
    } else {
      const filtered = patients.filter(
        (patient) =>
          patient.firstName
            .toLowerCase()
            .includes(patientSearch.toLowerCase()) ||
          patient.lastName
            .toLowerCase()
            .includes(patientSearch.toLowerCase()) ||
          patient.patientNumber
            .toLowerCase()
            .includes(patientSearch.toLowerCase())
      );
      setFilteredPatients(filtered);
    }
  }, [patientSearch, patients]);

  // Récupérer le tarif suggéré quand le type de consultation change
  useEffect(() => {
    const loadSuggestedPrice = async () => {
      if (formData.consultationType) {
        try {
          const result = await getConsultationPrice(formData.consultationType);
          if (result.success && result.price) {
            setSuggestedAmount(result.price.basePrice);
            if (!formData.amount) {
              setFormData((prev) => ({
                ...prev,
                amount: result.price!.basePrice.toString(),
              }));
            }
          }
        } catch (error) {
          console.error("Erreur lors du chargement du tarif:", error);
        }
      }
    };

    loadSuggestedPrice();
  }, [formData.consultationType]);

  // Sélectionner un patient
  const selectPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setFormData((prev) => ({ ...prev, patientId: patient.id }));
    setPatientSearch("");
  };

  // Créer le paiement
  const handleSubmit = async () => {
    if (!formData.patientId) {
      toast.error("Veuillez sélectionner un patient");
      return;
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      toast.error("Veuillez saisir un montant valide");
      return;
    }

    setSaving(true);
    try {
      const paymentData = {
        patientId: formData.patientId,
        amount: parseFloat(formData.amount),
        paymentMethod: formData.paymentMethod,
        paymentReference: formData.paymentReference || undefined,
        description:
          formData.description ||
          `Paiement consultation ${
            consultationTypeLabels[formData.consultationType]
          }`,
        notes: formData.notes || undefined,
      };

      const result = await createPayment(paymentData);

      if (result.success) {
        // Si c'est un paiement pour un rendez-vous, mettre à jour le statut
        if (appointmentId) {
          try {
            await updateAppointmentStatus(appointmentId, "SCHEDULED" as any);
            toast.success("Paiement enregistré et rendez-vous confirmé");
            router.push("/dashboard/appointments");
          } catch (error) {
            console.error(
              "Erreur lors de la mise à jour du rendez-vous:",
              error
            );
            toast.success(
              "Paiement enregistré, mais erreur lors de la confirmation du rendez-vous"
            );
            router.push("/dashboard/billing");
          }
        } else {
          toast.success("Paiement enregistré avec succès");
          router.push("/dashboard/billing");
        }
      } else {
        toast.error(
          result.error || "Erreur lors de l'enregistrement du paiement"
        );
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de l'enregistrement du paiement");
    } finally {
      setSaving(false);
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/billing">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {appointmentId ? "Paiement de consultation" : "Nouveau paiement"}
            </h1>
            <p className="text-gray-600 mt-2">
              {appointmentId
                ? "Payer pour confirmer le rendez-vous"
                : "Enregistrer un paiement de consultation"}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sélection du patient */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Rendez-vous à payer
              </CardTitle>
              <CardDescription>
                Sélectionnez un rendez-vous en attente de paiement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedPatient ? (
                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">
                        {selectedPatient.firstName} {selectedPatient.lastName}
                      </h3>
                      <p className="text-sm text-gray-600">
                        N° {selectedPatient.patientNumber}
                      </p>
                      {selectedPatient.phone && (
                        <p className="text-sm text-gray-600">
                          {selectedPatient.phone}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedPatient(null);
                        setFormData((prev) => ({ ...prev, patientId: "" }));
                      }}
                    >
                      Changer
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div>
                    <Label htmlFor="patientSearch">Rechercher un patient</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="patientSearch"
                        value={patientSearch}
                        onChange={(e) => setPatientSearch(e.target.value)}
                        placeholder="Nom, prénom ou numéro patient..."
                        className="pl-10"
                      />
                    </div>
                  </div>

                  {loading ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                  ) : (
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {/* Afficher les rendez-vous en attente de paiement */}
                      {pendingAppointments.length === 0 ? (
                        <div className="text-center text-gray-500 py-4">
                          <p>Aucun rendez-vous en attente de paiement</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => setShowAllPatients(true)}
                          >
                            Paiement manuel
                          </Button>
                        </div>
                      ) : (
                        pendingAppointments
                          .filter((appointment) => {
                            if (!patientSearch) return true;
                            const patient = appointment.patient;
                            return (
                              patient.firstName
                                .toLowerCase()
                                .includes(patientSearch.toLowerCase()) ||
                              patient.lastName
                                .toLowerCase()
                                .includes(patientSearch.toLowerCase()) ||
                              patient.patientNumber
                                .toLowerCase()
                                .includes(patientSearch.toLowerCase())
                            );
                          })
                          .slice(0, 10)
                          .map((appointment) => (
                            <div
                              key={appointment.id}
                              className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                              onClick={() => {
                                selectPatient({
                                  id: appointment.patient.id,
                                  firstName: appointment.patient.firstName,
                                  lastName: appointment.patient.lastName,
                                  patientNumber:
                                    appointment.patient.patientNumber,
                                  phone: appointment.patient.phone,
                                  email: null,
                                });
                                setAppointmentId(appointment.id);
                                setFormData((prev) => ({
                                  ...prev,
                                  consultationType: appointment.type,
                                  amount:
                                    appointment.consultationFee?.toString() ||
                                    "",
                                }));
                              }}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium">
                                    {appointment.patient.firstName}{" "}
                                    {appointment.patient.lastName}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    N° {appointment.patient.patientNumber}
                                  </p>
                                  <p className="text-xs text-blue-600">
                                    {consultationTypeLabels[appointment.type]} -{" "}
                                    {new Date(
                                      appointment.consultationDate
                                    ).toLocaleDateString("fr-FR")}{" "}
                                    {new Date(
                                      appointment.consultationDate
                                    ).toLocaleTimeString("fr-FR", {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </p>
                                  {appointment.consultationFee && (
                                    <p className="text-xs text-green-600 font-medium">
                                      {appointment.consultationFee.toLocaleString()}{" "}
                                      FCFA
                                    </p>
                                  )}
                                </div>
                                <Button variant="ghost" size="sm">
                                  Payer
                                </Button>
                              </div>
                            </div>
                          ))
                      )}

                      {/* Afficher les patients si mode manuel activé */}
                      {showAllPatients && (
                        <>
                          <div className="border-t pt-4 mt-4">
                            <p className="text-sm text-gray-600 mb-2">
                              Paiement manuel (tous les patients) :
                            </p>
                            {filteredPatients.length === 0 ? (
                              <p className="text-center text-gray-500 py-4">
                                {patientSearch
                                  ? "Aucun patient trouvé"
                                  : "Aucun patient"}
                              </p>
                            ) : (
                              filteredPatients.slice(0, 10).map((patient) => (
                                <div
                                  key={patient.id}
                                  className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 mb-2"
                                  onClick={() => selectPatient(patient)}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <p className="font-medium">
                                        {patient.firstName} {patient.lastName}
                                      </p>
                                      <p className="text-sm text-gray-600">
                                        N° {patient.patientNumber}
                                      </p>
                                    </div>
                                    <Button variant="ghost" size="sm">
                                      Sélectionner
                                    </Button>
                                  </div>
                                </div>
                              ))
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>

          {/* Formulaire de paiement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Informations de paiement
              </CardTitle>
              <CardDescription>
                Détails du paiement à enregistrer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="consultationType">Type de consultation</Label>
                <Select
                  value={formData.consultationType}
                  onValueChange={(value: ConsultationType) =>
                    setFormData((prev) => ({
                      ...prev,
                      consultationType: value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {consultationTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {consultationTypeLabels[type]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="amount">Montant (FCFA) *</Label>
                <Input
                  id="amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, amount: e.target.value }))
                  }
                  placeholder="Ex: 5000"
                  min="0"
                  step="100"
                />
                {suggestedAmount && (
                  <p className="text-sm text-gray-600 mt-1">
                    Tarif suggéré: {suggestedAmount.toLocaleString()} FCFA
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="paymentMethod">Mode de paiement</Label>
                <Select
                  value={formData.paymentMethod}
                  onValueChange={(value: PaymentMethod) =>
                    setFormData((prev) => ({ ...prev, paymentMethod: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method} value={method}>
                        {paymentMethodLabels[method]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {(formData.paymentMethod === "MOBILE" ||
                formData.paymentMethod === "CARD") && (
                <div>
                  <Label htmlFor="paymentReference">
                    Référence de transaction
                  </Label>
                  <Input
                    id="paymentReference"
                    value={formData.paymentReference}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        paymentReference: e.target.value,
                      }))
                    }
                    placeholder="Ex: TXN123456789"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Description du paiement..."
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, notes: e.target.value }))
                  }
                  placeholder="Notes additionnelles..."
                  rows={3}
                />
              </div>

              <Button
                onClick={handleSubmit}
                disabled={saving || !selectedPatient}
                className="w-full"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <CreditCard className="h-4 w-4 mr-2" />
                )}
                Enregistrer le paiement
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
