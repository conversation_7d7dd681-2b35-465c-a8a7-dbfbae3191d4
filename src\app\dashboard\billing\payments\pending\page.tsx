"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Calendar,
  Clock,
  CreditCard,
  Stethoscope,
  User,
  Phone,
  FileText,
  Loader2,
} from "lucide-react";
import { getPendingPaymentAppointments } from "@/lib/actions/appointments";
import { ConsultationType } from "@prisma/client";

// Types
interface PendingAppointment {
  id: string;
  consultationDate: string;
  type: ConsultationType;
  chiefComplaint?: string;
  consultationFee?: number;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
  };
}

const consultationTypeLabels: Record<ConsultationType, string> = {
  GENERAL: "Consultation générale",
  SPECIALIST: "Spécialiste",
  EMERGENCY: "Urgence",
  FOLLOW_UP: "Suivi",
  TELEMEDICINE: "Télémédecine",
};

export default function PendingPaymentsPage() {
  const router = useRouter();
  const [appointments, setAppointments] = useState<PendingAppointment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPendingAppointments();
  }, []);

  const loadPendingAppointments = async () => {
    try {
      const result = await getPendingPaymentAppointments();
      if (result.success) {
        setAppointments(result.appointments || []);
      } else {
        toast.error(result.error || "Erreur lors du chargement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des rendez-vous");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/billing">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Rendez-vous en attente de paiement
            </h1>
            <p className="text-gray-600 mt-2">
              {appointments.length} rendez-vous en attente de paiement
            </p>
          </div>
        </div>
      </div>

      {/* Liste des rendez-vous en attente */}
      {appointments.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CreditCard className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucun rendez-vous en attente de paiement
            </h3>
            <p className="text-gray-500 text-center">
              Tous les rendez-vous ont été payés ou aucun rendez-vous n'est programmé.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {appointments.map((appointment) => (
            <Card key={appointment.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="bg-orange-100 p-2 rounded-lg">
                      <Calendar className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">
                        {appointment.patient.firstName} {appointment.patient.lastName}
                      </CardTitle>
                      <CardDescription>
                        Patient #{appointment.patient.patientNumber}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                    En attente de paiement
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Informations du rendez-vous */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {formatDate(appointment.consultationDate)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {formatTime(appointment.consultationDate)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Stethoscope className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {consultationTypeLabels[appointment.type]}
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        Dr. {appointment.doctor.firstName} {appointment.doctor.lastName}
                      </span>
                    </div>
                    {appointment.patient.phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{appointment.patient.phone}</span>
                      </div>
                    )}
                    {appointment.chiefComplaint && (
                      <div className="flex items-start space-x-3">
                        <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                        <span className="text-sm">{appointment.chiefComplaint}</span>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col justify-center space-y-4">
                    {appointment.consultationFee && (
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Montant à payer</p>
                        <p className="text-2xl font-bold text-green-600">
                          {appointment.consultationFee.toLocaleString()} FCFA
                        </p>
                      </div>
                    )}
                    <Button size="lg" className="w-full" asChild>
                      <Link
                        href={`/dashboard/billing/payments/new?appointmentId=${appointment.id}&patientId=${appointment.patient.id}&consultationType=${appointment.type}`}
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Effectuer le paiement
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
