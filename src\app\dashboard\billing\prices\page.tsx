"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Save, Settings, Trash2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  getConsultationPrices,
  upsertConsultationPrice,
} from "@/lib/actions/billing";
import { ConsultationType } from "@prisma/client";

// Types
interface ConsultationPrice {
  id: string;
  consultationType: ConsultationType;
  basePrice: number;
  emergencyPrice?: number;
  insurancePrice?: number;
  description?: string;
}

interface PriceFormData {
  consultationType: ConsultationType;
  basePrice: string;
  emergencyPrice: string;
  insurancePrice: string;
  description: string;
}

// Mapping des types de consultation
const consultationTypeLabels: Record<ConsultationType, string> = {
  GENERAL: "Consultation générale",
  EMERGENCY: "Urgence",
  FOLLOW_UP: "Suivi",
  SPECIALIST: "Spécialiste",
  TELEMEDICINE: "Télémédecine",
};

const consultationTypes = Object.keys(
  consultationTypeLabels
) as ConsultationType[];

export default function ConsultationPricesPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [consultationPrices, setConsultationPrices] = useState<
    ConsultationPrice[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingPrice, setEditingPrice] = useState<ConsultationPrice | null>(
    null
  );
  const [formData, setFormData] = useState<PriceFormData>({
    consultationType: "GENERAL",
    basePrice: "",
    emergencyPrice: "",
    insurancePrice: "",
    description: "",
  });

  // Charger les tarifs
  useEffect(() => {
    const loadPrices = async () => {
      try {
        setLoading(true);
        const result = await getConsultationPrices();
        if (result.success) {
          setConsultationPrices(result.prices || []);
        } else {
          toast.error("Erreur lors du chargement des tarifs");
        }
      } catch (error) {
        console.error("Erreur:", error);
        toast.error("Erreur lors du chargement des tarifs");
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      loadPrices();
    }
  }, [session]);

  // Démarrer l'édition d'un tarif
  const startEditing = (price: ConsultationPrice) => {
    setEditingPrice(price);
    setFormData({
      consultationType: price.consultationType,
      basePrice: price.basePrice.toString(),
      emergencyPrice: price.emergencyPrice?.toString() || "",
      insurancePrice: price.insurancePrice?.toString() || "",
      description: price.description || "",
    });
  };

  // Démarrer la création d'un nouveau tarif
  const startCreating = () => {
    setEditingPrice(null);
    setFormData({
      consultationType: "GENERAL",
      basePrice: "",
      emergencyPrice: "",
      insurancePrice: "",
      description: "",
    });
  };

  // Annuler l'édition
  const cancelEditing = () => {
    setEditingPrice(null);
    setFormData({
      consultationType: "GENERAL",
      basePrice: "",
      emergencyPrice: "",
      insurancePrice: "",
      description: "",
    });
  };

  // Sauvegarder un tarif
  const handleSave = async () => {
    if (!formData.basePrice) {
      toast.error("Le prix de base est obligatoire");
      return;
    }

    setSaving(true);
    try {
      const data = {
        consultationType: formData.consultationType,
        basePrice: parseFloat(formData.basePrice),
        emergencyPrice: formData.emergencyPrice
          ? parseFloat(formData.emergencyPrice)
          : undefined,
        insurancePrice: formData.insurancePrice
          ? parseFloat(formData.insurancePrice)
          : undefined,
        description: formData.description || undefined,
      };

      const result = await upsertConsultationPrice(data);

      if (result.success) {
        toast.success(editingPrice ? "Tarif mis à jour" : "Tarif créé");

        // Recharger les tarifs
        const pricesResult = await getConsultationPrices();
        if (pricesResult.success) {
          setConsultationPrices(pricesResult.prices || []);
        }

        cancelEditing();
      } else {
        toast.error(result.error || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/billing">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Gestion des tarifs
              </h1>
              <p className="text-gray-600 mt-2">
                Configurez les tarifs pour chaque type de consultation
              </p>
            </div>
          </div>
          <Button onClick={startCreating}>
            <Settings className="h-4 w-4 mr-2" />
            Nouveau tarif
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Formulaire */}
          <Card>
            <CardHeader>
              <CardTitle>
                {editingPrice ? "Modifier le tarif" : "Nouveau tarif"}
              </CardTitle>
              <CardDescription>
                {editingPrice
                  ? "Modifiez les informations du tarif sélectionné"
                  : "Créez un nouveau tarif de consultation"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="consultationType">Type de consultation</Label>
                <Select
                  value={formData.consultationType}
                  onValueChange={(value: ConsultationType) =>
                    setFormData((prev) => ({
                      ...prev,
                      consultationType: value,
                    }))
                  }
                  disabled={!!editingPrice}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {consultationTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {consultationTypeLabels[type]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="basePrice">Prix de base (FCFA) *</Label>
                <Input
                  id="basePrice"
                  type="number"
                  value={formData.basePrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      basePrice: e.target.value,
                    }))
                  }
                  placeholder="Ex: 5000"
                  min="0"
                  step="100"
                />
              </div>

              <div>
                <Label htmlFor="emergencyPrice">Prix d'urgence (FCFA)</Label>
                <Input
                  id="emergencyPrice"
                  type="number"
                  value={formData.emergencyPrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      emergencyPrice: e.target.value,
                    }))
                  }
                  placeholder="Ex: 10000"
                  min="0"
                  step="100"
                />
              </div>

              <div>
                <Label htmlFor="insurancePrice">Prix assurance (FCFA)</Label>
                <Input
                  id="insurancePrice"
                  type="number"
                  value={formData.insurancePrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      insurancePrice: e.target.value,
                    }))
                  }
                  placeholder="Ex: 3000"
                  min="0"
                  step="100"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Description du tarif..."
                  rows={3}
                />
              </div>

              <div className="flex space-x-3">
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {editingPrice ? "Mettre à jour" : "Créer"}
                </Button>
                {editingPrice && (
                  <Button variant="outline" onClick={cancelEditing}>
                    Annuler
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Liste des tarifs */}
          <Card>
            <CardHeader>
              <CardTitle>Tarifs configurés</CardTitle>
              <CardDescription>
                Liste des tarifs actuellement configurés
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p>Chargement des tarifs...</p>
                </div>
              ) : consultationPrices.length === 0 ? (
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun tarif configuré
                  </h3>
                  <p className="text-gray-600">
                    Créez votre premier tarif pour commencer.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {consultationPrices.map((price) => (
                    <div
                      key={price.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        editingPrice?.id === price.id
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => startEditing(price)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">
                          {consultationTypeLabels[price.consultationType]}
                        </Badge>
                        <span className="font-semibold text-lg">
                          {price.basePrice.toLocaleString()} FCFA
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Urgence:</span>{" "}
                          {price.emergencyPrice
                            ? `${price.emergencyPrice.toLocaleString()} FCFA`
                            : "Non défini"}
                        </div>
                        <div>
                          <span className="font-medium">Assurance:</span>{" "}
                          {price.insurancePrice
                            ? `${price.insurancePrice.toLocaleString()} FCFA`
                            : "Non défini"}
                        </div>
                      </div>

                      {price.description && (
                        <p className="text-sm text-gray-600 mt-2">
                          {price.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
