"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Save,
  CheckCircle,
  Clock,
  User,
  Stethoscope,
  Heart,
  Thermometer,
  Weight,
  Ruler,
  Activity,
  FileText,
  Pill,
  TestTube,
  Phone,
  Mail,
  AlertCircle,
  Loader2,
  Edit,
  History,
} from "lucide-react";
import Link from "next/link";
import {
  getConsultationById,
  updateConsultation,
  completeConsultation,
} from "@/lib/actions/consultations";
import {
  calculateAge,
  formatGender,
  getPatientInitials,
} from "@/lib/utils/patient-utils";

// Types pour la consultation
interface VitalSigns {
  weight?: number;
  height?: number;
  bloodPressure?: string;
  temperature?: number;
  heartRate?: number;
}

interface ConsultationData {
  id: string;
  consultationDate: string;
  status: string;
  type: string;
  chiefComplaint?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  consultationFee?: number;
  duration?: number;
  weight?: number;
  height?: number;
  bloodPressure?: string;
  temperature?: number;
  heartRate?: number;
  paymentStatus?: string;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
    dateOfBirth: string;
    gender: string;
    bloodType?: string;
    consultations: Array<{
      id: string;
      consultationDate: string;
      diagnosis?: string;
      doctor: {
        firstName: string;
        lastName: string;
      };
    }>;
    prescriptions: Array<{
      id: string;
      prescriptionDate: string;
      doctor: {
        firstName: string;
        lastName: string;
      };
    }>;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
    phone?: string;
    email?: string;
  };
}

export default function ConsultationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [consultation, setConsultation] = useState<ConsultationData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // États pour les formulaires
  const [vitalSigns, setVitalSigns] = useState<VitalSigns>({});
  const [clinicalData, setClinicalData] = useState({
    symptoms: "",
    diagnosis: "",
    treatment: "",
    notes: "",
    duration: "",
  });

  // Charger les données de la consultation
  useEffect(() => {
    const loadConsultation = async () => {
      if (!params.id) return;

      try {
        console.log("🔄 Chargement de la consultation:", params.id);
        const result = await getConsultationById(params.id as string);

        if (result.success && result.consultation) {
          console.log("✅ Consultation chargée:", result.consultation);
          setConsultation(result.consultation as any);

          // Pré-remplir les formulaires avec les données existantes
          setVitalSigns({
            weight: result.consultation.weight || undefined,
            height: result.consultation.height || undefined,
            bloodPressure: result.consultation.bloodPressure || "",
            temperature: result.consultation.temperature || undefined,
            heartRate: result.consultation.heartRate || undefined,
          });

          setClinicalData({
            symptoms: result.consultation.symptoms || "",
            diagnosis: result.consultation.diagnosis || "",
            treatment: result.consultation.treatment || "",
            notes: result.consultation.notes || "",
            duration: result.consultation.duration?.toString() || "",
          });
        } else {
          console.error("❌ Erreur lors du chargement:", result.error);
          router.push("/dashboard/consultations");
        }
      } catch (error) {
        console.error("💥 Exception lors du chargement:", error);
        router.push("/dashboard/consultations");
      } finally {
        setLoading(false);
      }
    };

    loadConsultation();
  }, [params.id, router]);

  // Sauvegarder les modifications
  const handleSave = async (overrideData?: any) => {
    if (!consultation) return;

    setSaving(true);
    try {
      const updateData = {
        ...clinicalData,
        vitalSigns,
        duration: clinicalData.duration
          ? parseInt(clinicalData.duration)
          : undefined,
        ...overrideData, // Permet de surcharger des données (ex: status)
      };

      const result = await updateConsultation(consultation.id, updateData);

      if (result.success) {
        console.log("✅ Consultation mise à jour");
        // Recharger les données
        const refreshResult = await getConsultationById(consultation.id);
        if (refreshResult.success && refreshResult.consultation) {
          setConsultation(refreshResult.consultation as any);
        }
      } else {
        console.error("❌ Erreur lors de la sauvegarde:", result.error);
        alert("Erreur lors de la sauvegarde: " + result.error);
      }
    } catch (error) {
      console.error("💥 Exception lors de la sauvegarde:", error);
      alert("Une erreur est survenue lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  // Terminer la consultation
  const handleComplete = async () => {
    if (!consultation) return;

    if (!clinicalData.diagnosis.trim()) {
      alert("Veuillez saisir un diagnostic avant de terminer la consultation");
      return;
    }

    setSaving(true);
    try {
      const finalData = {
        ...clinicalData,
        vitalSigns,
        duration: clinicalData.duration
          ? parseInt(clinicalData.duration)
          : undefined,
      };

      const result = await completeConsultation(consultation.id, finalData);

      if (result.success) {
        console.log("✅ Consultation terminée");
        router.push("/dashboard/consultations");
      } else {
        console.error("❌ Erreur lors de la finalisation:", result.error);
        alert("Erreur lors de la finalisation: " + result.error);
      }
    } catch (error) {
      console.error("💥 Exception lors de la finalisation:", error);
      alert("Une erreur est survenue lors de la finalisation");
    } finally {
      setSaving(false);
    }
  };

  // Obtenir la couleur du badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SCHEDULED: {
        label: "Programmée",
        className: "bg-blue-100 text-blue-800",
      },
      IN_PROGRESS: {
        label: "En cours",
        className: "bg-yellow-100 text-yellow-800",
      },
      COMPLETED: {
        label: "Terminée",
        className: "bg-green-100 text-green-800",
      },
      CANCELLED: { label: "Annulée", className: "bg-red-100 text-red-800" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] ||
      statusConfig.SCHEDULED;
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Formater la date et l'heure
  const formatDateTime = (dateInput: string | Date) => {
    const date =
      typeof dateInput === "string" ? new Date(dateInput) : dateInput;
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    const timeStr = date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (isToday) {
      return `Aujourd'hui à ${timeStr}`;
    } else {
      return `${date.toLocaleDateString("fr-FR")} à ${timeStr}`;
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement de la consultation...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!consultation) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Consultation non trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Cette consultation n'existe pas ou vous n'avez pas les
                permissions pour y accéder.
              </p>
              <Button asChild>
                <Link href="/dashboard/consultations">
                  Retour aux consultations
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/consultations">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Consultation Médicale
              </h1>
              <p className="text-gray-600 mt-2">
                {consultation.patient.firstName} {consultation.patient.lastName}{" "}
                • {formatDateTime(consultation.consultationDate)}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(consultation.status)}

            {/* Boutons pour consultation en cours */}
            {consultation.status === "IN_PROGRESS" && (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleSave}
                  disabled={saving}
                >
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Sauvegarder
                </Button>
                <Button onClick={handleComplete} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Terminer
                </Button>
              </div>
            )}

            {/* Boutons pour consultation terminée */}
            {consultation.status === "COMPLETED" && (
              <div className="flex space-x-2">
                <Button variant="outline" asChild>
                  <Link
                    href={`/dashboard/prescriptions/new?consultationId=${consultation.id}&patientId=${consultation.patient.id}`}
                  >
                    <Pill className="h-4 w-4 mr-2" />
                    Créer Prescription
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link
                    href={`/dashboard/laboratory/orders/new?consultationId=${consultation.id}&patientId=${consultation.patient.id}`}
                  >
                    <TestTube className="h-4 w-4 mr-2" />
                    Prescrire Examens
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    // Réouvrir la consultation pour modifications
                    handleSave({ status: "IN_PROGRESS" });
                  }}
                  disabled={saving}
                >
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Edit className="h-4 w-4 mr-2" />
                  )}
                  Modifier
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Informations du patient */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Informations du Patient
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start space-x-6">
              <Avatar className="h-16 w-16">
                <AvatarImage src="" />
                <AvatarFallback className="bg-blue-100 text-blue-600 text-lg">
                  {getPatientInitials(
                    consultation.patient.firstName,
                    consultation.patient.lastName
                  )}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">
                      {consultation.patient.firstName}{" "}
                      {consultation.patient.lastName}
                    </h3>
                    <p className="text-gray-600 font-mono">
                      {consultation.patient.patientNumber}
                    </p>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="text-sm text-gray-500">
                        {calculateAge(consultation.patient.dateOfBirth)} ans
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatGender(consultation.patient.gender)}
                      </span>
                      {consultation.patient.bloodType && (
                        <Badge
                          variant="outline"
                          className="bg-red-50 text-red-700"
                        >
                          {consultation.patient.bloodType}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Contact</h4>
                    {consultation.patient.phone && (
                      <div className="flex items-center text-sm text-gray-600 mb-1">
                        <Phone className="h-3 w-3 mr-2" />
                        {consultation.patient.phone}
                      </div>
                    )}
                    {consultation.patient.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-3 w-3 mr-2" />
                        {consultation.patient.email}
                      </div>
                    )}
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Médecin</h4>
                    <p className="text-sm text-gray-600">
                      Dr. {consultation.doctor.firstName}{" "}
                      {consultation.doctor.lastName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {consultation.doctor.role === "DOCTOR"
                        ? "Médecin"
                        : "Administrateur"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interface de consultation avec onglets */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="vitals">Données vitales</TabsTrigger>
            <TabsTrigger value="clinical">Examen clinique</TabsTrigger>
            <TabsTrigger value="history">Historique</TabsTrigger>
          </TabsList>

          {/* Onglet Vue d'ensemble */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Motif de consultation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Motif de Consultation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {consultation.chiefComplaint ? (
                    <p className="text-gray-700">
                      {consultation.chiefComplaint}
                    </p>
                  ) : (
                    <p className="text-gray-500 italic">Aucun motif spécifié</p>
                  )}
                </CardContent>
              </Card>

              {/* Résumé des données vitales */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    Données Vitales
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    {consultation.weight && (
                      <div className="flex items-center">
                        <Weight className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="text-sm">
                          <span className="font-medium">
                            {consultation.weight}
                          </span>{" "}
                          kg
                        </span>
                      </div>
                    )}
                    {consultation.height && (
                      <div className="flex items-center">
                        <Ruler className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="text-sm">
                          <span className="font-medium">
                            {consultation.height}
                          </span>{" "}
                          cm
                        </span>
                      </div>
                    )}
                    {consultation.bloodPressure && (
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="text-sm">
                          <span className="font-medium">
                            {consultation.bloodPressure}
                          </span>
                        </span>
                      </div>
                    )}
                    {consultation.temperature && (
                      <div className="flex items-center">
                        <Thermometer className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="text-sm">
                          <span className="font-medium">
                            {consultation.temperature}
                          </span>
                          °C
                        </span>
                      </div>
                    )}
                    {consultation.heartRate && (
                      <div className="flex items-center">
                        <Activity className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="text-sm">
                          <span className="font-medium">
                            {consultation.heartRate}
                          </span>{" "}
                          bpm
                        </span>
                      </div>
                    )}
                  </div>
                  {!consultation.weight &&
                    !consultation.height &&
                    !consultation.bloodPressure &&
                    !consultation.temperature &&
                    !consultation.heartRate && (
                      <p className="text-gray-500 italic text-sm">
                        Aucune donnée vitale enregistrée
                      </p>
                    )}
                </CardContent>
              </Card>

              {/* Diagnostic */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Stethoscope className="h-5 w-5 mr-2" />
                    Diagnostic
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {consultation.diagnosis ? (
                    <p className="text-gray-700">{consultation.diagnosis}</p>
                  ) : (
                    <p className="text-gray-500 italic">
                      Diagnostic en cours...
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Traitement */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Pill className="h-5 w-5 mr-2" />
                    Traitement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {consultation.treatment ? (
                    <p className="text-gray-700">{consultation.treatment}</p>
                  ) : (
                    <p className="text-gray-500 italic">Traitement à définir</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Notes */}
            {consultation.notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Notes Médicales
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {consultation.notes}
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Onglet Données vitales */}
          <TabsContent value="vitals" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Saisie des Données Vitales
                </CardTitle>
                <CardDescription>
                  Enregistrez les mesures vitales du patient
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="weight" className="flex items-center">
                      <Weight className="h-4 w-4 mr-2" />
                      Poids (kg)
                    </Label>
                    <Input
                      id="weight"
                      type="number"
                      placeholder="Ex: 70"
                      value={vitalSigns.weight || ""}
                      onChange={(e) =>
                        setVitalSigns((prev) => ({
                          ...prev,
                          weight: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        }))
                      }
                      min="0"
                      step="0.1"
                      disabled={consultation.status === "COMPLETED"}
                    />
                  </div>

                  <div>
                    <Label htmlFor="height" className="flex items-center">
                      <Ruler className="h-4 w-4 mr-2" />
                      Taille (cm)
                    </Label>
                    <Input
                      id="height"
                      type="number"
                      placeholder="Ex: 175"
                      value={vitalSigns.height || ""}
                      onChange={(e) =>
                        setVitalSigns((prev) => ({
                          ...prev,
                          height: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        }))
                      }
                      min="0"
                      step="0.1"
                      disabled={consultation.status === "COMPLETED"}
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="bloodPressure"
                      className="flex items-center"
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      Tension artérielle
                    </Label>
                    <Input
                      id="bloodPressure"
                      placeholder="Ex: 120/80"
                      value={vitalSigns.bloodPressure || ""}
                      onChange={(e) =>
                        setVitalSigns((prev) => ({
                          ...prev,
                          bloodPressure: e.target.value,
                        }))
                      }
                      disabled={consultation.status === "COMPLETED"}
                    />
                  </div>

                  <div>
                    <Label htmlFor="temperature" className="flex items-center">
                      <Thermometer className="h-4 w-4 mr-2" />
                      Température (°C)
                    </Label>
                    <Input
                      id="temperature"
                      type="number"
                      placeholder="Ex: 37.2"
                      value={vitalSigns.temperature || ""}
                      onChange={(e) =>
                        setVitalSigns((prev) => ({
                          ...prev,
                          temperature: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        }))
                      }
                      min="30"
                      max="45"
                      step="0.1"
                      disabled={consultation.status === "COMPLETED"}
                    />
                  </div>

                  <div>
                    <Label htmlFor="heartRate" className="flex items-center">
                      <Activity className="h-4 w-4 mr-2" />
                      Rythme cardiaque (bpm)
                    </Label>
                    <Input
                      id="heartRate"
                      type="number"
                      placeholder="Ex: 72"
                      value={vitalSigns.heartRate || ""}
                      onChange={(e) =>
                        setVitalSigns((prev) => ({
                          ...prev,
                          heartRate: e.target.value
                            ? parseInt(e.target.value)
                            : undefined,
                        }))
                      }
                      min="30"
                      max="200"
                      disabled={consultation.status === "COMPLETED"}
                    />
                  </div>

                  <div>
                    <Label htmlFor="duration" className="flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      Durée consultation (min)
                    </Label>
                    <Select
                      value={clinicalData.duration}
                      onValueChange={(value) =>
                        setClinicalData((prev) => ({
                          ...prev,
                          duration: value,
                        }))
                      }
                      disabled={consultation.status === "COMPLETED"}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="45">45 minutes</SelectItem>
                        <SelectItem value="60">1 heure</SelectItem>
                        <SelectItem value="90">1h30</SelectItem>
                        <SelectItem value="120">2 heures</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Calculs automatiques */}
                {vitalSigns.weight && vitalSigns.height && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">
                      Calculs automatiques
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700">IMC:</span>
                        <span className="ml-2 font-medium">
                          {(
                            vitalSigns.weight /
                            Math.pow(vitalSigns.height / 100, 2)
                          ).toFixed(1)}{" "}
                          kg/m²
                        </span>
                      </div>
                      <div>
                        <span className="text-blue-700">
                          Surface corporelle:
                        </span>
                        <span className="ml-2 font-medium">
                          {Math.sqrt(
                            (vitalSigns.weight * vitalSigns.height) / 3600
                          ).toFixed(2)}{" "}
                          m²
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Onglet Examen clinique */}
          <TabsContent value="clinical" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Symptômes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Décrivez les symptômes observés..."
                    value={clinicalData.symptoms}
                    onChange={(e) =>
                      setClinicalData((prev) => ({
                        ...prev,
                        symptoms: e.target.value,
                      }))
                    }
                    rows={4}
                    disabled={consultation.status === "COMPLETED"}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Stethoscope className="h-5 w-5 mr-2" />
                    Diagnostic
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Saisissez le diagnostic..."
                    value={clinicalData.diagnosis}
                    onChange={(e) =>
                      setClinicalData((prev) => ({
                        ...prev,
                        diagnosis: e.target.value,
                      }))
                    }
                    rows={4}
                    disabled={consultation.status === "COMPLETED"}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Pill className="h-5 w-5 mr-2" />
                    Traitement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Décrivez le traitement prescrit..."
                    value={clinicalData.treatment}
                    onChange={(e) =>
                      setClinicalData((prev) => ({
                        ...prev,
                        treatment: e.target.value,
                      }))
                    }
                    rows={4}
                    disabled={consultation.status === "COMPLETED"}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Notes Médicales
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Notes additionnelles..."
                    value={clinicalData.notes}
                    onChange={(e) =>
                      setClinicalData((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    rows={4}
                    disabled={consultation.status === "COMPLETED"}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Onglet Historique */}
          <TabsContent value="history" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Consultations précédentes */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <History className="h-5 w-5 mr-2" />
                    Consultations Précédentes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {consultation.patient.consultations.length > 0 ? (
                    <div className="space-y-4">
                      {consultation.patient.consultations.map(
                        (pastConsultation) => (
                          <div
                            key={pastConsultation.id}
                            className="border-l-4 border-blue-200 pl-4"
                          >
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900">
                                {formatDateTime(
                                  pastConsultation.consultationDate
                                )}
                              </p>
                              <p className="text-xs text-gray-500">
                                Dr. {pastConsultation.doctor.firstName}{" "}
                                {pastConsultation.doctor.lastName}
                              </p>
                            </div>
                            {pastConsultation.diagnosis && (
                              <p className="text-sm text-gray-600 mt-1">
                                {pastConsultation.diagnosis}
                              </p>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic text-sm">
                      Aucune consultation précédente
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Prescriptions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Pill className="h-5 w-5 mr-2" />
                    Prescriptions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {consultation.patient.prescriptions.length > 0 ? (
                    <div className="space-y-4">
                      {consultation.patient.prescriptions.map(
                        (prescription) => (
                          <div
                            key={prescription.id}
                            className="border-l-4 border-green-200 pl-4"
                          >
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900">
                                {formatDateTime(prescription.prescriptionDate)}
                              </p>
                              <p className="text-xs text-gray-500">
                                Dr. {prescription.doctor.firstName}{" "}
                                {prescription.doctor.lastName}
                              </p>
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic text-sm">
                      Aucune prescription
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
