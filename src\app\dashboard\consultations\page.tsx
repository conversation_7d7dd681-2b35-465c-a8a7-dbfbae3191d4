"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Stethoscope,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Activity,
  Phone,
  Mail,
  Heart,
  Thermometer,
  Weight,
  Ruler,
  FileText,
} from "lucide-react";
import Link from "next/link";
import {
  getConsultations,
  startConsultation,
  completeConsultation,
} from "@/lib/actions/consultations";
import {
  calculateAge,
  formatGender,
  getPatientInitials,
} from "@/lib/utils/patient-utils";

// Types pour les consultations
interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  type: string;
  chiefComplaint?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  consultationFee?: number;
  duration?: number;
  weight?: number;
  height?: number;
  bloodPressure?: string;
  temperature?: number;
  heartRate?: number;
  paymentStatus?: string;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
    dateOfBirth: string;
    gender: string;
    bloodType?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
  };
}

export default function ConsultationsPage() {
  const { data: session } = useSession();
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [dateFilter, setDateFilter] = useState<string>("");

  const loadConsultations = useCallback(async () => {
    setLoading(true);
    try {
      console.log("🔄 Chargement des consultations...");
      const result = await getConsultations({
        search: searchTerm || undefined,
        status: (statusFilter as any) || undefined,
        type: (typeFilter as any) || undefined,
        dateFrom: dateFilter || undefined,
      });

      console.log("📊 Résultat du chargement:", result);

      if (result.success && result.consultations) {
        console.log("✅ Consultations chargées:", result.consultations.length);
        setConsultations(result.consultations);
      } else {
        console.error(
          "❌ Erreur lors du chargement des consultations:",
          result.error
        );
        setConsultations([]);
      }
    } catch (error) {
      console.error(
        "💥 Exception lors du chargement des consultations:",
        error
      );
      setConsultations([]);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter, typeFilter, dateFilter]);

  useEffect(() => {
    loadConsultations();
  }, [loadConsultations]);

  // Obtenir la couleur du badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SCHEDULED: {
        label: "Programmée",
        className: "bg-blue-100 text-blue-800",
      },
      IN_PROGRESS: {
        label: "En cours",
        className: "bg-yellow-100 text-yellow-800",
      },
      COMPLETED: {
        label: "Terminée",
        className: "bg-green-100 text-green-800",
      },
      CANCELLED: { label: "Annulée", className: "bg-red-100 text-red-800" },
      NO_SHOW: { label: "Absent", className: "bg-gray-100 text-gray-800" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] ||
      statusConfig.SCHEDULED;
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Obtenir la couleur du badge de type
  const getTypeBadge = (type: string) => {
    const typeConfig = {
      GENERAL: { label: "Générale", className: "bg-blue-100 text-blue-800" },
      EMERGENCY: { label: "Urgence", className: "bg-red-100 text-red-800" },
      FOLLOW_UP: { label: "Suivi", className: "bg-green-100 text-green-800" },
      SPECIALIST: {
        label: "Spécialiste",
        className: "bg-purple-100 text-purple-800",
      },
      TELEMEDICINE: {
        label: "Télémédecine",
        className: "bg-orange-100 text-orange-800",
      },
    };

    const config =
      typeConfig[type as keyof typeof typeConfig] || typeConfig.GENERAL;
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Formater la date et l'heure
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    const timeStr = date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (isToday) {
      return `Aujourd'hui à ${timeStr}`;
    } else {
      return `${date.toLocaleDateString("fr-FR")} à ${timeStr}`;
    }
  };

  // Démarrer une consultation
  const handleStartConsultation = async (consultationId: string) => {
    try {
      const result = await startConsultation(consultationId);
      if (result.success) {
        loadConsultations(); // Recharger la liste
      } else {
        console.error("Erreur lors du démarrage:", result.error);
        alert("Erreur lors du démarrage de la consultation: " + result.error);
      }
    } catch (error) {
      console.error("Erreur lors du démarrage de la consultation:", error);
      alert("Une erreur est survenue");
    }
  };

  // Terminer une consultation
  const handleCompleteConsultation = async (consultationId: string) => {
    try {
      const result = await completeConsultation(consultationId);
      if (result.success) {
        loadConsultations(); // Recharger la liste
      } else {
        console.error("Erreur lors de la finalisation:", result.error);
        alert(
          "Erreur lors de la finalisation de la consultation: " + result.error
        );
      }
    } catch (error) {
      console.error(
        "Erreur lors de la finalisation de la consultation:",
        error
      );
      alert("Une erreur est survenue");
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Consultations Médicales
            </h1>
            <p className="text-gray-600 mt-2">
              Gérez les consultations et examens médicaux
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={loadConsultations}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Actualiser
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Consultations
              </CardTitle>
              <Stethoscope className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{consultations.length}</div>
              <p className="text-xs text-muted-foreground">
                Toutes les consultations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Programmées</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {consultations.filter((c) => c.status === "SCHEDULED").length}
              </div>
              <p className="text-xs text-muted-foreground">En attente</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En cours</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {consultations.filter((c) => c.status === "IN_PROGRESS").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Consultations actives
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminées</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {consultations.filter((c) => c.status === "COMPLETED").length}
              </div>
              <p className="text-xs text-muted-foreground">
                Consultations finalisées
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filtres et recherche */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher par patient, médecin, diagnostic..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tous les statuts</SelectItem>
                  <SelectItem value="SCHEDULED">Programmée</SelectItem>
                  <SelectItem value="IN_PROGRESS">En cours</SelectItem>
                  <SelectItem value="COMPLETED">Terminée</SelectItem>
                  <SelectItem value="CANCELLED">Annulée</SelectItem>
                  <SelectItem value="NO_SHOW">Absent</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tous les types</SelectItem>
                  <SelectItem value="GENERAL">Générale</SelectItem>
                  <SelectItem value="EMERGENCY">Urgence</SelectItem>
                  <SelectItem value="FOLLOW_UP">Suivi</SelectItem>
                  <SelectItem value="SPECIALIST">Spécialiste</SelectItem>
                  <SelectItem value="TELEMEDICINE">Télémédecine</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-full md:w-48"
              />
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("");
                  setTypeFilter("");
                  setDateFilter("");
                }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Réinitialiser
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Liste des consultations */}
        <Card>
          <CardHeader>
            <CardTitle>Consultations ({consultations.length})</CardTitle>
            <CardDescription>
              Liste de toutes les consultations médicales
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Chargement des consultations...
              </div>
            ) : consultations.length === 0 ? (
              <div className="text-center py-8">
                <Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune consultation trouvée
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || statusFilter || typeFilter || dateFilter
                    ? "Aucune consultation ne correspond à vos critères de recherche."
                    : "Les consultations apparaîtront ici une fois que les rendez-vous seront démarrés."}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {consultations.map((consultation) => (
                  <div
                    key={consultation.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src="" />
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {getPatientInitials(
                            consultation.patient.firstName,
                            consultation.patient.lastName
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-gray-900">
                            {consultation.patient.firstName}{" "}
                            {consultation.patient.lastName}
                          </h3>
                          {getStatusBadge(consultation.status)}
                          {getTypeBadge(consultation.type)}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span className="font-mono">
                            {consultation.patient.patientNumber}
                          </span>
                          <span>
                            {calculateAge(consultation.patient.dateOfBirth)} ans
                          </span>
                          <span>
                            {formatGender(consultation.patient.gender)}
                          </span>
                          {consultation.patient.bloodType && (
                            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">
                              {consultation.patient.bloodType}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDateTime(consultation.consultationDate)}
                          </div>
                          <div className="flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            Dr. {consultation.doctor.firstName}{" "}
                            {consultation.doctor.lastName}
                          </div>
                          {consultation.chiefComplaint && (
                            <div className="flex items-center">
                              <Stethoscope className="h-3 w-3 mr-1" />
                              {consultation.chiefComplaint}
                            </div>
                          )}
                        </div>

                        {/* Données vitales si disponibles */}
                        {(consultation.weight ||
                          consultation.height ||
                          consultation.bloodPressure ||
                          consultation.temperature ||
                          consultation.heartRate) && (
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                            {consultation.weight && (
                              <div className="flex items-center">
                                <Weight className="h-3 w-3 mr-1" />
                                {consultation.weight}kg
                              </div>
                            )}
                            {consultation.height && (
                              <div className="flex items-center">
                                <Ruler className="h-3 w-3 mr-1" />
                                {consultation.height}cm
                              </div>
                            )}
                            {consultation.bloodPressure && (
                              <div className="flex items-center">
                                <Heart className="h-3 w-3 mr-1" />
                                {consultation.bloodPressure}
                              </div>
                            )}
                            {consultation.temperature && (
                              <div className="flex items-center">
                                <Thermometer className="h-3 w-3 mr-1" />
                                {consultation.temperature}°C
                              </div>
                            )}
                            {consultation.heartRate && (
                              <div className="flex items-center">
                                <Activity className="h-3 w-3 mr-1" />
                                {consultation.heartRate} bpm
                              </div>
                            )}
                          </div>
                        )}

                        {/* Diagnostic si disponible */}
                        {consultation.diagnosis && (
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <div className="flex items-center">
                              <FileText className="h-3 w-3 mr-1" />
                              <span className="font-medium">
                                Diagnostic:
                              </span>{" "}
                              {consultation.diagnosis}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      {consultation.consultationFee && (
                        <div className="text-right text-sm">
                          <div className="text-gray-900 font-medium">
                            {consultation.consultationFee.toLocaleString()} FCFA
                          </div>
                          <div className="text-gray-500">
                            {consultation.duration
                              ? `${consultation.duration} min`
                              : "Durée non définie"}
                          </div>
                        </div>
                      )}

                      {/* Actions rapides pour changer le statut */}
                      {consultation.status === "SCHEDULED" && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleStartConsultation(consultation.id)
                            }
                          >
                            <Activity className="h-4 w-4 mr-1" />
                            Démarrer
                          </Button>
                        </div>
                      )}

                      {consultation.status === "IN_PROGRESS" && (
                        <div className="flex space-x-2">
                          <Button size="sm" asChild>
                            <Link
                              href={`/dashboard/consultations/${consultation.id}`}
                            >
                              <Stethoscope className="h-4 w-4 mr-1" />
                              Consulter
                            </Link>
                          </Button>
                        </div>
                      )}

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/consultations/${consultation.id}`}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Voir la consultation
                            </Link>
                          </DropdownMenuItem>
                          {consultation.status === "IN_PROGRESS" && (
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/consultations/${consultation.id}/edit`}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Modifier
                              </Link>
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
