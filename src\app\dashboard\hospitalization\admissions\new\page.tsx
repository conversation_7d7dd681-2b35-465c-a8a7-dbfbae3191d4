"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  UserPlus,
  Building2,
  Bed,
  Calendar,
  User,
  Phone,
  Save,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import {
  createAdmission,
  getRooms,
  getActivePatients,
  getActiveDoctors,
} from "@/lib/actions/hospitalization";

const admissionSchema = z.object({
  patientId: z.string().min(1, "Veuillez sélectionner un patient"),
  roomId: z.string().min(1, "Veuillez sélectionner une chambre"),
  bedId: z.string().optional(),
  doctorId: z.string().min(1, "Veuillez sélectionner un médecin"),
  admissionDate: z.string().min(1, "Date d'admission requise"),
  expectedDischargeDate: z.string().optional(),
  reason: z.string().min(1, "Motif d'admission requis"),
  diagnosis: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelation: z.string().optional(),
  insuranceInfo: z.string().optional(),
  admissionNotes: z.string().optional(),
});

type AdmissionFormData = z.infer<typeof admissionSchema>;

export default function NewAdmissionPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [rooms, setRooms] = useState<any[]>([]);
  const [availableBeds, setAvailableBeds] = useState<any[]>([]);
  const [patients, setPatients] = useState<any[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);

  const form = useForm<AdmissionFormData>({
    resolver: zodResolver(admissionSchema),
    defaultValues: {
      admissionDate: new Date().toISOString().slice(0, 16),
    },
  });

  // Charger les données initiales
  useEffect(() => {
    async function loadData() {
      try {
        const roomsResult = await getRooms();
        if (roomsResult.success) {
          setRooms(roomsResult.rooms || []);
        }

        // Charger les patients et médecins depuis la base de données
        const [patientsResult, doctorsResult] = await Promise.all([
          getActivePatients(),
          getActiveDoctors(),
        ]);

        if (patientsResult.success) {
          setPatients(patientsResult.patients || []);
        } else {
          toast.error("Erreur lors du chargement des patients");
        }

        if (doctorsResult.success) {
          setDoctors(doctorsResult.doctors || []);
        } else {
          toast.error("Erreur lors du chargement des médecins");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      }
    }

    loadData();
  }, []);

  // Mettre à jour les lits disponibles quand la chambre change
  const selectedRoomId = form.watch("roomId");
  useEffect(() => {
    if (selectedRoomId) {
      const selectedRoom = rooms.find((room) => room.id === selectedRoomId);
      if (selectedRoom) {
        const availableBeds =
          selectedRoom.beds?.filter((bed: any) => bed.status === "AVAILABLE") ||
          [];
        setAvailableBeds(availableBeds);

        // Reset bed selection if current bed is not available
        const currentBedId = form.getValues("bedId");
        if (
          currentBedId &&
          !availableBeds.find((bed: any) => bed.id === currentBedId)
        ) {
          form.setValue("bedId", "");
        }
      }
    } else {
      setAvailableBeds([]);
    }
  }, [selectedRoomId, rooms, form]);

  const onSubmit = async (data: AdmissionFormData) => {
    try {
      setLoading(true);
      const result = await createAdmission(data);

      if (result.success) {
        toast.success("Admission créée avec succès");
        router.push("/dashboard/hospitalization/admissions");
      } else {
        toast.error(
          result.error || "Erreur lors de la création de l'admission"
        );
      }
    } catch (error) {
      toast.error("Erreur lors de la création de l'admission");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Nouvelle Admission
            </h1>
            <p className="text-gray-600 mt-2">
              Créer une nouvelle admission d'hospitalisation
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/dashboard/hospitalization/admissions">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour aux Admissions
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Informations Patient */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Informations Patient
                  </CardTitle>
                  <CardDescription>
                    Sélectionnez le patient à hospitaliser
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="patientId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Patient *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner un patient" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {patients.map((patient) => (
                              <SelectItem key={patient.id} value={patient.id}>
                                {patient.firstName} {patient.lastName} -{" "}
                                {patient.phone}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="doctorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Médecin responsable *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner un médecin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {doctors.map((doctor) => (
                              <SelectItem key={doctor.id} value={doctor.id}>
                                {doctor.firstName} {doctor.lastName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Informations Chambre */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2" />
                    Chambre et Lit
                  </CardTitle>
                  <CardDescription>
                    Assignez une chambre et un lit au patient
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="roomId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Chambre *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner une chambre" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {rooms.map((room) => (
                              <SelectItem key={room.id} value={room.id}>
                                Chambre {room.number} - {room.name} (Étage{" "}
                                {room.floor})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bedId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lit (optionnel)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner un lit" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableBeds.map((bed) => (
                              <SelectItem key={bed.id} value={bed.id}>
                                Lit {bed.number}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Informations Admission */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Détails de l'Admission
                  </CardTitle>
                  <CardDescription>
                    Dates et motifs de l'hospitalisation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="admissionDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date d'admission *</FormLabel>
                        <FormControl>
                          <Input type="datetime-local" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expectedDischargeDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date de sortie prévue</FormLabel>
                        <FormControl>
                          <Input type="datetime-local" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Motif d'admission *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ex: Chirurgie programmée, Observation..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="diagnosis"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Diagnostic</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Diagnostic médical..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Contact d'urgence */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Phone className="h-5 w-5 mr-2" />
                    Contact d'Urgence
                  </CardTitle>
                  <CardDescription>
                    Personne à contacter en cas d'urgence
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="emergencyContactName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom du contact</FormLabel>
                        <FormControl>
                          <Input placeholder="Nom complet..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergencyContactPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Téléphone</FormLabel>
                        <FormControl>
                          <Input placeholder="+223 70 00 00 00" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergencyContactRelation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Relation</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ex: Époux, Fils, Mère..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Notes et assurance */}
            <Card>
              <CardHeader>
                <CardTitle>Informations Complémentaires</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="insuranceInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Informations d'assurance</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nom de l'assurance, numéro de police..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="admissionNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes d'admission</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Notes supplémentaires sur l'admission..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild>
                <Link href="/dashboard/hospitalization/admissions">
                  Annuler
                </Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Créer l'Admission
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </DashboardLayout>
  );
}
