"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Users,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  UserCheck,
  UserX,
  Calendar,
  Clock,
  Building2,
  Bed,
} from "lucide-react";
import Link from "next/link";
import { getAdmissions, updateAdmissionStatus } from "@/lib/actions/hospitalization";

export default function AdmissionsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [admissions, setAdmissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadAdmissions() {
      try {
        setLoading(true);
        const result = await getAdmissions();
        
        if (result.success) {
          setAdmissions(result.admissions || []);
        } else {
          toast.error("Erreur lors du chargement des admissions");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadAdmissions();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ADMITTED":
        return <Badge className="bg-green-100 text-green-800">Hospitalisé</Badge>;
      case "DISCHARGED":
        return <Badge className="bg-blue-100 text-blue-800">Sorti</Badge>;
      case "TRANSFERRED":
        return <Badge className="bg-yellow-100 text-yellow-800">Transféré</Badge>;
      case "CANCELLED":
        return <Badge className="bg-red-100 text-red-800">Annulé</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleStatusChange = async (admissionId: string, newStatus: string) => {
    try {
      const result = await updateAdmissionStatus(admissionId, newStatus as any);
      
      if (result.success) {
        toast.success("Statut mis à jour avec succès");
        // Recharger les données
        const updatedResult = await getAdmissions();
        if (updatedResult.success) {
          setAdmissions(updatedResult.admissions || []);
        }
      } else {
        toast.error("Erreur lors de la mise à jour du statut");
      }
    } catch (error) {
      toast.error("Erreur lors de la mise à jour");
    }
  };

  const filteredAdmissions = admissions.filter((admission) => {
    const matchesSearch =
      admission.patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admission.patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admission.admissionNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === "all" || admission.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des admissions...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Gestion des Admissions
            </h1>
            <p className="text-gray-600 mt-2">
              Liste complète des admissions et hospitalisations
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hospitalization">
                ← Retour aux Hospitalisations
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/hospitalization/admissions/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Admission
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Admissions</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{admissions.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Hospitalisés</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {admissions.filter(a => a.status === "ADMITTED").length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sorties</CardTitle>
              <UserX className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {admissions.filter(a => a.status === "DISCHARGED").length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aujourd'hui</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {admissions.filter(a => 
                  new Date(a.admissionDate).toDateString() === new Date().toDateString()
                ).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher un patient ou numéro..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="ADMITTED">Hospitalisé</SelectItem>
                  <SelectItem value="DISCHARGED">Sorti</SelectItem>
                  <SelectItem value="TRANSFERRED">Transféré</SelectItem>
                  <SelectItem value="CANCELLED">Annulé</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="w-full">
                <Filter className="h-4 w-4 mr-2" />
                Plus de filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Table des admissions */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Admissions</CardTitle>
            <CardDescription>
              {filteredAdmissions.length} admission(s) trouvée(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>N° Admission</TableHead>
                  <TableHead>Patient</TableHead>
                  <TableHead>Médecin</TableHead>
                  <TableHead>Chambre/Lit</TableHead>
                  <TableHead>Date d'admission</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAdmissions.map((admission) => (
                  <TableRow key={admission.id}>
                    <TableCell className="font-medium">
                      {admission.admissionNumber}
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">
                          {admission.patient.firstName} {admission.patient.lastName}
                        </p>
                        <p className="text-sm text-gray-600">
                          {admission.patient.phone}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      Dr. {admission.doctor.firstName} {admission.doctor.lastName}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4 text-gray-500" />
                        <span>{admission.room.number}</span>
                        {admission.bed && (
                          <>
                            <Bed className="h-4 w-4 text-gray-500" />
                            <span>{admission.bed.number}</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span>
                          {new Date(admission.admissionDate).toLocaleDateString("fr-FR")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(admission.status)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            Voir détails
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Modifier
                          </DropdownMenuItem>
                          {admission.status === "ADMITTED" && (
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(admission.id, "DISCHARGED")}
                            >
                              <UserX className="mr-2 h-4 w-4" />
                              Marquer comme sorti
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {filteredAdmissions.length === 0 && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">
                  Aucune admission trouvée avec ces critères
                </p>
                <Button asChild>
                  <Link href="/dashboard/hospitalization/admissions/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Créer une nouvelle admission
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
