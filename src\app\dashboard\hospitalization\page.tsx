"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Bed,
  Users,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  UserCheck,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import Link from "next/link";

export default function HospitalizationPage() {
  const [activeTab, setActiveTab] = useState("overview");

  // Données de démonstration
  const stats = [
    {
      title: "Chambres Total",
      value: "45",
      change: "3 nouvelles ce mois",
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Lits Disponibles",
      value: "12",
      change: "Sur 78 lits total",
      icon: Bed,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Patients Hospitalisés",
      value: "66",
      change: "+5 depuis hier",
      icon: Users,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Taux d'Occupation",
      value: "84.6%",
      change: "Optimal",
      icon: UserCheck,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const recentAdmissions = [
    {
      id: "ADM-20250126-001",
      patient: "Aminata Traoré",
      room: "101",
      bed: "A",
      doctor: "Dr. Keita",
      admissionDate: "2025-01-26 14:30",
      reason: "Chirurgie programmée",
      status: "ADMITTED",
    },
    {
      id: "ADM-20250126-002",
      patient: "Moussa Diallo",
      room: "205",
      bed: "B",
      doctor: "Dr. Sanogo",
      admissionDate: "2025-01-26 09:15",
      reason: "Observation post-opératoire",
      status: "ADMITTED",
    },
    {
      id: "ADM-20250125-015",
      patient: "Fatoumata Keita",
      room: "103",
      bed: "A",
      doctor: "Dr. Coulibaly",
      admissionDate: "2025-01-25 16:45",
      reason: "Accouchement",
      status: "DISCHARGED",
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ADMITTED":
        return <Badge className="bg-blue-100 text-blue-800">Hospitalisé</Badge>;
      case "DISCHARGED":
        return <Badge className="bg-green-100 text-green-800">Sorti</Badge>;
      case "TRANSFERRED":
        return <Badge className="bg-yellow-100 text-yellow-800">Transféré</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Hospitalisations & Lits
            </h1>
            <p className="text-gray-600 mt-2">
              Gestion des chambres, lits et admissions
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hospitalization/rooms">
                <Building2 className="h-4 w-4 mr-2" />
                Gérer les Chambres
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/hospitalization/admissions/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Admission
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="admissions">Admissions</TabsTrigger>
            <TabsTrigger value="rooms">Chambres</TabsTrigger>
            <TabsTrigger value="planning">Planning</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Admissions récentes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Admissions Récentes
                </CardTitle>
                <CardDescription>
                  Dernières admissions et sorties
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAdmissions.map((admission) => (
                    <div
                      key={admission.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium text-gray-900">
                              {admission.patient}
                            </p>
                            <p className="text-sm text-gray-600">
                              {admission.id}
                            </p>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>Chambre {admission.room} - Lit {admission.bed}</p>
                            <p>{admission.doctor}</p>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>{admission.admissionDate}</p>
                            <p>{admission.reason}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(admission.status)}
                        <Button variant="outline" size="sm">
                          Voir Détails
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/dashboard/hospitalization/admissions">
                      Voir Toutes les Admissions
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="admissions">
            <Card>
              <CardHeader>
                <CardTitle>Gestion des Admissions</CardTitle>
                <CardDescription>
                  Liste complète des admissions et hospitalisations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Page des admissions en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/admissions">
                      Accéder aux Admissions
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rooms">
            <Card>
              <CardHeader>
                <CardTitle>Gestion des Chambres</CardTitle>
                <CardDescription>
                  Configuration et statut des chambres et lits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Page des chambres en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/rooms">
                      Accéder aux Chambres
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="planning">
            <Card>
              <CardHeader>
                <CardTitle>Planning d'Occupation</CardTitle>
                <CardDescription>
                  Vue calendrier des occupations et disponibilités
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Planning d'occupation en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/planning">
                      Accéder au Planning
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
