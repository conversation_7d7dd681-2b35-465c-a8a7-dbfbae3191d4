"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Bed,
  Users,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  UserCheck,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import Link from "next/link";
import { getRooms, getAdmissions } from "@/lib/actions/hospitalization";

export default function HospitalizationPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [rooms, setRooms] = useState<any[]>([]);
  const [admissions, setAdmissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        const [roomsResult, admissionsResult] = await Promise.all([
          getRooms(),
          getAdmissions(),
        ]);

        if (roomsResult.success) {
          setRooms(roomsResult.rooms || []);
        } else {
          toast.error("Erreur lors du chargement des chambres");
        }

        if (admissionsResult.success) {
          setAdmissions(admissionsResult.admissions || []);
        } else {
          toast.error("Erreur lors du chargement des admissions");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, []);

  // Calculer les statistiques à partir des données réelles
  const totalRooms = rooms.length;
  const totalBeds = rooms.reduce(
    (sum, room) => sum + (room.beds?.length || 0),
    0
  );
  const occupiedBeds = rooms.reduce(
    (sum, room) =>
      sum +
      (room.beds?.filter((bed: any) => bed.status === "OCCUPIED").length || 0),
    0
  );
  const availableBeds = totalBeds - occupiedBeds;
  const currentAdmissions = admissions.filter(
    (admission) => admission.status === "ADMITTED"
  ).length;
  const occupancyRate =
    totalBeds > 0 ? ((occupiedBeds / totalBeds) * 100).toFixed(1) : "0";

  const stats = [
    {
      title: "Chambres Total",
      value: totalRooms.toString(),
      change: `${totalRooms} chambres actives`,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Lits Disponibles",
      value: availableBeds.toString(),
      change: `Sur ${totalBeds} lits total`,
      icon: Bed,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Patients Hospitalisés",
      value: currentAdmissions.toString(),
      change: `${currentAdmissions} admissions actives`,
      icon: Users,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Taux d'Occupation",
      value: `${occupancyRate}%`,
      change: occupiedBeds > 0 ? "En cours" : "Disponible",
      icon: UserCheck,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  // Prendre les 5 admissions les plus récentes
  const recentAdmissions = admissions.slice(0, 5).map((admission) => ({
    id: admission.admissionNumber,
    patient: `${admission.patient.firstName} ${admission.patient.lastName}`,
    room: admission.room.number,
    bed: admission.bed?.number || "N/A",
    doctor: admission.doctor.name,
    admissionDate: new Date(admission.admissionDate).toLocaleString("fr-FR"),
    reason: admission.reason,
    status: admission.status,
  }));

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ADMITTED":
        return <Badge className="bg-blue-100 text-blue-800">Hospitalisé</Badge>;
      case "DISCHARGED":
        return <Badge className="bg-green-100 text-green-800">Sorti</Badge>;
      case "TRANSFERRED":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Transféré</Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des données...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Hospitalisations & Lits
            </h1>
            <p className="text-gray-600 mt-2">
              Gestion des chambres, lits et admissions
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hospitalization/rooms">
                <Building2 className="h-4 w-4 mr-2" />
                Gérer les Chambres
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/hospitalization/admissions/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Admission
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="admissions">Admissions</TabsTrigger>
            <TabsTrigger value="rooms">Chambres</TabsTrigger>
            <TabsTrigger value="planning">Planning</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Admissions récentes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Admissions Récentes
                </CardTitle>
                <CardDescription>
                  Dernières admissions et sorties
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAdmissions.map((admission) => (
                    <div
                      key={admission.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium text-gray-900">
                              {admission.patient}
                            </p>
                            <p className="text-sm text-gray-600">
                              {admission.id}
                            </p>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>
                              Chambre {admission.room} - Lit {admission.bed}
                            </p>
                            <p>{admission.doctor}</p>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>{admission.admissionDate}</p>
                            <p>{admission.reason}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(admission.status)}
                        <Button variant="outline" size="sm">
                          Voir Détails
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/dashboard/hospitalization/admissions">
                      Voir Toutes les Admissions
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="admissions">
            <Card>
              <CardHeader>
                <CardTitle>Gestion des Admissions</CardTitle>
                <CardDescription>
                  Liste complète des admissions et hospitalisations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Page des admissions en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/admissions">
                      Accéder aux Admissions
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rooms">
            <Card>
              <CardHeader>
                <CardTitle>Gestion des Chambres</CardTitle>
                <CardDescription>
                  Configuration et statut des chambres et lits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Page des chambres en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/rooms">
                      Accéder aux Chambres
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="planning">
            <Card>
              <CardHeader>
                <CardTitle>Planning d'Occupation</CardTitle>
                <CardDescription>
                  Vue calendrier des occupations et disponibilités
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    Planning d'occupation en cours de développement
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/hospitalization/planning">
                      Accéder au Planning
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
