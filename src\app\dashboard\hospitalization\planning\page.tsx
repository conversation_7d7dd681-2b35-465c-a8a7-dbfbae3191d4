"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar,
  Building2,
  Bed,
  Users,
  ArrowLeft,
  ArrowRight,
  Filter,
  RefreshCw,
  Eye,
} from "lucide-react";
import Link from "next/link";
import { getRooms, getAdmissions } from "@/lib/actions/hospitalization";

export default function PlanningPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState("week"); // week, month
  const [filterFloor, setFilterFloor] = useState("all");
  const [rooms, setRooms] = useState<any[]>([]);
  const [admissions, setAdmissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        const [roomsResult, admissionsResult] = await Promise.all([
          getRooms(),
          getAdmissions(),
        ]);

        if (roomsResult.success) {
          setRooms(roomsResult.rooms || []);
        }

        if (admissionsResult.success) {
          setAdmissions(admissionsResult.admissions || []);
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, []);

  // Générer les dates pour la vue semaine
  const getWeekDates = (date: Date) => {
    const week = [];
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay() + 1); // Lundi

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      week.push(day);
    }
    return week;
  };

  // Filtrer les chambres par étage
  const filteredRooms = rooms.filter(room => 
    filterFloor === "all" || room.floor.toString() === filterFloor
  );

  // Obtenir le statut d'occupation pour une chambre à une date donnée
  const getRoomOccupancy = (roomId: string, date: Date) => {
    const dateStr = date.toDateString();
    const roomAdmissions = admissions.filter(admission => 
      admission.roomId === roomId && 
      admission.status === "ADMITTED" &&
      new Date(admission.admissionDate).toDateString() <= dateStr &&
      (!admission.actualDischargeDate || new Date(admission.actualDischargeDate).toDateString() >= dateStr)
    );

    return roomAdmissions;
  };

  const getOccupancyBadge = (occupancy: any[]) => {
    if (occupancy.length === 0) {
      return <Badge className="bg-green-100 text-green-800">Libre</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800">{occupancy.length} patient(s)</Badge>;
    }
  };

  const navigateWeek = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === "next" ? 7 : -7));
    setCurrentDate(newDate);
  };

  const weekDates = getWeekDates(currentDate);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement du planning...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Planning d'Occupation
            </h1>
            <p className="text-gray-600 mt-2">
              Vue calendrier des occupations et disponibilités des chambres
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hospitalization">
                ← Retour aux Hospitalisations
              </Link>
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
          </div>
        </div>

        {/* Contrôles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Navigation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Select value={filterFloor} onValueChange={setFilterFloor}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Étage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les étages</SelectItem>
                    <SelectItem value="1">1er étage</SelectItem>
                    <SelectItem value="2">2ème étage</SelectItem>
                    <SelectItem value="3">3ème étage</SelectItem>
                    <SelectItem value="4">4ème étage</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={viewMode} onValueChange={setViewMode}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">Vue semaine</SelectItem>
                    <SelectItem value="month">Vue mois</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={() => navigateWeek("prev")}>
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium px-4">
                  Semaine du {weekDates[0].toLocaleDateString("fr-FR")} au {weekDates[6].toLocaleDateString("fr-FR")}
                </span>
                <Button variant="outline" size="sm" onClick={() => navigateWeek("next")}>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Planning */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Planning Hebdomadaire
            </CardTitle>
            <CardDescription>
              Occupation des chambres pour la semaine sélectionnée
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border p-3 bg-gray-50 text-left font-medium">
                      Chambre
                    </th>
                    {weekDates.map((date, index) => (
                      <th key={index} className="border p-3 bg-gray-50 text-center font-medium min-w-[120px]">
                        <div>
                          <div className="text-sm">
                            {date.toLocaleDateString("fr-FR", { weekday: "short" })}
                          </div>
                          <div className="text-xs text-gray-600">
                            {date.toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit" })}
                          </div>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {filteredRooms.map((room) => (
                    <tr key={room.id} className="hover:bg-gray-50">
                      <td className="border p-3">
                        <div className="flex items-center space-x-2">
                          <Building2 className="h-4 w-4 text-gray-500" />
                          <div>
                            <div className="font-medium">Chambre {room.number}</div>
                            <div className="text-sm text-gray-600">
                              Étage {room.floor} • {room.capacity} lit(s)
                            </div>
                          </div>
                        </div>
                      </td>
                      {weekDates.map((date, dateIndex) => {
                        const occupancy = getRoomOccupancy(room.id, date);
                        const isToday = date.toDateString() === new Date().toDateString();
                        
                        return (
                          <td 
                            key={dateIndex} 
                            className={`border p-2 text-center ${isToday ? 'bg-blue-50' : ''}`}
                          >
                            <div className="space-y-1">
                              {getOccupancyBadge(occupancy)}
                              {occupancy.length > 0 && (
                                <div className="space-y-1">
                                  {occupancy.slice(0, 2).map((admission, admIndex) => (
                                    <div key={admIndex} className="text-xs bg-gray-100 rounded p-1">
                                      <div className="font-medium">
                                        {admission.patient.firstName} {admission.patient.lastName}
                                      </div>
                                      <div className="text-gray-600">
                                        {admission.bed?.number && `Lit ${admission.bed.number}`}
                                      </div>
                                    </div>
                                  ))}
                                  {occupancy.length > 2 && (
                                    <div className="text-xs text-gray-500">
                                      +{occupancy.length - 2} autre(s)
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredRooms.length === 0 && (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">
                  Aucune chambre trouvée pour cet étage
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Légende */}
        <Card>
          <CardHeader>
            <CardTitle>Légende</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-100 text-green-800">Libre</Badge>
                <span className="text-sm text-gray-600">Chambre disponible</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className="bg-red-100 text-red-800">Occupé</Badge>
                <span className="text-sm text-gray-600">Chambre occupée</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-50 border rounded"></div>
                <span className="text-sm text-gray-600">Aujourd'hui</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Chambres Total</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredRooms.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lits Total</CardTitle>
              <Bed className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredRooms.reduce((sum, room) => sum + room.capacity, 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupés Aujourd'hui</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredRooms.reduce((sum, room) => 
                  sum + getRoomOccupancy(room.id, new Date()).length, 0
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taux d'Occupation</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(() => {
                  const totalBeds = filteredRooms.reduce((sum, room) => sum + room.capacity, 0);
                  const occupiedBeds = filteredRooms.reduce((sum, room) => 
                    sum + getRoomOccupancy(room.id, new Date()).length, 0
                  );
                  return totalBeds > 0 ? `${((occupiedBeds / totalBeds) * 100).toFixed(1)}%` : "0%";
                })()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
