"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Building2,
  Bed,
  Wifi,
  Tv,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Save,
  <PERSON>L<PERSON><PERSON>,
  DollarSign,
} from "lucide-react";
import Link from "next/link";
import { createRoom } from "@/lib/actions/hospitalization";

const roomSchema = z.object({
  number: z.string().min(1, "Numéro de chambre requis"),
  name: z.string().min(1, "Nom de chambre requis"),
  floor: z.coerce.number().min(1, "Étage requis"),
  roomType: z.enum(["STANDARD", "VIP", "ICU", "MATERNITY", "PEDIATRIC"], {
    required_error: "Type de chambre requis",
  }),
  capacity: z.coerce.number().min(1, "Capacité minimale de 1 lit").max(10, "Capacité maximale de 10 lits"),
  hasPrivateBathroom: z.boolean().default(false),
  hasAirConditioning: z.boolean().default(false),
  hasTV: z.boolean().default(false),
  hasWifi: z.boolean().default(false),
  description: z.string().optional(),
  dailyRate: z.coerce.number().min(0, "Tarif doit être positif").optional(),
});

type RoomFormData = z.infer<typeof roomSchema>;

export default function NewRoomPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const form = useForm<RoomFormData>({
    resolver: zodResolver(roomSchema),
    defaultValues: {
      hasPrivateBathroom: false,
      hasAirConditioning: false,
      hasTV: false,
      hasWifi: true, // WiFi par défaut
      capacity: 1,
      floor: 1,
    },
  });

  const onSubmit = async (data: RoomFormData) => {
    try {
      setLoading(true);
      const result = await createRoom(data);

      if (result.success) {
        toast.success("Chambre créée avec succès");
        router.push("/dashboard/hospitalization/rooms");
      } else {
        toast.error(result.error || "Erreur lors de la création de la chambre");
      }
    } catch (error) {
      toast.error("Erreur lors de la création de la chambre");
    } finally {
      setLoading(false);
    }
  };

  const roomTypeOptions = [
    { value: "STANDARD", label: "Standard", description: "Chambre standard" },
    { value: "VIP", label: "VIP", description: "Chambre VIP avec services premium" },
    { value: "ICU", label: "Soins Intensifs", description: "Unité de soins intensifs" },
    { value: "MATERNITY", label: "Maternité", description: "Chambre de maternité" },
    { value: "PEDIATRIC", label: "Pédiatrie", description: "Chambre pédiatrique" },
  ];

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Nouvelle Chambre
            </h1>
            <p className="text-gray-600 mt-2">
              Créer une nouvelle chambre d'hospitalisation
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/dashboard/hospitalization/rooms">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour aux Chambres
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Informations de base */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2" />
                    Informations de Base
                  </CardTitle>
                  <CardDescription>
                    Informations principales de la chambre
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Numéro de chambre *</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: 101, 205A..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom de la chambre *</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Chambre Standard 101..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="floor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Étage *</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="capacity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Capacité (lits) *</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" max="10" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="roomType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type de chambre *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner un type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roomTypeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div>
                                  <div className="font-medium">{option.label}</div>
                                  <div className="text-sm text-gray-500">{option.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Équipements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Bed className="h-5 w-5 mr-2" />
                    Équipements
                  </CardTitle>
                  <CardDescription>
                    Équipements disponibles dans la chambre
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="hasPrivateBathroom"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="flex items-center">
                            <Bath className="h-4 w-4 mr-2" />
                            Salle de bain privée
                          </FormLabel>
                          <FormDescription>
                            La chambre dispose d'une salle de bain privée
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hasAirConditioning"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="flex items-center">
                            <Snowflake className="h-4 w-4 mr-2" />
                            Climatisation
                          </FormLabel>
                          <FormDescription>
                            La chambre est équipée de la climatisation
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hasTV"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="flex items-center">
                            <Tv className="h-4 w-4 mr-2" />
                            Télévision
                          </FormLabel>
                          <FormDescription>
                            La chambre dispose d'une télévision
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hasWifi"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="flex items-center">
                            <Wifi className="h-4 w-4 mr-2" />
                            WiFi
                          </FormLabel>
                          <FormDescription>
                            Accès WiFi disponible dans la chambre
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Tarification et description */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Tarification et Description
                </CardTitle>
                <CardDescription>
                  Tarif journalier et description de la chambre
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="dailyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tarif journalier (XOF)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="0" 
                          placeholder="Ex: 25000"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Tarif par jour d'hospitalisation en francs CFA
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Description détaillée de la chambre, équipements spéciaux, notes..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Informations supplémentaires sur la chambre
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild>
                <Link href="/dashboard/hospitalization/rooms">
                  Annuler
                </Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Créer la Chambre
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </DashboardLayout>
  );
}
