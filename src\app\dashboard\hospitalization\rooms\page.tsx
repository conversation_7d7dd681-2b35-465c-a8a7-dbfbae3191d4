"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Building2,
  Bed,
  Plus,
  Search,
  Filter,
  Wifi,
  Tv,
  Snowflake,
  Bath,
  Settings,
  Eye,
  Edit,
} from "lucide-react";
import Link from "next/link";
import { getRooms } from "@/lib/actions/hospitalization";

export default function RoomsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterFloor, setFilterFloor] = useState("all");
  const [rooms, setRooms] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadRooms() {
      try {
        setLoading(true);
        const result = await getRooms();

        if (result.success) {
          setRooms(result.rooms || []);
        } else {
          toast.error("Erreur lors du chargement des chambres");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadRooms();
  }, []);

  // Transformer les données pour l'affichage
  const transformedRooms = rooms.map((room) => ({
    id: room.id,
    number: room.number,
    name: room.name,
    floor: room.floor,
    type: room.roomType,
    capacity: room.capacity,
    occupiedBeds:
      room.beds?.filter((bed: any) => bed.status === "OCCUPIED").length || 0,
    dailyRate: room.dailyRate || 0,
    hasPrivateBathroom: room.hasPrivateBathroom,
    hasAirConditioning: room.hasAirConditioning,
    hasTV: room.hasTV,
    hasWifi: room.hasWifi,
    isAvailable: room.isAvailable,
    beds:
      room.beds?.map((bed: any) => ({
        number: bed.number,
        status: bed.status,
        patient: bed.admissions?.[0]?.patient
          ? `${bed.admissions[0].patient.firstName} ${bed.admissions[0].patient.lastName}`
          : null,
      })) || [],
  }));

  const getRoomTypeBadge = (type: string) => {
    switch (type) {
      case "STANDARD":
        return <Badge className="bg-blue-100 text-blue-800">Standard</Badge>;
      case "VIP":
        return <Badge className="bg-purple-100 text-purple-800">VIP</Badge>;
      case "ICU":
        return (
          <Badge className="bg-red-100 text-red-800">Soins Intensifs</Badge>
        );
      case "MATERNITY":
        return <Badge className="bg-pink-100 text-pink-800">Maternité</Badge>;
      case "PEDIATRIC":
        return <Badge className="bg-green-100 text-green-800">Pédiatrie</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const getBedStatusBadge = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return (
          <Badge className="bg-green-100 text-green-800">Disponible</Badge>
        );
      case "OCCUPIED":
        return <Badge className="bg-red-100 text-red-800">Occupé</Badge>;
      case "MAINTENANCE":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Maintenance</Badge>
        );
      case "RESERVED":
        return <Badge className="bg-blue-100 text-blue-800">Réservé</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const filteredRooms = transformedRooms.filter((room) => {
    const matchesSearch =
      room.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || room.type === filterType;
    const matchesFloor =
      filterFloor === "all" || room.floor.toString() === filterFloor;

    return matchesSearch && matchesType && matchesFloor;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des chambres...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Gestion des Chambres
            </h1>
            <p className="text-gray-600 mt-2">
              Configuration et statut des chambres et lits
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hospitalization">
                ← Retour aux Hospitalisations
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/hospitalization/rooms/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Chambre
              </Link>
            </Button>
          </div>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher une chambre..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <SelectValue placeholder="Type de chambre" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  <SelectItem value="STANDARD">Standard</SelectItem>
                  <SelectItem value="VIP">VIP</SelectItem>
                  <SelectItem value="ICU">Soins Intensifs</SelectItem>
                  <SelectItem value="MATERNITY">Maternité</SelectItem>
                  <SelectItem value="PEDIATRIC">Pédiatrie</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterFloor} onValueChange={setFilterFloor}>
                <SelectTrigger>
                  <SelectValue placeholder="Étage" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les étages</SelectItem>
                  <SelectItem value="1">1er étage</SelectItem>
                  <SelectItem value="2">2ème étage</SelectItem>
                  <SelectItem value="3">3ème étage</SelectItem>
                  <SelectItem value="4">4ème étage</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Plus de filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Liste des chambres */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredRooms.map((room) => (
            <Card key={room.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Building2 className="h-5 w-5 mr-2" />
                      Chambre {room.number}
                    </CardTitle>
                    <CardDescription>{room.name}</CardDescription>
                  </div>
                  {getRoomTypeBadge(room.type)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Informations de base */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Étage</p>
                    <p className="font-medium">{room.floor}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Capacité</p>
                    <p className="font-medium">{room.capacity} lit(s)</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Occupation</p>
                    <p className="font-medium">
                      {room.occupiedBeds}/{room.capacity}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Tarif/jour</p>
                    <p className="font-medium">
                      {room.dailyRate.toLocaleString()} XOF
                    </p>
                  </div>
                </div>

                {/* Équipements */}
                <div>
                  <p className="text-sm text-gray-600 mb-2">Équipements</p>
                  <div className="flex flex-wrap gap-2">
                    {room.hasPrivateBathroom && (
                      <Badge variant="outline" className="text-xs">
                        <Bath className="h-3 w-3 mr-1" />
                        Salle de bain
                      </Badge>
                    )}
                    {room.hasAirConditioning && (
                      <Badge variant="outline" className="text-xs">
                        <Snowflake className="h-3 w-3 mr-1" />
                        Climatisation
                      </Badge>
                    )}
                    {room.hasTV && (
                      <Badge variant="outline" className="text-xs">
                        <Tv className="h-3 w-3 mr-1" />
                        TV
                      </Badge>
                    )}
                    {room.hasWifi && (
                      <Badge variant="outline" className="text-xs">
                        <Wifi className="h-3 w-3 mr-1" />
                        WiFi
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Lits */}
                <div>
                  <p className="text-sm text-gray-600 mb-2">Lits</p>
                  <div className="space-y-2">
                    {room.beds.map((bed) => (
                      <div
                        key={bed.number}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <div className="flex items-center space-x-2">
                          <Bed className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium">
                            Lit {bed.number}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getBedStatusBadge(bed.status)}
                          {bed.patient && (
                            <span className="text-xs text-gray-600">
                              {bed.patient}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-4 border-t">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-2" />
                    Voir
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Modifier
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRooms.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">
                Aucune chambre trouvée avec ces critères
              </p>
              <Button asChild>
                <Link href="/dashboard/hospitalization/rooms/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Créer une nouvelle chambre
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
