"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Clock,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Timer,
  Coffee,
  LogIn,
  LogOut,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  getEmployees,
  getAttendances,
  createOrUpdateAttendance,
} from "@/lib/actions/hr";

// Types
interface Employee {
  id: string;
  employeeNumber: string;
  firstName: string;
  lastName: string;
  status: string;
  department: {
    name: string;
  };
  position: {
    title: string;
  };
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkIn?: string;
  checkOut?: string;
  breakStart?: string;
  breakEnd?: string;
  hoursWorked?: number;
  status: string;
  employee: {
    firstName: string;
    lastName: string;
    employeeNumber: string;
  };
}

const attendanceStatusLabels = {
  PRESENT: "Présent",
  ABSENT: "Absent",
  LATE: "En retard",
  SICK_LEAVE: "Congé maladie",
  VACATION: "Congé",
  HALF_DAY: "Demi-journée",
};

const attendanceStatusColors = {
  PRESENT: "bg-green-100 text-green-800",
  ABSENT: "bg-red-100 text-red-800",
  LATE: "bg-orange-100 text-orange-800",
  SICK_LEAVE: "bg-blue-100 text-blue-800",
  VACATION: "bg-purple-100 text-purple-800",
  HALF_DAY: "bg-yellow-100 text-yellow-800",
};

export default function AttendancePage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split("T")[0]
  );

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les employés actifs
      const employeesResult = await getEmployees({ status: "ACTIVE" });
      if (employeesResult.success) {
        setEmployees(employeesResult.employees || []);
      }

      // Charger les données de présence pour la date sélectionnée
      const attendancesResult = await getAttendances(selectedDate);
      if (attendancesResult.success) {
        setAttendanceRecords(attendancesResult.attendances || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  // Recharger les données quand la date change
  useEffect(() => {
    if (selectedDate) {
      loadAttendanceData();
    }
  }, [selectedDate]);

  const loadAttendanceData = async () => {
    try {
      const attendancesResult = await getAttendances(selectedDate);
      if (attendancesResult.success) {
        setAttendanceRecords(attendancesResult.attendances || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des présences");
    }
  };

  const handleQuickPunch = async (
    type: "checkIn" | "checkOut" | "breakStart" | "breakEnd"
  ) => {
    // Pour la démo, on utilise le premier employé
    if (employees.length === 0) {
      toast.error("Aucun employé disponible");
      return;
    }

    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    try {
      const formData: any = {
        employeeId: employees[0].id,
        date: selectedDate,
        status: "PRESENT" as const,
      };

      // Définir le champ approprié selon le type de pointage
      formData[type] = currentTime;

      const result = await createOrUpdateAttendance(formData);

      if (result.success) {
        toast.success(
          `${
            type === "checkIn"
              ? "Arrivée"
              : type === "checkOut"
              ? "Départ"
              : type === "breakStart"
              ? "Début de pause"
              : "Fin de pause"
          } enregistré(e)`
        );
        loadAttendanceData();
      } else {
        toast.error(result.error || "Erreur lors du pointage");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du pointage");
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return "-";
    return timeString;
  };

  const calculateWorkingHours = (
    checkIn?: string,
    checkOut?: string,
    breakStart?: string,
    breakEnd?: string
  ) => {
    if (!checkIn || !checkOut) return 0;

    const start = new Date(`2000-01-01T${checkIn}:00`);
    const end = new Date(`2000-01-01T${checkOut}:00`);
    let hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);

    // Soustraire la pause si elle existe
    if (breakStart && breakEnd) {
      const breakStartTime = new Date(`2000-01-01T${breakStart}:00`);
      const breakEndTime = new Date(`2000-01-01T${breakEnd}:00`);
      const breakHours =
        (breakEndTime.getTime() - breakStartTime.getTime()) / (1000 * 60 * 60);
      hours -= breakHours;
    }

    return Math.max(0, hours);
  };

  // Calculer les statistiques du jour
  const todayStats = {
    present: attendanceRecords.filter((r) => r.status === "PRESENT").length,
    absent: attendanceRecords.filter((r) => r.status === "ABSENT").length,
    late: attendanceRecords.filter((r) => r.status === "LATE").length,
    totalHours: attendanceRecords.reduce(
      (sum, r) => sum + (r.hoursWorked || 0),
      0
    ),
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/hr">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Pointage</h1>
              <p className="text-gray-600 mt-2">
                Gestion des présences et horaires
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div>
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-40"
              />
            </div>
            <Button>
              <Clock className="h-4 w-4 mr-2" />
              Pointer maintenant
            </Button>
          </div>
        </div>

        {/* Statistiques du jour */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Présents</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {todayStats.present}
              </div>
              <p className="text-xs text-muted-foreground">Employés présents</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Absents</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {todayStats.absent}
              </div>
              <p className="text-xs text-muted-foreground">Employés absents</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En retard</CardTitle>
              <AlertCircle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {todayStats.late}
              </div>
              <p className="text-xs text-muted-foreground">Arrivées tardives</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Heures
              </CardTitle>
              <Timer className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {todayStats.totalHours.toFixed(1)}h
              </div>
              <p className="text-xs text-muted-foreground">
                Heures travaillées
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue="today" className="space-y-6">
          <TabsList>
            <TabsTrigger value="today">Aujourd'hui</TabsTrigger>
            <TabsTrigger value="quick-punch">Pointage rapide</TabsTrigger>
            <TabsTrigger value="reports">Rapports</TabsTrigger>
          </TabsList>

          {/* Présences du jour */}
          <TabsContent value="today">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Présences du{" "}
                  {new Date(selectedDate).toLocaleDateString("fr-FR")}
                </CardTitle>
                <CardDescription>
                  État des présences pour la date sélectionnée
                </CardDescription>
              </CardHeader>
              <CardContent>
                {attendanceRecords.length === 0 ? (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucune donnée de présence
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Les données de présence pour cette date apparaîtront ici.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Employé</TableHead>
                        <TableHead>Nom</TableHead>
                        <TableHead>Arrivée</TableHead>
                        <TableHead>Départ</TableHead>
                        <TableHead>Pause</TableHead>
                        <TableHead>Heures</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {attendanceRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-mono text-sm">
                            {record.employee.employeeNumber}
                          </TableCell>
                          <TableCell className="font-medium">
                            {record.employee.firstName}{" "}
                            {record.employee.lastName}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <LogIn className="h-4 w-4 mr-1 text-green-600" />
                              {formatTime(record.checkIn)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <LogOut className="h-4 w-4 mr-1 text-red-600" />
                              {formatTime(record.checkOut)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {record.breakStart && record.breakEnd ? (
                              <div className="flex items-center">
                                <Coffee className="h-4 w-4 mr-1 text-orange-600" />
                                {formatTime(record.breakStart)} -{" "}
                                {formatTime(record.breakEnd)}
                              </div>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Timer className="h-4 w-4 mr-1 text-blue-600" />
                              {record.hoursWorked
                                ? `${record.hoursWorked}h`
                                : "-"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                attendanceStatusColors[
                                  record.status as keyof typeof attendanceStatusColors
                                ]
                              }
                            >
                              {
                                attendanceStatusLabels[
                                  record.status as keyof typeof attendanceStatusLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm">
                                Modifier
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Pointage rapide */}
          <TabsContent value="quick-punch">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Pointage rapide
                </CardTitle>
                <CardDescription>
                  Interface de pointage pour les employés
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Clock className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {new Date().toLocaleTimeString("fr-FR", {
                      hour: "2-digit",
                      minute: "2-digit",
                      second: "2-digit",
                    })}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {new Date().toLocaleDateString("fr-FR", {
                      weekday: "long",
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })}
                  </p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                    <Button
                      size="lg"
                      className="h-20 flex-col"
                      onClick={() => handleQuickPunch("checkIn")}
                    >
                      <LogIn className="h-6 w-6 mb-2" />
                      Arrivée
                    </Button>
                    <Button
                      size="lg"
                      variant="outline"
                      className="h-20 flex-col"
                      onClick={() => handleQuickPunch("breakStart")}
                    >
                      <Coffee className="h-6 w-6 mb-2" />
                      Pause
                    </Button>
                    <Button
                      size="lg"
                      variant="outline"
                      className="h-20 flex-col"
                      onClick={() => handleQuickPunch("breakEnd")}
                    >
                      <Coffee className="h-6 w-6 mb-2" />
                      Retour
                    </Button>
                    <Button
                      size="lg"
                      variant="destructive"
                      className="h-20 flex-col"
                      onClick={() => handleQuickPunch("checkOut")}
                    >
                      <LogOut className="h-6 w-6 mb-2" />
                      Départ
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rapports */}
          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Rapports de présence
                </CardTitle>
                <CardDescription>
                  Analyses et statistiques des présences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Rapports à venir
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Les rapports détaillés de présence seront disponibles
                    prochainement.
                  </p>
                  <Button variant="outline">Exporter les données</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
