"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  UserPlus,
  Building2,
  Briefcase,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getDepartments, getPositions, createEmployee } from "@/lib/actions/hr";
import { ContractType, Gender } from "@prisma/client";

// Types
interface Department {
  id: string;
  name: string;
  code: string;
}

interface Position {
  id: string;
  title: string;
  departmentId: string;
  baseSalary?: number;
}

const contractTypes: ContractType[] = ["CDI", "CDD", "STAGE", "FREELANCE", "PART_TIME"];
const genders: Gender[] = ["MALE", "FEMALE"];

const contractTypeLabels = {
  CDI: "CDI - Contrat à durée indéterminée",
  CDD: "CDD - Contrat à durée déterminée",
  STAGE: "Stage",
  FREELANCE: "Freelance/Consultant",
  PART_TIME: "Temps partiel",
};

const genderLabels = {
  MALE: "Homme",
  FEMALE: "Femme",
};

export default function NewEmployeePage() {
  const router = useRouter();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    dateOfBirth: "",
    gender: "" as Gender | "",
    address: "",
    departmentId: "",
    positionId: "",
    hireDate: new Date().toISOString().split("T")[0],
    contractEndDate: "",
    currentSalary: "",
    contractType: "CDI" as ContractType,
    workingHours: "40",
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Filtrer les postes par département sélectionné
    if (formData.departmentId) {
      const filtered = positions.filter(
        (position) => position.departmentId === formData.departmentId
      );
      setFilteredPositions(filtered);
      
      // Réinitialiser le poste sélectionné si il n'est plus valide
      if (formData.positionId && !filtered.find(p => p.id === formData.positionId)) {
        setFormData(prev => ({ ...prev, positionId: "" }));
      }
    } else {
      setFilteredPositions([]);
      setFormData(prev => ({ ...prev, positionId: "" }));
    }
  }, [formData.departmentId, positions]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les départements
      const departmentsResult = await getDepartments();
      if (departmentsResult.success) {
        setDepartments(departmentsResult.departments || []);
      }

      // Charger les postes
      const positionsResult = await getPositions();
      if (positionsResult.success) {
        setPositions(positionsResult.positions || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.firstName || !formData.lastName || !formData.departmentId || !formData.positionId) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    setSaving(true);
    try {
      const result = await createEmployee({
        ...formData,
        currentSalary: formData.currentSalary ? parseFloat(formData.currentSalary) : undefined,
        workingHours: parseFloat(formData.workingHours),
        gender: formData.gender || undefined,
        contractEndDate: formData.contractEndDate || undefined,
      });

      if (result.success) {
        toast.success("Employé créé avec succès");
        router.push("/dashboard/hr/employees");
      } else {
        toast.error(result.error || "Erreur lors de la création");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la création de l'employé");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/hr/employees">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Nouvel employé</h1>
            <p className="text-gray-600 mt-2">
              Ajouter un nouvel employé à l'organisation
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informations personnelles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Informations personnelles
              </CardTitle>
              <CardDescription>
                Informations de base de l'employé
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">Prénom *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, firstName: e.target.value }))
                    }
                    placeholder="Prénom"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Nom *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, lastName: e.target.value }))
                    }
                    placeholder="Nom de famille"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, email: e.target.value }))
                      }
                      placeholder="<EMAIL>"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="phone">Téléphone</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, phone: e.target.value }))
                      }
                      placeholder="+223 XX XX XX XX"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dateOfBirth">Date de naissance</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, dateOfBirth: e.target.value }))
                      }
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="gender">Genre</Label>
                  <Select
                    value={formData.gender}
                    onValueChange={(value: Gender) =>
                      setFormData((prev) => ({ ...prev, gender: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner le genre" />
                    </SelectTrigger>
                    <SelectContent>
                      {genders.map((gender) => (
                        <SelectItem key={gender} value={gender}>
                          {genderLabels[gender]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="address">Adresse</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, address: e.target.value }))
                    }
                    placeholder="Adresse complète"
                    className="pl-10"
                    rows={2}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informations professionnelles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Briefcase className="h-5 w-5 mr-2" />
                Informations professionnelles
              </CardTitle>
              <CardDescription>
                Poste et département de l'employé
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="departmentId">Département *</Label>
                  <Select
                    value={formData.departmentId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, departmentId: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un département" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          <div className="flex items-center">
                            <Building2 className="h-4 w-4 mr-2" />
                            {dept.name} ({dept.code})
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="positionId">Poste *</Label>
                  <Select
                    value={formData.positionId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, positionId: value }))
                    }
                    disabled={!formData.departmentId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un poste" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredPositions.map((position) => (
                        <SelectItem key={position.id} value={position.id}>
                          <div className="flex items-center">
                            <Briefcase className="h-4 w-4 mr-2" />
                            {position.title}
                            {position.baseSalary && (
                              <span className="ml-2 text-sm text-gray-500">
                                ({position.baseSalary.toLocaleString()} FCFA)
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!formData.departmentId && (
                    <p className="text-sm text-gray-500 mt-1">
                      Sélectionnez d'abord un département
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="hireDate">Date d'embauche *</Label>
                  <Input
                    id="hireDate"
                    type="date"
                    value={formData.hireDate}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, hireDate: e.target.value }))
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractType">Type de contrat *</Label>
                  <Select
                    value={formData.contractType}
                    onValueChange={(value: ContractType) =>
                      setFormData((prev) => ({ ...prev, contractType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {contractTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {contractTypeLabels[type]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {formData.contractType === "CDD" && (
                <div>
                  <Label htmlFor="contractEndDate">Date de fin de contrat</Label>
                  <Input
                    id="contractEndDate"
                    type="date"
                    value={formData.contractEndDate}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, contractEndDate: e.target.value }))
                    }
                  />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="currentSalary">Salaire (FCFA)</Label>
                  <Input
                    id="currentSalary"
                    type="number"
                    value={formData.currentSalary}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, currentSalary: e.target.value }))
                    }
                    placeholder="Ex: 500000"
                  />
                </div>
                <div>
                  <Label htmlFor="workingHours">Heures de travail/semaine</Label>
                  <Input
                    id="workingHours"
                    type="number"
                    value={formData.workingHours}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, workingHours: e.target.value }))
                    }
                    placeholder="40"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hr/employees">
                Annuler
              </Link>
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Créer l'employé
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
