"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Calendar,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  User,
  CalendarDays,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  getEmployees,
  getLeaveRequests,
  createLeaveRequest,
  updateLeaveRequestStatus,
} from "@/lib/actions/hr";

// Types
interface Employee {
  id: string;
  employeeNumber: string;
  firstName: string;
  lastName: string;
  department: {
    name: string;
  };
  position: {
    title: string;
  };
}

interface LeaveRequest {
  id: string;
  requestNumber: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  reason: string;
  status: string;
  employee: {
    firstName: string;
    lastName: string;
    employeeNumber: string;
    department: {
      name: string;
    };
  };
  createdAt: string;
}

const leaveTypes = [
  { value: "ANNUAL", label: "Congé annuel" },
  { value: "SICK", label: "Congé maladie" },
  { value: "MATERNITY", label: "Congé maternité" },
  { value: "PATERNITY", label: "Congé paternité" },
  { value: "EMERGENCY", label: "Congé d'urgence" },
  { value: "UNPAID", label: "Congé sans solde" },
  { value: "STUDY", label: "Congé d'études" },
];

const leaveStatusLabels = {
  PENDING: "En attente",
  APPROVED: "Approuvé",
  REJECTED: "Refusé",
  CANCELLED: "Annulé",
};

const leaveStatusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
};

export default function LeavesPage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [formData, setFormData] = useState({
    employeeId: "",
    leaveType: "",
    startDate: "",
    endDate: "",
    reason: "",
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les employés actifs
      const employeesResult = await getEmployees({ status: "ACTIVE" });
      if (employeesResult.success) {
        setEmployees(employeesResult.employees || []);
      }

      // Charger les demandes de congés
      const leaveRequestsResult = await getLeaveRequests();
      if (leaveRequestsResult.success) {
        setLeaveRequests(leaveRequestsResult.leaveRequests || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const calculateDays = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.employeeId ||
      !formData.leaveType ||
      !formData.startDate ||
      !formData.endDate ||
      !formData.reason
    ) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    setSaving(true);
    try {
      const result = await createLeaveRequest({
        employeeId: formData.employeeId,
        leaveType: formData.leaveType as any,
        startDate: formData.startDate,
        endDate: formData.endDate,
        reason: formData.reason,
      });

      if (result.success) {
        toast.success("Demande de congé créée avec succès");
        setIsDialogOpen(false);
        setFormData({
          employeeId: "",
          leaveType: "",
          startDate: "",
          endDate: "",
          reason: "",
        });
        loadData();
      } else {
        toast.error(result.error || "Erreur lors de la création");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la création de la demande");
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleLeaveRequestAction = async (
    requestId: string,
    action: "APPROVED" | "REJECTED"
  ) => {
    try {
      const result = await updateLeaveRequestStatus(requestId, action);

      if (result.success) {
        toast.success(
          `Demande ${
            action === "APPROVED" ? "approuvée" : "refusée"
          } avec succès`
        );
        loadData();
      } else {
        toast.error(result.error || "Erreur lors de la mise à jour");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la mise à jour de la demande");
    }
  };

  // Calculer les statistiques
  const stats = {
    total: leaveRequests.length,
    pending: leaveRequests.filter((r) => r.status === "PENDING").length,
    approved: leaveRequests.filter((r) => r.status === "APPROVED").length,
    rejected: leaveRequests.filter((r) => r.status === "REJECTED").length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/hr">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Demandes de congés
              </h1>
              <p className="text-gray-600 mt-2">
                Gestion des congés et absences
              </p>
            </div>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle demande
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Nouvelle demande de congé</DialogTitle>
                <DialogDescription>
                  Créer une nouvelle demande de congé pour un employé.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="space-y-4 py-4">
                  <div>
                    <Label htmlFor="employeeId">Employé *</Label>
                    <Select
                      value={formData.employeeId}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, employeeId: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un employé" />
                      </SelectTrigger>
                      <SelectContent>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            {employee.firstName} {employee.lastName} (
                            {employee.employeeNumber})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="leaveType">Type de congé *</Label>
                    <Select
                      value={formData.leaveType}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, leaveType: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner le type" />
                      </SelectTrigger>
                      <SelectContent>
                        {leaveTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startDate">Date de début *</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={formData.startDate}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            startDate: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="endDate">Date de fin *</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={formData.endDate}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            endDate: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>
                  </div>
                  {formData.startDate && formData.endDate && (
                    <div className="text-sm text-gray-600">
                      Durée:{" "}
                      {calculateDays(formData.startDate, formData.endDate)}{" "}
                      jour(s)
                    </div>
                  )}
                  <div>
                    <Label htmlFor="reason">Motif *</Label>
                    <Textarea
                      id="reason"
                      value={formData.reason}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          reason: e.target.value,
                        }))
                      }
                      placeholder="Motif de la demande de congé..."
                      rows={3}
                      required
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit" disabled={saving}>
                    {saving ? "Création..." : "Créer"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Demandes
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">Toutes demandes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En attente</CardTitle>
              <AlertCircle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats.pending}
              </div>
              <p className="text-xs text-muted-foreground">À traiter</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approuvées</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.approved}
              </div>
              <p className="text-xs text-muted-foreground">Validées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Refusées</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats.rejected}
              </div>
              <p className="text-xs text-muted-foreground">Non validées</p>
            </CardContent>
          </Card>
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue="all" className="space-y-6">
          <TabsList>
            <TabsTrigger value="all">Toutes les demandes</TabsTrigger>
            <TabsTrigger value="pending">En attente</TabsTrigger>
            <TabsTrigger value="calendar">Calendrier</TabsTrigger>
          </TabsList>

          {/* Toutes les demandes */}
          <TabsContent value="all">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Demandes de congés ({leaveRequests.length})
                </CardTitle>
                <CardDescription>
                  Toutes les demandes de congés de l'organisation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {leaveRequests.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucune demande de congé
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Les demandes de congés apparaîtront ici.
                    </p>
                    <Button onClick={() => setIsDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Créer une demande
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Demande</TableHead>
                        <TableHead>Employé</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Période</TableHead>
                        <TableHead>Durée</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Date demande</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leaveRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell className="font-mono text-sm">
                            {request.requestNumber}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">
                                {request.employee.firstName}{" "}
                                {request.employee.lastName}
                              </p>
                              <p className="text-sm text-gray-500">
                                {request.employee.department.name}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {
                                leaveTypes.find(
                                  (t) => t.value === request.leaveType
                                )?.label
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <p>{formatDate(request.startDate)}</p>
                              <p className="text-gray-500">
                                au {formatDate(request.endDate)}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <CalendarDays className="h-4 w-4 mr-1 text-gray-400" />
                              {request.totalDays} jour(s)
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                leaveStatusColors[
                                  request.status as keyof typeof leaveStatusColors
                                ]
                              }
                            >
                              {
                                leaveStatusLabels[
                                  request.status as keyof typeof leaveStatusLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>{formatDate(request.createdAt)}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              {request.status === "PENDING" && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-green-600"
                                    onClick={() =>
                                      handleLeaveRequestAction(
                                        request.id,
                                        "APPROVED"
                                      )
                                    }
                                  >
                                    <CheckCircle className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-600"
                                    onClick={() =>
                                      handleLeaveRequestAction(
                                        request.id,
                                        "REJECTED"
                                      )
                                    }
                                  >
                                    <XCircle className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Demandes en attente */}
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  Demandes en attente d'approbation
                </CardTitle>
                <CardDescription>
                  Demandes nécessitant une action de votre part
                </CardDescription>
              </CardHeader>
              <CardContent>
                {leaveRequests.filter((r) => r.status === "PENDING").length ===
                0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucune demande en attente
                    </h3>
                    <p className="text-gray-600">
                      Toutes les demandes ont été traitées.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {leaveRequests
                      .filter((r) => r.status === "PENDING")
                      .map((request) => (
                        <Card key={request.id}>
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-4">
                                  <div>
                                    <h4 className="font-medium">
                                      {request.employee.firstName}{" "}
                                      {request.employee.lastName}
                                    </h4>
                                    <p className="text-sm text-gray-500">
                                      {request.employee.department.name}
                                    </p>
                                  </div>
                                  <Badge variant="outline">
                                    {
                                      leaveTypes.find(
                                        (t) => t.value === request.leaveType
                                      )?.label
                                    }
                                  </Badge>
                                  <div className="text-sm">
                                    <p>
                                      {formatDate(request.startDate)} -{" "}
                                      {formatDate(request.endDate)}
                                    </p>
                                    <p className="text-gray-500">
                                      {request.totalDays} jour(s)
                                    </p>
                                  </div>
                                </div>
                                <p className="text-sm text-gray-600 mt-2">
                                  {request.reason}
                                </p>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  className="bg-green-600 hover:bg-green-700"
                                  onClick={() =>
                                    handleLeaveRequestAction(
                                      request.id,
                                      "APPROVED"
                                    )
                                  }
                                >
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Approuver
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() =>
                                    handleLeaveRequestAction(
                                      request.id,
                                      "REJECTED"
                                    )
                                  }
                                >
                                  <XCircle className="h-4 w-4 mr-1" />
                                  Refuser
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Calendrier */}
          <TabsContent value="calendar">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Calendrier des congés
                </CardTitle>
                <CardDescription>
                  Vue calendrier des congés approuvés
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Calendrier à venir
                  </h3>
                  <p className="text-gray-600 mb-4">
                    La vue calendrier sera disponible prochainement.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
