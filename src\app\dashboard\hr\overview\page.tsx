"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Users,
  Building2,
  Briefcase,
  Clock,
  Calendar,
  CheckCircle,
  TrendingUp,
  Award,
  Target,
  Zap,
  Shield,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";

const features = [
  {
    icon: Building2,
    title: "Gestion des Départements",
    description: "Organisation hiérarchique avec managers assignés",
    status: "Complet",
    color: "bg-green-100 text-green-800",
  },
  {
    icon: Briefcase,
    title: "Gestion des Postes",
    description: "Définition des rôles avec salaires de base",
    status: "Complet",
    color: "bg-green-100 text-green-800",
  },
  {
    icon: Users,
    title: "Gestion des Employés",
    description: "CRUD complet avec numérotation automatique",
    status: "Complet",
    color: "bg-green-100 text-green-800",
  },
  {
    icon: Clock,
    title: "Système de Pointage",
    description: "Suivi des présences avec calcul d'heures",
    status: "Complet",
    color: "bg-green-100 text-green-800",
  },
  {
    icon: Calendar,
    title: "Gestion des Congés",
    description: "Workflow d'approbation des demandes",
    status: "Complet",
    color: "bg-green-100 text-green-800",
  },
];

const stats = [
  {
    icon: Users,
    label: "Employés Gérés",
    value: "8",
    description: "Employés actifs dans le système",
  },
  {
    icon: Building2,
    label: "Départements",
    value: "5",
    description: "Départements organisés",
  },
  {
    icon: Briefcase,
    label: "Postes Définis",
    value: "11",
    description: "Rôles et fonctions",
  },
  {
    icon: CheckCircle,
    label: "Fonctionnalités",
    value: "100%",
    description: "Module complet",
  },
];

const benefits = [
  {
    icon: Zap,
    title: "Efficacité Opérationnelle",
    description: "Automatisation des processus RH et réduction des tâches manuelles",
  },
  {
    icon: Shield,
    title: "Conformité Réglementaire",
    description: "Respect des lois du travail maliennes et traçabilité complète",
  },
  {
    icon: Target,
    title: "Gestion Centralisée",
    description: "Toutes les données RH dans un seul système intégré",
  },
  {
    icon: TrendingUp,
    title: "Analyses et Rapports",
    description: "Statistiques en temps réel pour la prise de décision",
  },
];

export default function HROverviewPage() {
  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/hr">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Link>
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-gray-900">
              Module Personnel/RH
            </h1>
            <p className="text-xl text-gray-600 mt-2">
              Solution complète de gestion des ressources humaines
            </p>
          </div>
        </div>

        {/* Badge de statut */}
        <div className="flex items-center space-x-4">
          <Badge className="bg-green-100 text-green-800 px-4 py-2 text-lg">
            <CheckCircle className="h-5 w-5 mr-2" />
            Module Complet et Opérationnel
          </Badge>
          <Badge variant="outline" className="px-4 py-2 text-lg">
            <Award className="h-5 w-5 mr-2" />
            Adapté à l'Afrique
          </Badge>
        </div>

        {/* Statistiques principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="border-l-4 border-l-blue-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.label}
                </CardTitle>
                <stat.icon className="h-5 w-5 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {stat.value}
                </div>
                <p className="text-sm text-gray-600">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Fonctionnalités principales */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Fonctionnalités Principales</CardTitle>
            <CardDescription>
              Toutes les fonctionnalités nécessaires pour une gestion RH moderne
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 rounded-lg border">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {feature.description}
                    </p>
                    <Badge className={feature.color}>
                      {feature.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Avantages business */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Avantages Business</CardTitle>
            <CardDescription>
              Impact positif sur la gestion hospitalière
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 rounded-lg bg-blue-50">
                  <div className="flex-shrink-0">
                    <benefit.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {benefit.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Données de démonstration */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Données de Démonstration</CardTitle>
            <CardDescription>
              Le système contient des données réalistes pour tester toutes les fonctionnalités
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 rounded-lg bg-green-50">
                <Building2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">5 Départements</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Médecine générale</li>
                  <li>• Laboratoire</li>
                  <li>• Pharmacie</li>
                  <li>• Administration</li>
                  <li>• Urgences</li>
                </ul>
              </div>
              
              <div className="text-center p-6 rounded-lg bg-blue-50">
                <Briefcase className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">11 Postes</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Médecins spécialisés</li>
                  <li>• Personnel soignant</li>
                  <li>• Techniciens</li>
                  <li>• Administration</li>
                  <li>• Support</li>
                </ul>
              </div>
              
              <div className="text-center p-6 rounded-lg bg-purple-50">
                <Users className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">8 Employés</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Profils complets</li>
                  <li>• Salaires réalistes</li>
                  <li>• Contrats variés</li>
                  <li>• Managers assignés</li>
                  <li>• Statuts actifs</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Spécificités africaines */}
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-2xl text-orange-900">
              Adaptation à l'Afrique
            </CardTitle>
            <CardDescription className="text-orange-700">
              Conçu spécifiquement pour les réalités des hôpitaux africains
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-orange-900 mb-3">💰 Gestion Financière</h3>
                <ul className="text-sm text-orange-800 space-y-2">
                  <li>• Salaires en FCFA</li>
                  <li>• Types de contrats locaux (CDI, CDD, Stage)</li>
                  <li>• Heures de travail flexibles</li>
                  <li>• Gestion des heures supplémentaires</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-orange-900 mb-3">📅 Congés Adaptés</h3>
                <ul className="text-sm text-orange-800 space-y-2">
                  <li>• Congés selon la législation malienne</li>
                  <li>• Congés maternité/paternité</li>
                  <li>• Congés d'urgence familiale</li>
                  <li>• Congés sans solde</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-orange-900 mb-3">🏥 Postes Hospitaliers</h3>
                <ul className="text-sm text-orange-800 space-y-2">
                  <li>• Médecins généralistes et spécialistes</li>
                  <li>• Personnel soignant qualifié</li>
                  <li>• Techniciens de laboratoire</li>
                  <li>• Support administratif</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-orange-900 mb-3">🔒 Conformité</h3>
                <ul className="text-sm text-orange-800 space-y-2">
                  <li>• Respect du droit du travail malien</li>
                  <li>• Traçabilité complète</li>
                  <li>• Sécurité des données</li>
                  <li>• Multi-tenant strict</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions rapides */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Actions Rapides</CardTitle>
            <CardDescription>
              Accès direct aux fonctionnalités principales
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button asChild className="h-20 flex-col">
                <Link href="/dashboard/hr/employees">
                  <Users className="h-6 w-6 mb-2" />
                  Voir Employés
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/dashboard/hr/departments">
                  <Building2 className="h-6 w-6 mb-2" />
                  Départements
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/dashboard/hr/attendance">
                  <Clock className="h-6 w-6 mb-2" />
                  Pointage
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="h-20 flex-col">
                <Link href="/dashboard/hr/leaves">
                  <Calendar className="h-6 w-6 mb-2" />
                  Congés
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center py-8 border-t">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Module Personnel/RH - GlobalCare Solutions
          </h3>
          <p className="text-gray-600">
            Solution moderne et complète pour la gestion des ressources humaines hospitalières
          </p>
          <div className="mt-4">
            <Badge className="bg-green-100 text-green-800">
              ✅ Prêt pour la Production
            </Badge>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
