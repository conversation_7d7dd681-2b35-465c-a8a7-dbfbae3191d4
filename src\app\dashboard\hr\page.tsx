"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  Building2,
  UserPlus,
  Settings,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Briefcase,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getDepartments, getPositions, getEmployees } from "@/lib/actions/hr";

// Types
interface Department {
  id: string;
  name: string;
  code: string;
  description?: string;
  manager?: {
    firstName: string;
    lastName: string;
  };
  _count: {
    employees: number;
    positions: number;
  };
}

interface Position {
  id: string;
  title: string;
  code: string;
  description?: string;
  baseSalary?: number;
  department: {
    name: string;
  };
  _count: {
    employees: number;
  };
}

interface Employee {
  id: string;
  employeeNumber: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  status: string;
  hireDate: string;
  currentSalary?: number;
  contractType: string;
  department: {
    name: string;
  };
  position: {
    title: string;
  };
  user?: {
    email: string;
    role: string;
  };
}

const statusLabels = {
  ACTIVE: "Actif",
  INACTIVE: "Inactif",
  ON_LEAVE: "En congé",
  SUSPENDED: "Suspendu",
  TERMINATED: "Licencié",
};

const statusColors = {
  ACTIVE: "bg-green-100 text-green-800",
  INACTIVE: "bg-gray-100 text-gray-800",
  ON_LEAVE: "bg-blue-100 text-blue-800",
  SUSPENDED: "bg-orange-100 text-orange-800",
  TERMINATED: "bg-red-100 text-red-800",
};

const contractTypeLabels = {
  CDI: "CDI",
  CDD: "CDD",
  STAGE: "Stage",
  FREELANCE: "Freelance",
  PART_TIME: "Temps partiel",
};

export default function HRPage() {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les départements
      const departmentsResult = await getDepartments();
      if (departmentsResult.success) {
        setDepartments(departmentsResult.departments || []);
      }

      // Charger les postes
      const positionsResult = await getPositions();
      if (positionsResult.success) {
        setPositions(positionsResult.positions || []);
      }

      // Charger les employés
      const employeesResult = await getEmployees();
      if (employeesResult.success) {
        setEmployees(employeesResult.employees || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données RH");
    } finally {
      setLoading(false);
    }
  };

  // Calculer les statistiques
  const stats = {
    totalEmployees: employees.length,
    activeEmployees: employees.filter((e) => e.status === "ACTIVE").length,
    totalDepartments: departments.length,
    totalPositions: positions.length,
    onLeave: employees.filter((e) => e.status === "ON_LEAVE").length,
    newHiresThisMonth: employees.filter((e) => {
      const hireDate = new Date(e.hireDate);
      const now = new Date();
      return (
        hireDate.getMonth() === now.getMonth() &&
        hireDate.getFullYear() === now.getFullYear()
      );
    }).length,
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Ressources Humaines
            </h1>
            <p className="text-gray-600 mt-2">
              Gestion du personnel et des départements
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/hr/departments">
                <Building2 className="h-4 w-4 mr-2" />
                Départements
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/hr/positions">
                <Briefcase className="h-4 w-4 mr-2" />
                Postes
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/hr/attendance">
                <Clock className="h-4 w-4 mr-2" />
                Pointage
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/hr/leaves">
                <Calendar className="h-4 w-4 mr-2" />
                Congés
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/hr/employees/new">
                <UserPlus className="h-4 w-4 mr-2" />
                Nouvel employé
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Employés
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEmployees}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeEmployees} actifs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Départements
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDepartments}</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalPositions} postes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En congé</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.onLeave}</div>
              <p className="text-xs text-muted-foreground">Employés absents</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Nouvelles embauches
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.newHiresThisMonth}
              </div>
              <p className="text-xs text-muted-foreground">Ce mois-ci</p>
            </CardContent>
          </Card>
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue="employees" className="space-y-6">
          <TabsList>
            <TabsTrigger value="employees">Employés</TabsTrigger>
            <TabsTrigger value="departments">Départements</TabsTrigger>
            <TabsTrigger value="positions">Postes</TabsTrigger>
          </TabsList>

          {/* Employés */}
          <TabsContent value="employees">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Employés récents
                </CardTitle>
                <CardDescription>Les derniers employés ajoutés</CardDescription>
              </CardHeader>
              <CardContent>
                {employees.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun employé
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Commencez par ajouter des employés à votre organisation.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/hr/employees/new">
                        Ajouter un employé
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Employé</TableHead>
                        <TableHead>Nom</TableHead>
                        <TableHead>Département</TableHead>
                        <TableHead>Poste</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Date d'embauche</TableHead>
                        <TableHead>Contrat</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {employees.slice(0, 10).map((employee) => (
                        <TableRow key={employee.id}>
                          <TableCell className="font-mono text-sm">
                            {employee.employeeNumber}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">
                                {employee.firstName} {employee.lastName}
                              </p>
                              {employee.email && (
                                <p className="text-sm text-gray-500">
                                  {employee.email}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{employee.department.name}</TableCell>
                          <TableCell>{employee.position.title}</TableCell>
                          <TableCell>
                            <Badge
                              className={
                                statusColors[
                                  employee.status as keyof typeof statusColors
                                ]
                              }
                            >
                              {
                                statusLabels[
                                  employee.status as keyof typeof statusLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>{formatDate(employee.hireDate)}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {
                                contractTypeLabels[
                                  employee.contractType as keyof typeof contractTypeLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Départements */}
          <TabsContent value="departments">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="h-5 w-5 mr-2" />
                  Départements
                </CardTitle>
                <CardDescription>Organisation par départements</CardDescription>
              </CardHeader>
              <CardContent>
                {departments.length === 0 ? (
                  <div className="text-center py-8">
                    <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun département
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Créez des départements pour organiser votre personnel.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/hr/departments">
                        Gérer les départements
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {departments.map((department) => (
                      <Card key={department.id}>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">
                            {department.name}
                          </CardTitle>
                          <CardDescription>
                            Code: {department.code}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Employés:</span>
                              <span className="font-medium">
                                {department._count.employees}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span>Postes:</span>
                              <span className="font-medium">
                                {department._count.positions}
                              </span>
                            </div>
                            {department.manager && (
                              <div className="flex justify-between text-sm">
                                <span>Manager:</span>
                                <span className="font-medium">
                                  {department.manager.firstName}{" "}
                                  {department.manager.lastName}
                                </span>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Postes */}
          <TabsContent value="positions">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Briefcase className="h-5 w-5 mr-2" />
                  Postes disponibles
                </CardTitle>
                <CardDescription>
                  Fonctions et rôles dans l'organisation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {positions.length === 0 ? (
                  <div className="text-center py-8">
                    <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun poste défini
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Définissez les postes disponibles dans votre organisation.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/hr/positions">
                        Gérer les postes
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Code</TableHead>
                        <TableHead>Titre du poste</TableHead>
                        <TableHead>Département</TableHead>
                        <TableHead>Salaire de base</TableHead>
                        <TableHead>Employés</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {positions.map((position) => (
                        <TableRow key={position.id}>
                          <TableCell className="font-mono text-sm">
                            {position.code}
                          </TableCell>
                          <TableCell className="font-medium">
                            {position.title}
                          </TableCell>
                          <TableCell>{position.department.name}</TableCell>
                          <TableCell>
                            {position.baseSalary
                              ? `${position.baseSalary.toLocaleString()} FCFA`
                              : "-"}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {position._count.employees}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
