"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowLeft,
  Briefcase,
  Plus,
  Users,
  Building2,
  Edit,
  DollarSign,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getDepartments, getPositions, createPosition } from "@/lib/actions/hr";

// Types
interface Department {
  id: string;
  name: string;
  code: string;
}

interface Position {
  id: string;
  title: string;
  code: string;
  description?: string;
  baseSalary?: number;
  department: {
    name: string;
  };
  _count: {
    employees: number;
  };
}

export default function PositionsPage() {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [formData, setFormData] = useState({
    title: "",
    code: "",
    description: "",
    departmentId: "",
    baseSalary: "",
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les départements
      const departmentsResult = await getDepartments();
      if (departmentsResult.success) {
        setDepartments(departmentsResult.departments || []);
      }

      // Charger les postes
      const positionsResult = await getPositions();
      if (positionsResult.success) {
        setPositions(positionsResult.positions || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.code || !formData.departmentId) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    setSaving(true);
    try {
      const result = await createPosition({
        ...formData,
        baseSalary: formData.baseSalary ? parseFloat(formData.baseSalary) : undefined,
      });

      if (result.success) {
        toast.success("Poste créé avec succès");
        setIsDialogOpen(false);
        setFormData({
          title: "",
          code: "",
          description: "",
          departmentId: "",
          baseSalary: "",
        });
        loadData();
      } else {
        toast.error(result.error || "Erreur lors de la création");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la création du poste");
    } finally {
      setSaving(false);
    }
  };

  // Grouper les postes par département
  const positionsByDepartment = positions.reduce((acc, position) => {
    const deptName = position.department.name;
    if (!acc[deptName]) {
      acc[deptName] = [];
    }
    acc[deptName].push(position);
    return acc;
  }, {} as Record<string, Position[]>);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/hr">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Postes</h1>
              <p className="text-gray-600 mt-2">
                Gestion des postes et fonctions
              </p>
            </div>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau poste
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Créer un nouveau poste</DialogTitle>
                <DialogDescription>
                  Ajoutez un nouveau poste à votre organisation.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="space-y-4 py-4">
                  <div>
                    <Label htmlFor="title">Titre du poste *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, title: e.target.value }))
                      }
                      placeholder="Ex: Médecin généraliste"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="code">Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, code: e.target.value.toUpperCase() }))
                      }
                      placeholder="Ex: MED_GEN"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="departmentId">Département *</Label>
                    <Select
                      value={formData.departmentId}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, departmentId: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un département" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id}>
                            {dept.name} ({dept.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="baseSalary">Salaire de base (FCFA)</Label>
                    <Input
                      id="baseSalary"
                      type="number"
                      value={formData.baseSalary}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, baseSalary: e.target.value }))
                      }
                      placeholder="Ex: 500000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, description: e.target.value }))
                      }
                      placeholder="Description du poste..."
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit" disabled={saving}>
                    {saving ? "Création..." : "Créer"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Postes</CardTitle>
              <Briefcase className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{positions.length}</div>
              <p className="text-xs text-muted-foreground">Postes définis</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Départements</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{departments.length}</div>
              <p className="text-xs text-muted-foreground">Avec postes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Employés</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {positions.reduce((sum, pos) => sum + pos._count.employees, 0)}
              </div>
              <p className="text-xs text-muted-foreground">Assignés aux postes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Salaire Moyen</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {positions.filter(p => p.baseSalary).length > 0
                  ? Math.round(
                      positions
                        .filter(p => p.baseSalary)
                        .reduce((sum, pos) => sum + (pos.baseSalary || 0), 0) /
                      positions.filter(p => p.baseSalary).length
                    ).toLocaleString()
                  : "0"}
              </div>
              <p className="text-xs text-muted-foreground">FCFA</p>
            </CardContent>
          </Card>
        </div>

        {/* Liste des postes par département */}
        <div className="space-y-6">
          {departments.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun département
                </h3>
                <p className="text-gray-600 mb-4">
                  Créez d'abord des départements avant d'ajouter des postes.
                </p>
                <Button asChild>
                  <Link href="/dashboard/hr/departments">
                    Gérer les départements
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : Object.keys(positionsByDepartment).length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun poste défini
                </h3>
                <p className="text-gray-600 mb-4">
                  Créez votre premier poste pour organiser votre personnel.
                </p>
                <Button onClick={() => setIsDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Créer un poste
                </Button>
              </CardContent>
            </Card>
          ) : (
            Object.entries(positionsByDepartment).map(([deptName, deptPositions]) => (
              <Card key={deptName}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2" />
                    {deptName}
                  </CardTitle>
                  <CardDescription>
                    {deptPositions.length} poste(s) dans ce département
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Code</TableHead>
                        <TableHead>Titre</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Salaire de base</TableHead>
                        <TableHead>Employés</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {deptPositions.map((position) => (
                        <TableRow key={position.id}>
                          <TableCell className="font-mono text-sm">
                            {position.code}
                          </TableCell>
                          <TableCell className="font-medium">
                            {position.title}
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {position.description || "-"}
                          </TableCell>
                          <TableCell>
                            {position.baseSalary ? (
                              <span className="font-medium">
                                {position.baseSalary.toLocaleString()} FCFA
                              </span>
                            ) : (
                              <span className="text-gray-500">Non défini</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {position._count.employees}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
