"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ArrowLeft,
  TestTube,
  User,
  Search,
  Plus,
  Trash2,
  Calculator,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabTestTypes, createLabOrder } from "@/lib/actions/laboratory";
import { getAllPatients } from "@/lib/actions/patients";
import { LabUrgency } from "@prisma/client";

// Types
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  patientNumber: string;
  phone?: string | null;
}

interface LabTestType {
  id: string;
  name: string;
  code: string;
  category: string;
  price: number;
  description?: string;
  sampleType?: string;
  preparation?: string;
}

interface SelectedTest {
  testType: LabTestType;
  urgency: LabUrgency;
  clinicalInfo?: string;
  instructions?: string;
}

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "Urgence",
};

const urgencyColors = {
  NORMAL: "bg-green-100 text-green-800",
  URGENT: "bg-orange-100 text-orange-800",
  EMERGENCY: "bg-red-100 text-red-800",
};

export default function NewLabOrderPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [patients, setPatients] = useState<Patient[]>([]);
  const [labTestTypes, setLabTestTypes] = useState<LabTestType[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [filteredTests, setFilteredTests] = useState<LabTestType[]>([]);

  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [selectedTests, setSelectedTests] = useState<SelectedTest[]>([]);

  const [patientSearch, setPatientSearch] = useState("");
  const [testSearch, setTestSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("ALL");

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isExternal, setIsExternal] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Pré-remplir avec les paramètres URL si fournis
    const patientId = searchParams.get("patientId");
    const consultationId = searchParams.get("consultationId");

    if (patientId && patients.length > 0) {
      const patient = patients.find((p) => p.id === patientId);
      if (patient) {
        setSelectedPatient(patient);
      }
    }
  }, [searchParams, patients]);

  useEffect(() => {
    filterPatients();
  }, [patients, patientSearch]);

  useEffect(() => {
    filterTests();
  }, [labTestTypes, testSearch, selectedCategory]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les patients
      const patientsResult = await getAllPatients();
      if (patientsResult.success) {
        setPatients(patientsResult.patients || []);
      }

      // Charger les types d'examens
      const testsResult = await getLabTestTypes();
      if (testsResult.success) {
        setLabTestTypes(testsResult.labTestTypes || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const filterPatients = () => {
    if (!patientSearch) {
      setFilteredPatients(patients);
      return;
    }

    const filtered = patients.filter(
      (patient) =>
        patient.firstName.toLowerCase().includes(patientSearch.toLowerCase()) ||
        patient.lastName.toLowerCase().includes(patientSearch.toLowerCase()) ||
        patient.patientNumber
          .toLowerCase()
          .includes(patientSearch.toLowerCase())
    );
    setFilteredPatients(filtered);
  };

  const filterTests = () => {
    let filtered = labTestTypes;

    // Filtrer par recherche
    if (testSearch) {
      filtered = filtered.filter(
        (test) =>
          test.name.toLowerCase().includes(testSearch.toLowerCase()) ||
          test.code.toLowerCase().includes(testSearch.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (selectedCategory !== "ALL") {
      filtered = filtered.filter((test) => test.category === selectedCategory);
    }

    setFilteredTests(filtered);
  };

  const selectPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setPatientSearch("");
  };

  const addTest = (testType: LabTestType) => {
    // Vérifier si le test n'est pas déjà sélectionné
    if (selectedTests.some((st) => st.testType.id === testType.id)) {
      toast.error("Cet examen est déjà sélectionné");
      return;
    }

    const newTest: SelectedTest = {
      testType,
      urgency: "NORMAL",
      clinicalInfo: "",
      instructions: "",
    };

    setSelectedTests((prev) => [...prev, newTest]);
    toast.success(`${testType.name} ajouté`);
  };

  const removeTest = (testTypeId: string) => {
    setSelectedTests((prev) =>
      prev.filter((st) => st.testType.id !== testTypeId)
    );
  };

  const updateTestUrgency = (testTypeId: string, urgency: LabUrgency) => {
    setSelectedTests((prev) =>
      prev.map((st) =>
        st.testType.id === testTypeId ? { ...st, urgency } : st
      )
    );
  };

  const updateTestClinicalInfo = (testTypeId: string, clinicalInfo: string) => {
    setSelectedTests((prev) =>
      prev.map((st) =>
        st.testType.id === testTypeId ? { ...st, clinicalInfo } : st
      )
    );
  };

  const calculateTotal = () => {
    return selectedTests.reduce((total, st) => total + st.testType.price, 0);
  };

  const handleSubmit = async () => {
    if (!selectedPatient) {
      toast.error("Veuillez sélectionner un patient");
      return;
    }

    if (selectedTests.length === 0) {
      toast.error("Veuillez sélectionner au moins un examen");
      return;
    }

    setSaving(true);
    try {
      const consultationId = searchParams.get("consultationId");

      // Créer une prescription pour chaque examen sélectionné
      const promises = selectedTests.map((selectedTest) =>
        createLabOrder({
          patientId: selectedPatient.id,
          testTypeId: selectedTest.testType.id,
          consultationId: consultationId || undefined,
          urgency: selectedTest.urgency,
          clinicalInfo: selectedTest.clinicalInfo || undefined,
          instructions: selectedTest.instructions || undefined,
          isExternal: isExternal,
        })
      );

      const results = await Promise.all(promises);

      // Vérifier si toutes les prescriptions ont réussi
      const failedResults = results.filter((result) => !result.success);

      if (failedResults.length > 0) {
        toast.error(`${failedResults.length} prescription(s) ont échoué`);
        return;
      }

      toast.success(
        `${selectedTests.length} prescription(s) d'examen créée(s) avec succès`
      );

      // Rediriger vers la page de paiement laboratoire ou la liste des prescriptions
      router.push("/dashboard/laboratory");
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la création des prescriptions");
    } finally {
      setSaving(false);
    }
  };

  const categories = [...new Set(labTestTypes.map((test) => test.category))];

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Nouvelle prescription d'examen
              </h1>
              <p className="text-gray-600 mt-2">
                Prescrire des examens de laboratoire pour un patient
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sélection du patient */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Sélection du patient
              </CardTitle>
              <CardDescription>
                Recherchez et sélectionnez le patient
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedPatient ? (
                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-blue-900">
                        {selectedPatient.firstName} {selectedPatient.lastName}
                      </p>
                      <p className="text-sm text-blue-700">
                        N° {selectedPatient.patientNumber}
                      </p>
                      {selectedPatient.phone && (
                        <p className="text-sm text-blue-600">
                          {selectedPatient.phone}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedPatient(null)}
                    >
                      Changer
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Rechercher un patient..."
                      value={patientSearch}
                      onChange={(e) => setPatientSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {loading ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                  ) : (
                    <div className="max-h-60 overflow-y-auto space-y-2">
                      {filteredPatients.length === 0 ? (
                        <p className="text-center text-gray-500 py-4">
                          {patientSearch
                            ? "Aucun patient trouvé"
                            : "Aucun patient"}
                        </p>
                      ) : (
                        filteredPatients.slice(0, 10).map((patient) => (
                          <div
                            key={patient.id}
                            className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                            onClick={() => selectPatient(patient)}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  {patient.firstName} {patient.lastName}
                                </p>
                                <p className="text-sm text-gray-600">
                                  N° {patient.patientNumber}
                                </p>
                              </div>
                              <Button variant="ghost" size="sm">
                                Sélectionner
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>

          {/* Sélection des examens */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TestTube className="h-5 w-5 mr-2" />
                Examens disponibles
              </CardTitle>
              <CardDescription>
                Sélectionnez les examens à prescrire
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Filtres */}
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher un examen..."
                    value={testSearch}
                    onChange={(e) => setTestSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Toutes les catégories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Liste des examens */}
              <div className="max-h-80 overflow-y-auto space-y-2">
                {filteredTests.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">
                    {labTestTypes.length === 0
                      ? "Aucun examen configuré"
                      : "Aucun examen trouvé"}
                  </p>
                ) : (
                  filteredTests.map((test) => (
                    <div
                      key={test.id}
                      className="p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <p className="font-medium">{test.name}</p>
                            <Badge variant="outline" className="text-xs">
                              {test.code}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            {test.category}
                          </p>
                          <p className="text-sm font-medium text-green-600">
                            {test.price.toLocaleString()} FCFA
                          </p>
                          {test.sampleType && (
                            <p className="text-xs text-gray-500">
                              Échantillon: {test.sampleType}
                            </p>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addTest(test)}
                          disabled={selectedTests.some(
                            (st) => st.testType.id === test.id
                          )}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          {selectedTests.some(
                            (st) => st.testType.id === test.id
                          )
                            ? "Ajouté"
                            : "Ajouter"}
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Examens sélectionnés */}
        {selectedTests.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Examens sélectionnés ({selectedTests.length})
                </span>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Total</p>
                  <p className="text-lg font-bold text-green-600">
                    {calculateTotal().toLocaleString()} FCFA
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Examen</TableHead>
                    <TableHead>Urgence</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Informations cliniques</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedTests.map((selectedTest) => (
                    <TableRow key={selectedTest.testType.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {selectedTest.testType.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {selectedTest.testType.category}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={selectedTest.urgency}
                          onValueChange={(value: LabUrgency) =>
                            updateTestUrgency(selectedTest.testType.id, value)
                          }
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(urgencyLabels).map(
                              ([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              )
                            )}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell className="font-medium">
                        {selectedTest.testType.price.toLocaleString()} FCFA
                      </TableCell>
                      <TableCell>
                        <Input
                          placeholder="Informations cliniques..."
                          value={selectedTest.clinicalInfo || ""}
                          onChange={(e) =>
                            updateTestClinicalInfo(
                              selectedTest.testType.id,
                              e.target.value
                            )
                          }
                          className="w-48"
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTest(selectedTest.testType.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex items-center justify-between mt-6">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isExternal"
                    checked={isExternal}
                    onChange={(e) => setIsExternal(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="isExternal" className="text-sm text-gray-700">
                    Prescription externe (patient fera l'analyse ailleurs)
                  </label>
                </div>
                <div className="flex space-x-3">
                  <Button variant="outline" asChild>
                    <Link href="/dashboard/laboratory">Annuler</Link>
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    disabled={
                      saving || !selectedPatient || selectedTests.length === 0
                    }
                  >
                    {saving
                      ? "Création..."
                      : isExternal
                      ? `Prescrire ${selectedTests.length} examen(s) (Externe)`
                      : `Prescrire ${selectedTests.length} examen(s)`}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
