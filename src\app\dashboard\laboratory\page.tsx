"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Flask,
  Plus,
  Settings,
  TrendingUp,
  Users,
  Calendar,
  TestTube,
  Clock,
  CheckCircle,
  AlertCircle,
  CreditCard,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabTestTypes, getLabOrders } from "@/lib/actions/laboratory";

// Types
interface LabTestType {
  id: string;
  name: string;
  code: string;
  category: string;
  price: number;
  description?: string;
}

interface LabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  status: string;
  urgency: string;
  clinicalInfo?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  testType: {
    name: string;
    category: string;
    price: number;
    sampleType?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
  result?: {
    id: string;
    validatedAt?: string;
  };
  payment?: {
    id: string;
    amount: number;
    paymentDate: string;
  };
}

// Mapping des statuts
const statusLabels = {
  PENDING_PAYMENT: "En attente de paiement",
  PAID: "Payé",
  SAMPLE_TAKEN: "Échantillon prélevé",
  IN_PROGRESS: "En cours d'analyse",
  COMPLETED: "Terminé",
  CANCELLED: "Annulé",
};

const statusColors = {
  PENDING_PAYMENT: "bg-orange-100 text-orange-800",
  PAID: "bg-blue-100 text-blue-800",
  SAMPLE_TAKEN: "bg-purple-100 text-purple-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "Urgence",
};

export default function LaboratoryPage() {
  const [labTestTypes, setLabTestTypes] = useState<LabTestType[]>([]);
  const [labOrders, setLabOrders] = useState<LabOrder[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les types d'examens
      const testTypesResult = await getLabTestTypes();
      if (testTypesResult.success) {
        setLabTestTypes(testTypesResult.labTestTypes || []);
      }

      // Charger les prescriptions d'examens
      const ordersResult = await getLabOrders();
      if (ordersResult.success) {
        setLabOrders(ordersResult.labOrders || []);
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  // Calculer les statistiques
  const stats = {
    totalTests: labTestTypes.length,
    pendingPayments: labOrders.filter((o) => o.status === "PENDING_PAYMENT")
      .length,
    inProgress: labOrders.filter((o) => o.status === "IN_PROGRESS").length,
    completedToday: labOrders.filter((o) => {
      const today = new Date().toDateString();
      return (
        o.status === "COMPLETED" &&
        new Date(o.orderDate).toDateString() === today
      );
    }).length,
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Laboratoire</h1>
            <p className="text-gray-600 mt-2">
              Gestion des examens de laboratoire
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/laboratory/tests">
                <Settings className="h-4 w-4 mr-2" />
                Gérer les examens
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/laboratory/orders/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle prescription
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Types d'examens
              </CardTitle>
              <TestTube className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTests}</div>
              <p className="text-xs text-muted-foreground">
                Examens disponibles
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                En attente paiement
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingPayments}</div>
              <p className="text-xs text-muted-foreground">À payer</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                En cours d'analyse
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.inProgress}</div>
              <p className="text-xs text-muted-foreground">Analyses en cours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Terminés aujourd'hui
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedToday}</div>
              <p className="text-xs text-muted-foreground">Résultats prêts</p>
            </CardContent>
          </Card>
        </div>

        {/* Contenu principal */}
        <Tabs defaultValue="orders" className="space-y-6">
          <TabsList>
            <TabsTrigger value="orders">Prescriptions récentes</TabsTrigger>
            <TabsTrigger value="tests">Types d'examens</TabsTrigger>
          </TabsList>

          {/* Prescriptions récentes */}
          <TabsContent value="orders">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Flask className="h-5 w-5 mr-2" />
                  Prescriptions d'examens récentes
                </CardTitle>
                <CardDescription>
                  Les 20 dernières prescriptions d'examens
                </CardDescription>
              </CardHeader>
              <CardContent>
                {labOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <Flask className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucune prescription d'examen
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Commencez par prescrire des examens de laboratoire.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/laboratory/orders/new">
                        Nouvelle prescription
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Prescription</TableHead>
                        <TableHead>Patient</TableHead>
                        <TableHead>Examen</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Urgence</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Montant</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {labOrders.slice(0, 20).map((order) => (
                        <TableRow key={order.id}>
                          <TableCell className="font-mono text-sm">
                            {order.orderNumber}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">
                                {order.patient.firstName}{" "}
                                {order.patient.lastName}
                              </p>
                              <p className="text-sm text-gray-500">
                                {order.patient.patientNumber}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">
                                {order.testType.name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {order.testType.category}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                statusColors[
                                  order.status as keyof typeof statusColors
                                ]
                              }
                            >
                              {
                                statusLabels[
                                  order.status as keyof typeof statusLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                order.urgency === "EMERGENCY"
                                  ? "destructive"
                                  : "secondary"
                              }
                            >
                              {
                                urgencyLabels[
                                  order.urgency as keyof typeof urgencyLabels
                                ]
                              }
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p>{formatDate(order.orderDate)}</p>
                              <p className="text-sm text-gray-500">
                                {formatTime(order.orderDate)}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {order.testType.price.toLocaleString()} FCFA
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Types d'examens */}
          <TabsContent value="tests">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TestTube className="h-5 w-5 mr-2" />
                  Types d'examens disponibles
                </CardTitle>
                <CardDescription>
                  Examens de laboratoire configurés
                </CardDescription>
              </CardHeader>
              <CardContent>
                {labTestTypes.length === 0 ? (
                  <div className="text-center py-8">
                    <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun type d'examen configuré
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Configurez les types d'examens disponibles dans votre
                      laboratoire.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/laboratory/tests">
                        Configurer les examens
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Code</TableHead>
                        <TableHead>Nom de l'examen</TableHead>
                        <TableHead>Catégorie</TableHead>
                        <TableHead>Prix</TableHead>
                        <TableHead>Description</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {labTestTypes.map((test) => (
                        <TableRow key={test.id}>
                          <TableCell className="font-mono text-sm">
                            {test.code}
                          </TableCell>
                          <TableCell className="font-medium">
                            {test.name}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{test.category}</Badge>
                          </TableCell>
                          <TableCell className="font-medium">
                            {test.price.toLocaleString()} FCFA
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {test.description || "-"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
