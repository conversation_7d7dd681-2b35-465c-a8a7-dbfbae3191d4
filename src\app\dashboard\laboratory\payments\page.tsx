"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  TestTube,
  User,
  Search,
  CreditCard,
  CheckCircle,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  getPendingPaymentLabOrders,
  createLabPayment,
} from "@/lib/actions/laboratory";
import { PaymentMethod } from "@prisma/client";

// Types
interface PendingLabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  urgency: string;
  clinicalInfo?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  testType: {
    name: string;
    category: string;
    price: number;
    sampleType?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
}

const paymentMethods: PaymentMethod[] = ["CASH", "MOBILE", "CARD", "INSURANCE"];

const paymentMethodLabels = {
  CASH: "Espèces",
  MOBILE: "Mobile Money",
  CARD: "Carte bancaire",
  INSURANCE: "Assurance",
};

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "Urgence",
};

export default function LabPaymentsPage() {
  const router = useRouter();
  const [pendingLabOrders, setPendingLabOrders] = useState<PendingLabOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<PendingLabOrder[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<PendingLabOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [formData, setFormData] = useState({
    paymentMethod: "CASH" as PaymentMethod,
    reference: "",
    notes: "",
  });

  useEffect(() => {
    loadPendingOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [pendingLabOrders, searchTerm]);

  const loadPendingOrders = async () => {
    try {
      setLoading(true);
      const result = await getPendingPaymentLabOrders();
      if (result.success) {
        setPendingLabOrders(result.labOrders || []);
      } else {
        toast.error(result.error || "Erreur lors du chargement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des examens en attente");
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    if (!searchTerm) {
      setFilteredOrders(pendingLabOrders);
      return;
    }

    const filtered = pendingLabOrders.filter(
      (order) =>
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.patient.patientNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.testType.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredOrders(filtered);
  };

  const selectOrder = (order: PendingLabOrder) => {
    setSelectedOrder(order);
    setSearchTerm("");
  };

  const handlePayment = async () => {
    if (!selectedOrder) {
      toast.error("Veuillez sélectionner un examen");
      return;
    }

    setSaving(true);
    try {
      const result = await createLabPayment({
        labOrderId: selectedOrder.id,
        amount: selectedOrder.testType.price,
        paymentMethod: formData.paymentMethod,
        reference: formData.reference || undefined,
        notes: formData.notes || undefined,
      });

      if (result.success) {
        toast.success("Paiement enregistré avec succès");
        router.push("/dashboard/laboratory/results");
      } else {
        toast.error(result.error || "Erreur lors du paiement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de l'enregistrement du paiement");
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/laboratory">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Paiements Laboratoire
            </h1>
            <p className="text-gray-600 mt-2">
              Payer les examens de laboratoire en attente
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sélection de l'examen */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TestTube className="h-5 w-5 mr-2" />
                Examens en attente de paiement
              </CardTitle>
              <CardDescription>
                Sélectionnez un examen à payer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedOrder ? (
                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">
                        {selectedOrder.patient.firstName} {selectedOrder.patient.lastName}
                      </h3>
                      <p className="text-sm text-gray-600">
                        N° {selectedOrder.patient.patientNumber}
                      </p>
                      <p className="text-sm font-medium text-blue-600">
                        {selectedOrder.testType.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedOrder.testType.category}
                      </p>
                      <p className="text-sm font-bold text-green-600">
                        {selectedOrder.testType.price.toLocaleString()} FCFA
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedOrder(null)}
                    >
                      Changer
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div>
                    <Label htmlFor="search">Rechercher un examen</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="search"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Patient, N° prescription, examen..."
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="max-h-80 overflow-y-auto space-y-2">
                    {filteredOrders.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {pendingLabOrders.length === 0
                            ? "Aucun examen en attente de paiement"
                            : "Aucun résultat trouvé"}
                        </h3>
                        <p className="text-gray-600">
                          {pendingLabOrders.length === 0
                            ? "Tous les examens prescrits ont été payés."
                            : "Essayez de modifier votre recherche."}
                        </p>
                      </div>
                    ) : (
                      filteredOrders.map((order) => (
                        <div
                          key={order.id}
                          className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                          onClick={() => selectOrder(order)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <p className="font-medium">
                                  {order.patient.firstName} {order.patient.lastName}
                                </p>
                                <Badge variant={order.urgency === "EMERGENCY" ? "destructive" : "secondary"}>
                                  {urgencyLabels[order.urgency as keyof typeof urgencyLabels]}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600">
                                N° {order.patient.patientNumber}
                              </p>
                              <p className="text-sm text-blue-600 font-medium">
                                {order.testType.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {order.testType.category} - {formatDate(order.orderDate)} {formatTime(order.orderDate)}
                              </p>
                              <p className="text-sm font-bold text-green-600">
                                {order.testType.price.toLocaleString()} FCFA
                              </p>
                            </div>
                            <Button variant="ghost" size="sm">
                              Sélectionner
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Formulaire de paiement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Informations de paiement
              </CardTitle>
              <CardDescription>
                Détails du paiement de l'examen
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedOrder ? (
                <>
                  {/* Résumé de l'examen */}
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Résumé</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Patient :</span>
                        <span>{selectedOrder.patient.firstName} {selectedOrder.patient.lastName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>N° Prescription :</span>
                        <span className="font-mono">{selectedOrder.orderNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Examen :</span>
                        <span>{selectedOrder.testType.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Médecin :</span>
                        <span>Dr. {selectedOrder.doctor.firstName} {selectedOrder.doctor.lastName}</span>
                      </div>
                      <div className="flex justify-between font-bold text-green-600 border-t pt-1">
                        <span>Total :</span>
                        <span>{selectedOrder.testType.price.toLocaleString()} FCFA</span>
                      </div>
                    </div>
                  </div>

                  {/* Mode de paiement */}
                  <div>
                    <Label htmlFor="paymentMethod">Mode de paiement</Label>
                    <Select
                      value={formData.paymentMethod}
                      onValueChange={(value: PaymentMethod) =>
                        setFormData((prev) => ({ ...prev, paymentMethod: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method} value={method}>
                            {paymentMethodLabels[method]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Référence de transaction */}
                  {(formData.paymentMethod === "MOBILE" || formData.paymentMethod === "CARD") && (
                    <div>
                      <Label htmlFor="reference">Référence de transaction</Label>
                      <Input
                        id="reference"
                        value={formData.reference}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, reference: e.target.value }))
                        }
                        placeholder="Numéro de transaction..."
                      />
                    </div>
                  )}

                  {/* Notes */}
                  <div>
                    <Label htmlFor="notes">Notes (optionnel)</Label>
                    <Input
                      id="notes"
                      value={formData.notes}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, notes: e.target.value }))
                      }
                      placeholder="Notes additionnelles..."
                    />
                  </div>

                  {/* Informations cliniques */}
                  {selectedOrder.clinicalInfo && (
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <Label className="text-sm font-medium text-blue-900">
                        Informations cliniques :
                      </Label>
                      <p className="text-sm text-blue-800 mt-1">
                        {selectedOrder.clinicalInfo}
                      </p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-4 border-t">
                    <Button
                      variant="outline"
                      onClick={() => setSelectedOrder(null)}
                    >
                      Annuler
                    </Button>
                    <Button
                      onClick={handlePayment}
                      disabled={saving}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Traitement...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Confirmer le paiement
                        </>
                      )}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p>Sélectionnez un examen pour procéder au paiement</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
