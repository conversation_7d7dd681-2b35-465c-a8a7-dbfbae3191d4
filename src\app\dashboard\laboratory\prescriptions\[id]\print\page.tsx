"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import { getLabOrders } from "@/lib/actions/laboratory";

// Types
interface LabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  status: string;
  urgency: string;
  clinicalInfo?: string;
  instructions?: string;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    dateOfBirth?: string;
    gender?: string;
  };
  testType: {
    name: string;
    category: string;
    price: number;
    sampleType?: string;
    preparation?: string;
    normalValues?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    specialization?: string;
  };
}

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "URGENCE",
};

export default function PrintPrescriptionPage() {
  const params = useParams();
  const [labOrder, setLabOrder] = useState<LabOrder | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLabOrder();
    // Auto-print après chargement
    const timer = setTimeout(() => {
      if (!loading && labOrder) {
        window.print();
      }
    }, 1000);
    return () => clearTimeout(timer);
  }, [loading, labOrder]);

  const loadLabOrder = async () => {
    try {
      setLoading(true);
      const result = await getLabOrders();
      if (result.success) {
        const order = result.labOrders?.find(o => o.id === params.id);
        if (order) {
          setLabOrder(order as any);
        } else {
          toast.error("Prescription non trouvée");
        }
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement de la prescription");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      weekday: "long",
      day: "2-digit",
      month: "long",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement de la prescription...</p>
        </div>
      </div>
    );
  }

  if (!labOrder) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Prescription non trouvée
          </h3>
          <button onClick={() => window.close()} className="text-blue-600 hover:text-blue-800">
            Fermer
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-white p-8 max-w-4xl mx-auto">
        {/* En-tête médical professionnel */}
        <div className="border-b-4 border-blue-600 pb-6 mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-blue-900 mb-2">
                GlobalCare Solutions
              </h1>
              <p className="text-lg text-blue-700 font-medium">
                Centre Médical & Laboratoire de Biologie
              </p>
              <div className="text-sm text-gray-600 mt-2 space-y-1">
                <p>📍 Bamako, Mali</p>
                <p>📞 +223 XX XX XX XX</p>
                <p>✉️ <EMAIL></p>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-600 font-medium">N° Prescription</p>
                <p className="text-xl font-bold text-blue-900 font-mono">
                  {labOrder.orderNumber}
                </p>
                <p className="text-sm text-gray-600 mt-2">
                  {formatDate(labOrder.orderDate)}
                </p>
                <p className="text-sm text-gray-600">
                  {formatTime(labOrder.orderDate)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Titre de la prescription */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            PRESCRIPTION D'EXAMENS DE LABORATOIRE
          </h2>
          {labOrder.urgency !== "NORMAL" && (
            <div className={`inline-block px-4 py-2 rounded-full text-sm font-bold ${
              labOrder.urgency === "EMERGENCY" 
                ? "bg-red-100 text-red-800 border border-red-300" 
                : "bg-orange-100 text-orange-800 border border-orange-300"
            }`}>
              ⚠️ {urgencyLabels[labOrder.urgency as keyof typeof urgencyLabels]}
            </div>
          )}
        </div>

        {/* Informations patient et médecin */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          {/* Patient */}
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
              👤 INFORMATIONS PATIENT
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-600">Nom complet :</span>
                <p className="text-lg font-bold text-gray-900">
                  {labOrder.patient.firstName} {labOrder.patient.lastName}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">N° Patient :</span>
                <p className="font-mono text-gray-900">{labOrder.patient.patientNumber}</p>
              </div>
              {labOrder.patient.phone && (
                <div>
                  <span className="text-sm font-medium text-gray-600">Téléphone :</span>
                  <p className="text-gray-900">{labOrder.patient.phone}</p>
                </div>
              )}
              <div>
                <span className="text-sm font-medium text-gray-600">Date de prescription :</span>
                <p className="text-gray-900">{formatDate(labOrder.orderDate)}</p>
              </div>
            </div>
          </div>

          {/* Médecin */}
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <h3 className="text-lg font-bold text-blue-900 mb-4 flex items-center">
              👨‍⚕️ MÉDECIN PRESCRIPTEUR
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-blue-600">Docteur :</span>
                <p className="text-lg font-bold text-blue-900">
                  Dr. {labOrder.doctor.firstName} {labOrder.doctor.lastName}
                </p>
              </div>
              {labOrder.doctor.specialization && (
                <div>
                  <span className="text-sm font-medium text-blue-600">Spécialité :</span>
                  <p className="text-blue-900">{labOrder.doctor.specialization}</p>
                </div>
              )}
              <div>
                <span className="text-sm font-medium text-blue-600">Établissement :</span>
                <p className="text-blue-900">GlobalCare Solutions</p>
              </div>
            </div>
          </div>
        </div>

        {/* Examens prescrits */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            🧪 EXAMENS PRESCRITS
          </h3>
          
          <div className="border-2 border-gray-300 rounded-lg overflow-hidden">
            <div className="bg-gray-100 px-6 py-4 border-b border-gray-300">
              <div className="grid grid-cols-4 gap-4 font-bold text-gray-900">
                <div>EXAMEN</div>
                <div>CATÉGORIE</div>
                <div>ÉCHANTILLON</div>
                <div>PRIX</div>
              </div>
            </div>
            
            <div className="px-6 py-6">
              <div className="grid grid-cols-4 gap-4 items-center">
                <div>
                  <p className="font-bold text-lg text-gray-900">{labOrder.testType.name}</p>
                  {labOrder.testType.normalValues && (
                    <p className="text-sm text-gray-600 mt-1">
                      Valeurs normales : {labOrder.testType.normalValues}
                    </p>
                  )}
                </div>
                <div>
                  <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {labOrder.testType.category}
                  </span>
                </div>
                <div>
                  {labOrder.testType.sampleType && (
                    <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      {labOrder.testType.sampleType}
                    </span>
                  )}
                </div>
                <div>
                  <p className="text-lg font-bold text-green-600">
                    {labOrder.testType.price.toLocaleString()} FCFA
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Préparation et instructions */}
        {(labOrder.testType.preparation || labOrder.clinicalInfo || labOrder.instructions) && (
          <div className="mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              📋 INSTRUCTIONS
            </h3>
            
            <div className="space-y-4">
              {labOrder.testType.preparation && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-800 mb-2">⚠️ Préparation requise :</h4>
                  <p className="text-yellow-700">{labOrder.testType.preparation}</p>
                </div>
              )}
              
              {labOrder.clinicalInfo && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-bold text-blue-800 mb-2">🩺 Informations cliniques :</h4>
                  <p className="text-blue-700">{labOrder.clinicalInfo}</p>
                </div>
              )}
              
              {labOrder.instructions && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-bold text-green-800 mb-2">📝 Instructions spéciales :</h4>
                  <p className="text-green-700">{labOrder.instructions}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Instructions générales */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            📌 INSTRUCTIONS GÉNÉRALES
          </h3>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Présentez-vous à jeun si requis pour l'examen
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Apportez cette prescription et votre pièce d'identité
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Respectez les instructions de préparation mentionnées
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                Les résultats seront disponibles selon les délais du laboratoire
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                En cas d'urgence, contactez immédiatement le médecin prescripteur
              </li>
            </ul>
          </div>
        </div>

        {/* Signature et cachet */}
        <div className="flex justify-between items-end mt-12">
          <div className="text-center">
            <div className="border-t-2 border-gray-400 pt-2 w-48">
              <p className="text-sm font-medium text-gray-600">Signature du patient</p>
              <p className="text-xs text-gray-500 mt-1">
                (ou représentant légal)
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <div className="border-2 border-blue-600 rounded-lg p-4 bg-blue-50">
              <p className="font-bold text-blue-900">Dr. {labOrder.doctor.firstName} {labOrder.doctor.lastName}</p>
              <p className="text-sm text-blue-700">Médecin prescripteur</p>
              <div className="mt-4 h-16 flex items-center justify-center">
                <p className="text-xs text-blue-600 italic">Signature et cachet</p>
              </div>
            </div>
          </div>
        </div>

        {/* Pied de page */}
        <div className="mt-12 pt-6 border-t border-gray-300 text-center text-sm text-gray-500">
          <p>
            Cette prescription est valable 30 jours à compter de la date d'émission
          </p>
          <p className="mt-1">
            GlobalCare Solutions - Système de Gestion Hospitalière
          </p>
          <p className="mt-1">
            Imprimé le {new Date().toLocaleDateString("fr-FR")} à {new Date().toLocaleTimeString("fr-FR", { hour: "2-digit", minute: "2-digit" })}
          </p>
        </div>
      </div>

      {/* Styles d'impression */}
      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
            background: white !important;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          
          @page {
            size: A4;
            margin: 1cm;
          }
          
          .no-print {
            display: none !important;
          }
          
          /* Assurer que les couleurs de fond s'impriment */
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          
          /* Éviter les coupures de page dans les sections importantes */
          .bg-gray-50, .bg-blue-50, .bg-yellow-50, .bg-green-50 {
            page-break-inside: avoid;
          }
          
          /* Forcer l'impression des bordures et couleurs */
          .border, .border-2, .border-4 {
            border-color: #000 !important;
          }
          
          .bg-blue-600 {
            background-color: #2563eb !important;
          }
          
          .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }
          
          .text-blue-900 {
            color: #1e3a8a !important;
          }
          
          .text-blue-600 {
            color: #2563eb !important;
          }
        }
        
        @media screen {
          body {
            background-color: #f5f5f5;
          }
        }
      `}</style>
    </>
  );
}
