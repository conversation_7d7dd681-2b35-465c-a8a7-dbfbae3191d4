"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  TestTube,
  User,
  Save,
  CheckCircle,
  AlertTriangle,
  Info,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  getLabOrders,
  getLabResult,
  createOrUpdateLabResult,
  validateLabResult,
} from "@/lib/actions/laboratory";

// Types
interface LabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  status: string;
  urgency: string;
  clinicalInfo?: string;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  testType: {
    name: string;
    category: string;
    normalValues?: string;
    sampleType?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
}

interface LabResult {
  id: string;
  values: Record<string, any>;
  interpretation?: string;
  conclusion?: string;
  technician?: string;
  comments?: string;
  validatedBy?: string;
  validatedAt?: string;
}

// Modèles de résultats pour différents types d'examens
const resultTemplates: Record<string, Array<{ name: string; unit?: string; type: string; normalRange?: string }>> = {
  "Hématologie": [
    { name: "Hémoglobine", unit: "g/dl", type: "number", normalRange: "12-16 (F), 14-18 (H)" },
    { name: "Hématocrite", unit: "%", type: "number", normalRange: "36-46 (F), 41-53 (H)" },
    { name: "Globules rouges", unit: "M/μl", type: "number", normalRange: "4.2-5.4 (F), 4.7-6.1 (H)" },
    { name: "Globules blancs", unit: "/μl", type: "number", normalRange: "4000-10000" },
    { name: "Plaquettes", unit: "/μl", type: "number", normalRange: "150000-400000" },
  ],
  "Biochimie": [
    { name: "Glycémie", unit: "mg/dl", type: "number", normalRange: "70-110" },
    { name: "Créatinine", unit: "mg/dl", type: "number", normalRange: "0.6-1.2 (F), 0.7-1.3 (H)" },
    { name: "Urée", unit: "mg/dl", type: "number", normalRange: "15-45" },
    { name: "ALAT", unit: "UI/l", type: "number", normalRange: "< 40" },
    { name: "ASAT", unit: "UI/l", type: "number", normalRange: "< 40" },
  ],
  "Parasitologie": [
    { name: "Paludisme", type: "select", normalRange: "Négatif" },
    { name: "Parasites intestinaux", type: "text", normalRange: "Absence" },
    { name: "Œufs et parasites", type: "text", normalRange: "Absence" },
  ],
  "Sérologie": [
    { name: "VIH", type: "select", normalRange: "Négatif" },
    { name: "Hépatite B", type: "select", normalRange: "Négatif" },
    { name: "Hépatite C", type: "select", normalRange: "Négatif" },
  ],
};

export default function EnterResultsPage() {
  const params = useParams();
  const router = useRouter();
  const [labOrder, setLabOrder] = useState<LabOrder | null>(null);
  const [labResult, setLabResult] = useState<LabResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);

  const [formData, setFormData] = useState({
    values: {} as Record<string, any>,
    interpretation: "",
    conclusion: "",
    technician: "",
    comments: "",
  });

  useEffect(() => {
    loadData();
  }, [params.id]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Charger l'examen
      const ordersResult = await getLabOrders();
      if (ordersResult.success) {
        const order = ordersResult.labOrders?.find(o => o.id === params.id);
        if (order) {
          setLabOrder(order as any);
          
          // Charger le résultat existant s'il y en a un
          const resultResult = await getLabResult(params.id as string);
          if (resultResult.success && resultResult.labResult) {
            const result = resultResult.labResult;
            setLabResult(result as any);
            setFormData({
              values: result.values || {},
              interpretation: result.interpretation || "",
              conclusion: result.conclusion || "",
              technician: result.technician || "",
              comments: result.comments || "",
            });
          }
        } else {
          toast.error("Examen non trouvé");
          router.push("/dashboard/laboratory/results");
        }
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const handleValueChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      values: {
        ...prev.values,
        [fieldName]: value,
      },
    }));
  };

  const handleSave = async () => {
    if (!labOrder) return;

    setSaving(true);
    try {
      const result = await createOrUpdateLabResult({
        labOrderId: labOrder.id,
        values: formData.values,
        interpretation: formData.interpretation,
        conclusion: formData.conclusion,
        technician: formData.technician,
        comments: formData.comments,
      });

      if (result.success) {
        toast.success("Résultats sauvegardés avec succès");
        loadData(); // Recharger pour mettre à jour l'état
      } else {
        toast.error(result.error || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la sauvegarde des résultats");
    } finally {
      setSaving(false);
    }
  };

  const handleValidate = async () => {
    if (!labOrder || !formData.technician) {
      toast.error("Veuillez renseigner le nom du technicien");
      return;
    }

    setValidating(true);
    try {
      // Sauvegarder d'abord
      await handleSave();
      
      // Puis valider
      const result = await validateLabResult(labOrder.id, formData.technician);
      if (result.success) {
        toast.success("Résultats validés et finalisés");
        router.push("/dashboard/laboratory/results");
      } else {
        toast.error(result.error || "Erreur lors de la validation");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la validation des résultats");
    } finally {
      setValidating(false);
    }
  };

  const getResultTemplate = () => {
    if (!labOrder) return [];
    
    const category = labOrder.testType.category;
    return resultTemplates[category] || [
      { name: "Résultat", type: "text", normalRange: "Voir valeurs de référence" }
    ];
  };

  const renderField = (field: any) => {
    const value = formData.values[field.name] || "";

    switch (field.type) {
      case "number":
        return (
          <Input
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleValueChange(field.name, parseFloat(e.target.value) || "")}
            placeholder={`Ex: ${field.normalRange?.split(" ")[0] || ""}`}
          />
        );
      case "select":
        return (
          <select
            className="w-full p-2 border rounded-md"
            value={value}
            onChange={(e) => handleValueChange(field.name, e.target.value)}
          >
            <option value="">Sélectionner...</option>
            <option value="Positif">Positif</option>
            <option value="Négatif">Négatif</option>
            <option value="Douteux">Douteux</option>
          </select>
        );
      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleValueChange(field.name, e.target.value)}
            placeholder="Saisir le résultat..."
          />
        );
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!labOrder) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Examen non trouvé
            </h3>
            <Button asChild>
              <Link href="/dashboard/laboratory/results">
                Retour aux résultats
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const isValidated = labResult?.validatedAt;

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory/results">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Saisie des résultats
              </h1>
              <p className="text-gray-600 mt-2">
                {labOrder.testType.name} - {labOrder.patient.firstName} {labOrder.patient.lastName}
              </p>
            </div>
          </div>
          {isValidated && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="h-4 w-4 mr-1" />
              Validé
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations de l'examen */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TestTube className="h-5 w-5 mr-2" />
                Informations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-xs text-gray-600">N° Prescription</Label>
                <p className="font-mono text-sm">{labOrder.orderNumber}</p>
              </div>
              
              <div>
                <Label className="text-xs text-gray-600">Patient</Label>
                <p className="font-medium">
                  {labOrder.patient.firstName} {labOrder.patient.lastName}
                </p>
                <p className="text-sm text-gray-500">N° {labOrder.patient.patientNumber}</p>
              </div>

              <div>
                <Label className="text-xs text-gray-600">Examen</Label>
                <p className="font-medium">{labOrder.testType.name}</p>
                <p className="text-sm text-gray-500">{labOrder.testType.category}</p>
              </div>

              <div>
                <Label className="text-xs text-gray-600">Médecin</Label>
                <p className="text-sm">Dr. {labOrder.doctor.firstName} {labOrder.doctor.lastName}</p>
              </div>

              {labOrder.clinicalInfo && (
                <div>
                  <Label className="text-xs text-gray-600">Informations cliniques</Label>
                  <p className="text-sm bg-blue-50 p-2 rounded border border-blue-200">
                    {labOrder.clinicalInfo}
                  </p>
                </div>
              )}

              {labOrder.testType.normalValues && (
                <div>
                  <Label className="text-xs text-gray-600">Valeurs normales</Label>
                  <p className="text-sm bg-green-50 p-2 rounded border border-green-200">
                    {labOrder.testType.normalValues}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Saisie des résultats */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Résultats de l'analyse</CardTitle>
              <CardDescription>
                Saisissez les valeurs obtenues lors de l'analyse
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Valeurs mesurées */}
              <div>
                <h4 className="font-medium mb-4">Valeurs mesurées</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getResultTemplate().map((field) => (
                    <div key={field.name}>
                      <Label htmlFor={field.name} className="text-sm font-medium">
                        {field.name}
                        {field.unit && <span className="text-gray-500"> ({field.unit})</span>}
                      </Label>
                      {renderField(field)}
                      {field.normalRange && (
                        <p className="text-xs text-gray-500 mt-1">
                          Normal: {field.normalRange}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Interprétation */}
              <div>
                <Label htmlFor="interpretation" className="text-sm font-medium">
                  Interprétation
                </Label>
                <Textarea
                  id="interpretation"
                  value={formData.interpretation}
                  onChange={(e) => setFormData(prev => ({ ...prev, interpretation: e.target.value }))}
                  placeholder="Interprétation des résultats..."
                  rows={3}
                  disabled={isValidated}
                />
              </div>

              {/* Conclusion */}
              <div>
                <Label htmlFor="conclusion" className="text-sm font-medium">
                  Conclusion
                </Label>
                <Textarea
                  id="conclusion"
                  value={formData.conclusion}
                  onChange={(e) => setFormData(prev => ({ ...prev, conclusion: e.target.value }))}
                  placeholder="Conclusion de l'analyse..."
                  rows={2}
                  disabled={isValidated}
                />
              </div>

              {/* Technicien */}
              <div>
                <Label htmlFor="technician" className="text-sm font-medium">
                  Technicien responsable *
                </Label>
                <Input
                  id="technician"
                  value={formData.technician}
                  onChange={(e) => setFormData(prev => ({ ...prev, technician: e.target.value }))}
                  placeholder="Nom du technicien..."
                  disabled={isValidated}
                />
              </div>

              {/* Commentaires */}
              <div>
                <Label htmlFor="comments" className="text-sm font-medium">
                  Commentaires
                </Label>
                <Textarea
                  id="comments"
                  value={formData.comments}
                  onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
                  placeholder="Commentaires additionnels..."
                  rows={2}
                  disabled={isValidated}
                />
              </div>

              {/* Actions */}
              {!isValidated && (
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={handleSave}
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                        Sauvegarde...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Sauvegarder
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={handleValidate}
                    disabled={validating || !formData.technician}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {validating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Validation...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Valider et finaliser
                      </>
                    )}
                  </Button>
                </div>
              )}

              {isValidated && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <div>
                      <p className="font-medium text-green-900">Résultats validés</p>
                      <p className="text-sm text-green-700">
                        Validé par {labResult?.validatedBy} le{" "}
                        {labResult?.validatedAt && new Date(labResult.validatedAt).toLocaleDateString("fr-FR")}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
