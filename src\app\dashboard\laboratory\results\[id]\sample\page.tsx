"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  TestTube,
  User,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabOrders, updateLabOrderStatus } from "@/lib/actions/laboratory";

// Types
interface LabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  status: string;
  urgency: string;
  clinicalInfo?: string;
  instructions?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  testType: {
    name: string;
    category: string;
    sampleType?: string;
    preparation?: string;
    normalValues?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
}

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "Urgence",
};

const urgencyColors = {
  NORMAL: "bg-green-100 text-green-800",
  URGENT: "bg-orange-100 text-orange-800",
  EMERGENCY: "bg-red-100 text-red-800",
};

export default function SampleCollectionPage() {
  const params = useParams();
  const router = useRouter();
  const [labOrder, setLabOrder] = useState<LabOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [notes, setNotes] = useState("");

  useEffect(() => {
    loadLabOrder();
  }, [params.id]);

  const loadLabOrder = async () => {
    try {
      setLoading(true);
      const result = await getLabOrders({ patientId: undefined });
      if (result.success) {
        const order = result.labOrders?.find(o => o.id === params.id);
        if (order) {
          setLabOrder(order as any);
        } else {
          toast.error("Examen non trouvé");
          router.push("/dashboard/laboratory/results");
        }
      } else {
        toast.error(result.error || "Erreur lors du chargement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement de l'examen");
    } finally {
      setLoading(false);
    }
  };

  const handleSampleCollection = async () => {
    if (!labOrder) return;

    setSaving(true);
    try {
      const result = await updateLabOrderStatus(labOrder.id, "SAMPLE_TAKEN");
      if (result.success) {
        toast.success("Échantillon prélevé avec succès");
        router.push("/dashboard/laboratory/results");
      } else {
        toast.error(result.error || "Erreur lors de la mise à jour");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la confirmation du prélèvement");
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!labOrder) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Examen non trouvé
            </h3>
            <p className="text-gray-600 mb-4">
              Cet examen n'existe pas ou vous n'avez pas les permissions pour y accéder.
            </p>
            <Button asChild>
              <Link href="/dashboard/laboratory/results">
                Retour aux résultats
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory/results">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Prélèvement d'échantillon
              </h1>
              <p className="text-gray-600 mt-2">
                Confirmer le prélèvement pour l'examen {labOrder.testType.name}
              </p>
            </div>
          </div>
          <Badge className={urgencyColors[labOrder.urgency as keyof typeof urgencyColors]}>
            {urgencyLabels[labOrder.urgency as keyof typeof urgencyLabels]}
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informations de l'examen */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TestTube className="h-5 w-5 mr-2" />
                Détails de l'examen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  N° Prescription
                </Label>
                <p className="font-mono text-sm">{labOrder.orderNumber}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Examen demandé
                </Label>
                <p className="font-medium">{labOrder.testType.name}</p>
                <p className="text-sm text-gray-500">{labOrder.testType.category}</p>
              </div>

              {labOrder.testType.sampleType && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Type d'échantillon
                  </Label>
                  <p className="text-blue-600 font-medium">{labOrder.testType.sampleType}</p>
                </div>
              )}

              {labOrder.testType.preparation && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Préparation requise
                  </Label>
                  <p className="text-orange-600">{labOrder.testType.preparation}</p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Date de prescription
                </Label>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>{formatDate(labOrder.orderDate)}</span>
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span>{formatTime(labOrder.orderDate)}</span>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Médecin prescripteur
                </Label>
                <p>Dr. {labOrder.doctor.firstName} {labOrder.doctor.lastName}</p>
              </div>

              {labOrder.clinicalInfo && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Informations cliniques
                  </Label>
                  <p className="text-sm bg-blue-50 p-3 rounded-lg border border-blue-200">
                    {labOrder.clinicalInfo}
                  </p>
                </div>
              )}

              {labOrder.instructions && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Instructions spéciales
                  </Label>
                  <p className="text-sm bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                    {labOrder.instructions}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Informations du patient */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Informations du patient
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Nom complet
                </Label>
                <p className="font-medium text-lg">
                  {labOrder.patient.firstName} {labOrder.patient.lastName}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-600">
                  N° Patient
                </Label>
                <p className="font-mono">{labOrder.patient.patientNumber}</p>
              </div>

              {labOrder.patient.phone && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Téléphone
                  </Label>
                  <p>{labOrder.patient.phone}</p>
                </div>
              )}

              {/* Instructions de prélèvement */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">
                  Instructions de prélèvement
                </h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Vérifier l'identité du patient</li>
                  <li>• Respecter les conditions de prélèvement</li>
                  <li>• Étiqueter correctement l'échantillon</li>
                  <li>• Noter l'heure de prélèvement</li>
                  {labOrder.testType.sampleType === "Sang" && (
                    <li>• Utiliser des tubes appropriés</li>
                  )}
                  {labOrder.testType.preparation && (
                    <li>• Vérifier que la préparation a été respectée</li>
                  )}
                </ul>
              </div>

              {/* Notes du prélèvement */}
              <div>
                <Label htmlFor="notes" className="text-sm font-medium text-gray-600">
                  Notes du prélèvement (optionnel)
                </Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Observations particulières lors du prélèvement..."
                  rows={3}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                <p>En confirmant, l'échantillon sera marqué comme prélevé</p>
                <p>et l'examen passera en statut "Échantillon prélevé"</p>
              </div>
              <div className="flex space-x-3">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/laboratory/results">
                    Annuler
                  </Link>
                </Button>
                <Button
                  onClick={handleSampleCollection}
                  disabled={saving}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Confirmation...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Confirmer le prélèvement
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
