"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  TestTube,
  User,
  Calendar,
  Printer,
  Download,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabResult } from "@/lib/actions/laboratory";

// Types
interface LabResult {
  id: string;
  values: Record<string, any>;
  interpretation?: string;
  conclusion?: string;
  technician?: string;
  comments?: string;
  validatedBy?: string;
  validatedAt?: string;
  labOrder: {
    id: string;
    orderNumber: string;
    orderDate: string;
    status: string;
    urgency: string;
    clinicalInfo?: string;
    patient: {
      firstName: string;
      lastName: string;
      patientNumber: string;
      phone?: string;
    };
    testType: {
      name: string;
      category: string;
      normalValues?: string;
      sampleType?: string;
    };
    doctor: {
      firstName: string;
      lastName: string;
    };
  };
}

export default function ViewResultPage() {
  const params = useParams();
  const [labResult, setLabResult] = useState<LabResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLabResult();
  }, [params.id]);

  const loadLabResult = async () => {
    try {
      setLoading(true);
      const result = await getLabResult(params.id as string);
      if (result.success && result.labResult) {
        setLabResult(result.labResult as any);
      } else {
        toast.error("Résultat non trouvé");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement du résultat");
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!labResult) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Résultat non trouvé
            </h3>
            <Button asChild>
              <Link href="/dashboard/laboratory/results">
                Retour aux résultats
              </Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const { labOrder } = labResult;

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header - masqué à l'impression */}
        <div className="flex items-center justify-between print:hidden">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory/results">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Résultat d'analyse
              </h1>
              <p className="text-gray-600 mt-2">
                {labOrder.testType.name} - {labOrder.patient.firstName} {labOrder.patient.lastName}
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Imprimer
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/dashboard/laboratory/results/${params.id}/enter`}>
                Modifier
              </Link>
            </Button>
          </div>
        </div>

        {/* Contenu imprimable */}
        <div className="max-w-4xl mx-auto bg-white">
          {/* En-tête du rapport */}
          <div className="text-center mb-8 print:mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              RÉSULTAT D'ANALYSE DE LABORATOIRE
            </h1>
            <div className="text-sm text-gray-600">
              GlobalCare Solutions - Laboratoire de Biologie Médicale
            </div>
          </div>

          {/* Informations patient et prescription */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-lg">
                  <User className="h-5 w-5 mr-2" />
                  Informations Patient
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">Nom :</span> {labOrder.patient.firstName} {labOrder.patient.lastName}
                </div>
                <div>
                  <span className="font-medium">N° Patient :</span> {labOrder.patient.patientNumber}
                </div>
                {labOrder.patient.phone && (
                  <div>
                    <span className="font-medium">Téléphone :</span> {labOrder.patient.phone}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-lg">
                  <TestTube className="h-5 w-5 mr-2" />
                  Informations Prescription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">N° Prescription :</span> {labOrder.orderNumber}
                </div>
                <div>
                  <span className="font-medium">Date prescription :</span> {formatDate(labOrder.orderDate)}
                </div>
                <div>
                  <span className="font-medium">Médecin :</span> Dr. {labOrder.doctor.firstName} {labOrder.doctor.lastName}
                </div>
                <div>
                  <span className="font-medium">Examen :</span> {labOrder.testType.name}
                </div>
                <div>
                  <span className="font-medium">Catégorie :</span> {labOrder.testType.category}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Informations cliniques */}
          {labOrder.clinicalInfo && (
            <Card className="mb-6">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Informations Cliniques</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{labOrder.clinicalInfo}</p>
              </CardContent>
            </Card>
          )}

          {/* Résultats */}
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Résultats de l'Analyse</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(labResult.values).length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(labResult.values).map(([key, value]) => (
                      <div key={key} className="border rounded-lg p-3">
                        <div className="font-medium text-gray-900">{key}</div>
                        <div className="text-lg font-bold text-blue-600">{value}</div>
                      </div>
                    ))}
                  </div>

                  {labOrder.testType.normalValues && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="font-medium text-green-900 mb-1">Valeurs de référence :</div>
                      <div className="text-sm text-green-800">{labOrder.testType.normalValues}</div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 italic">Aucune valeur saisie</p>
              )}
            </CardContent>
          </Card>

          {/* Interprétation et conclusion */}
          {(labResult.interpretation || labResult.conclusion) && (
            <Card className="mb-6">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Interprétation et Conclusion</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {labResult.interpretation && (
                  <div>
                    <div className="font-medium text-gray-900 mb-2">Interprétation :</div>
                    <p className="text-gray-700">{labResult.interpretation}</p>
                  </div>
                )}
                {labResult.conclusion && (
                  <div>
                    <div className="font-medium text-gray-900 mb-2">Conclusion :</div>
                    <p className="text-gray-700 font-medium">{labResult.conclusion}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Commentaires */}
          {labResult.comments && (
            <Card className="mb-6">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Commentaires</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{labResult.comments}</p>
              </CardContent>
            </Card>
          )}

          {/* Validation et signatures */}
          <Card className="mb-8">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Validation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="font-medium text-gray-900 mb-2">Technicien responsable :</div>
                  <p className="text-gray-700">{labResult.technician || "Non renseigné"}</p>
                </div>
                <div>
                  <div className="font-medium text-gray-900 mb-2">Validation :</div>
                  {labResult.validatedAt ? (
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      <div>
                        <p className="font-medium">Validé par {labResult.validatedBy}</p>
                        <p className="text-sm">Le {formatDateTime(labResult.validatedAt)}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center text-orange-600">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      <span>En attente de validation</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pied de page */}
          <div className="text-center text-sm text-gray-500 border-t pt-4">
            <p>Ce document a été généré automatiquement par GlobalCare Solutions</p>
            <p>Date d'impression : {formatDateTime(new Date().toISOString())}</p>
          </div>
        </div>
      </div>

      {/* Styles d'impression */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print\\:hidden {
            display: none !important;
          }
          .max-w-4xl,
          .max-w-4xl * {
            visibility: visible;
          }
          .max-w-4xl {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          @page {
            margin: 1cm;
          }
        }
      `}</style>
    </DashboardLayout>
  );
}
