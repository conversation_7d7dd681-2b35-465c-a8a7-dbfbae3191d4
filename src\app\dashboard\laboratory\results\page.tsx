"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  TestTube,
  Search,
  Edit,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  Printer,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabOrders } from "@/lib/actions/laboratory";

// Types
interface LabOrder {
  id: string;
  orderNumber: string;
  orderDate: string;
  status: string;
  urgency: string;
  clinicalInfo?: string;
  sampleDate?: string;
  resultDate?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
  };
  testType: {
    name: string;
    category: string;
    price: number;
    sampleType?: string;
    normalValues?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
  result?: {
    id: string;
    validatedAt?: string;
  };
}

// Mapping des statuts
const statusLabels = {
  PENDING_PAYMENT: "En attente de paiement",
  PAID: "Payé",
  SAMPLE_TAKEN: "Échantillon prélevé",
  IN_PROGRESS: "En cours d'analyse",
  COMPLETED: "Terminé",
  CANCELLED: "Annulé",
  EXTERNAL: "Prescription externe",
};

const statusColors = {
  PENDING_PAYMENT: "bg-orange-100 text-orange-800",
  PAID: "bg-blue-100 text-blue-800",
  SAMPLE_TAKEN: "bg-purple-100 text-purple-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
  EXTERNAL: "bg-gray-100 text-gray-800",
};

const urgencyLabels = {
  NORMAL: "Normal",
  URGENT: "Urgent",
  EMERGENCY: "Urgence",
};

export default function LabResultsPage() {
  const [labOrders, setLabOrders] = useState<LabOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<LabOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [urgencyFilter, setUrgencyFilter] = useState("ALL");

  useEffect(() => {
    loadLabOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [labOrders, searchTerm, statusFilter, urgencyFilter]);

  const loadLabOrders = async () => {
    try {
      setLoading(true);
      const result = await getLabOrders();
      if (result.success) {
        setLabOrders(result.labOrders || []);
      } else {
        toast.error(result.error || "Erreur lors du chargement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des examens");
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = labOrders;

    // Filtrer par recherche
    if (searchTerm) {
      filtered = filtered.filter(
        (order) =>
          order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.patient.firstName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          order.patient.lastName
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          order.patient.patientNumber
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          order.testType.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par statut
    if (statusFilter !== "ALL") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filtrer par urgence
    if (urgencyFilter !== "ALL") {
      filtered = filtered.filter((order) => order.urgency === urgencyFilter);
    }

    setFilteredOrders(filtered);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getActionButton = (order: LabOrder) => {
    switch (order.status) {
      case "PAID":
        return (
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/laboratory/results/${order.id}/sample`}>
              <TestTube className="h-4 w-4 mr-1" />
              Prélever
            </Link>
          </Button>
        );
      case "SAMPLE_TAKEN":
        return (
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/laboratory/results/${order.id}/enter`}>
              <Edit className="h-4 w-4 mr-1" />
              Saisir
            </Link>
          </Button>
        );
      case "IN_PROGRESS":
        return (
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/laboratory/results/${order.id}/enter`}>
              <Edit className="h-4 w-4 mr-1" />
              Continuer
            </Link>
          </Button>
        );
      case "COMPLETED":
        return (
          <div className="flex space-x-1">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/laboratory/results/${order.id}/view`}>
                <Eye className="h-4 w-4 mr-1" />
                Voir
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/laboratory/results/${order.id}/print`}>
                <Printer className="h-4 w-4 mr-1" />
                Imprimer
              </Link>
            </Button>
          </div>
        );
      case "EXTERNAL":
        return (
          <Badge variant="secondary" className="text-xs">
            Prescription externe
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary" className="text-xs">
            En attente
          </Badge>
        );
    }
  };

  // Calculer les statistiques
  const stats = {
    toSample: filteredOrders.filter((o) => o.status === "PAID").length,
    inProgress: filteredOrders.filter((o) => o.status === "IN_PROGRESS").length,
    toValidate: filteredOrders.filter(
      (o) => o.status === "COMPLETED" && !o.result?.validatedAt
    ).length,
    completed: filteredOrders.filter(
      (o) => o.status === "COMPLETED" && o.result?.validatedAt
    ).length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Résultats de laboratoire
              </h1>
              <p className="text-gray-600 mt-2">
                Gestion des prélèvements, analyses et résultats
              </p>
            </div>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">À prélever</CardTitle>
              <TestTube className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.toSample}</div>
              <p className="text-xs text-muted-foreground">
                Échantillons à prélever
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                En cours d'analyse
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.inProgress}</div>
              <p className="text-xs text-muted-foreground">Analyses en cours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">À valider</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.toValidate}</div>
              <p className="text-xs text-muted-foreground">
                Résultats à valider
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminés</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completed}</div>
              <p className="text-xs text-muted-foreground">Résultats validés</p>
            </CardContent>
          </Card>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle>Filtres</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher par patient, N° prescription, examen..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tous les statuts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Tous les statuts</SelectItem>
                    <SelectItem value="PAID">Payé</SelectItem>
                    <SelectItem value="SAMPLE_TAKEN">
                      Échantillon prélevé
                    </SelectItem>
                    <SelectItem value="IN_PROGRESS">
                      En cours d'analyse
                    </SelectItem>
                    <SelectItem value="COMPLETED">Terminé</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-32">
                <Select value={urgencyFilter} onValueChange={setUrgencyFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Urgence" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Toutes</SelectItem>
                    <SelectItem value="NORMAL">Normal</SelectItem>
                    <SelectItem value="URGENT">Urgent</SelectItem>
                    <SelectItem value="EMERGENCY">Urgence</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Liste des examens */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Examens de laboratoire ({filteredOrders.length})
            </CardTitle>
            <CardDescription>
              Suivi des prélèvements, analyses et résultats
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredOrders.length === 0 ? (
              <div className="text-center py-8">
                <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {labOrders.length === 0
                    ? "Aucun examen trouvé"
                    : "Aucun résultat trouvé"}
                </h3>
                <p className="text-gray-600 mb-4">
                  {labOrders.length === 0
                    ? "Les examens prescrits apparaîtront ici."
                    : "Essayez de modifier vos critères de recherche."}
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>N° Prescription</TableHead>
                    <TableHead>Patient</TableHead>
                    <TableHead>Examen</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Urgence</TableHead>
                    <TableHead>Date prescription</TableHead>
                    <TableHead>Médecin</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-mono text-sm">
                        {order.orderNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {order.patient.firstName} {order.patient.lastName}
                          </p>
                          <p className="text-sm text-gray-500">
                            N° {order.patient.patientNumber}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{order.testType.name}</p>
                          <p className="text-sm text-gray-500">
                            {order.testType.category}
                          </p>
                          {order.testType.sampleType && (
                            <p className="text-xs text-blue-600">
                              Échantillon: {order.testType.sampleType}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            statusColors[
                              order.status as keyof typeof statusColors
                            ]
                          }
                        >
                          {
                            statusLabels[
                              order.status as keyof typeof statusLabels
                            ]
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            order.urgency === "EMERGENCY"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {
                            urgencyLabels[
                              order.urgency as keyof typeof urgencyLabels
                            ]
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p>{formatDate(order.orderDate)}</p>
                          <p className="text-sm text-gray-500">
                            {formatTime(order.orderDate)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">
                          Dr. {order.doctor.firstName} {order.doctor.lastName}
                        </p>
                      </TableCell>
                      <TableCell>{getActionButton(order)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
