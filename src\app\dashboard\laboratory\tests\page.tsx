"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  TestTube,
  Plus,
  Edit,
  Trash2,
  Search,
} from "lucide-react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { getLabTestTypes, createLabTestType } from "@/lib/actions/laboratory";

// Types
interface LabTestType {
  id: string;
  name: string;
  code: string;
  category: string;
  price: number;
  description?: string;
  normalValues?: string;
  sampleType?: string;
  preparation?: string;
}

interface FormData {
  name: string;
  code: string;
  category: string;
  price: string;
  description: string;
  normalValues: string;
  sampleType: string;
  preparation: string;
}

// Catégories d'examens courantes en Afrique
const categories = [
  "Hématologie",
  "Biochimie",
  "Parasitologie",
  "Bactériologie",
  "Sérologie",
  "Immunologie",
  "Hormonologie",
  "Toxicologie",
  "Cytologie",
  "Anatomie pathologique",
];

// Types d'échantillons
const sampleTypes = [
  "Sang",
  "Urine",
  "Selles",
  "Crachat",
  "LCR",
  "Liquide pleural",
  "Liquide d'ascite",
  "Frottis",
  "Biopsie",
  "Autre",
];

export default function LabTestTypesPage() {
  const [labTestTypes, setLabTestTypes] = useState<LabTestType[]>([]);
  const [filteredTests, setFilteredTests] = useState<LabTestType[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("ALL");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    name: "",
    code: "",
    category: "",
    price: "",
    description: "",
    normalValues: "",
    sampleType: "",
    preparation: "",
  });

  useEffect(() => {
    loadLabTestTypes();
  }, []);

  useEffect(() => {
    filterTests();
  }, [labTestTypes, searchTerm, selectedCategory]);

  const loadLabTestTypes = async () => {
    try {
      setLoading(true);
      const result = await getLabTestTypes();
      if (result.success) {
        setLabTestTypes(result.labTestTypes || []);
      } else {
        toast.error(result.error || "Erreur lors du chargement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement des types d'examens");
    } finally {
      setLoading(false);
    }
  };

  const filterTests = () => {
    let filtered = labTestTypes;

    // Filtrer par recherche
    if (searchTerm) {
      filtered = filtered.filter(
        (test) =>
          test.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          test.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
          test.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (selectedCategory !== "ALL") {
      filtered = filtered.filter((test) => test.category === selectedCategory);
    }

    setFilteredTests(filtered);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.code || !formData.category || !formData.price) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    setSaving(true);
    try {
      const result = await createLabTestType({
        name: formData.name,
        code: formData.code.toUpperCase(),
        category: formData.category,
        price: parseFloat(formData.price),
        description: formData.description || undefined,
        normalValues: formData.normalValues || undefined,
        sampleType: formData.sampleType || undefined,
        preparation: formData.preparation || undefined,
      });

      if (result.success) {
        toast.success("Type d'examen créé avec succès");
        setIsDialogOpen(false);
        setFormData({
          name: "",
          code: "",
          category: "",
          price: "",
          description: "",
          normalValues: "",
          sampleType: "",
          preparation: "",
        });
        loadLabTestTypes();
      } else {
        toast.error(result.error || "Erreur lors de la création");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la création du type d'examen");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/laboratory">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Types d'examens de laboratoire
              </h1>
              <p className="text-gray-600 mt-2">
                Gérer les examens disponibles dans votre laboratoire
              </p>
            </div>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nouvel examen
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Créer un nouveau type d'examen</DialogTitle>
                <DialogDescription>
                  Ajoutez un nouveau type d'examen à votre laboratoire
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Nom de l'examen *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, name: e.target.value }))
                      }
                      placeholder="Ex: Hémogramme complet"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="code">Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, code: e.target.value.toUpperCase() }))
                      }
                      placeholder="Ex: HEM001"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Catégorie *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="price">Prix (FCFA) *</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, price: e.target.value }))
                      }
                      placeholder="Ex: 2500"
                      min="0"
                      step="100"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sampleType">Type d'échantillon</Label>
                    <Select
                      value={formData.sampleType}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, sampleType: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Type d'échantillon" />
                      </SelectTrigger>
                      <SelectContent>
                        {sampleTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="preparation">Préparation</Label>
                    <Input
                      id="preparation"
                      value={formData.preparation}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, preparation: e.target.value }))
                      }
                      placeholder="Ex: À jeun"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, description: e.target.value }))
                    }
                    placeholder="Description de l'examen..."
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="normalValues">Valeurs normales</Label>
                  <Textarea
                    id="normalValues"
                    value={formData.normalValues}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, normalValues: e.target.value }))
                    }
                    placeholder="Ex: Hémoglobine: 12-16 g/dl (F), 14-18 g/dl (H)"
                    rows={2}
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit" disabled={saving}>
                    {saving ? "Création..." : "Créer l'examen"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle>Filtres</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher par nom, code ou catégorie..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-48">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">Toutes les catégories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Liste des types d'examens */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TestTube className="h-5 w-5 mr-2" />
              Types d'examens ({filteredTests.length})
            </CardTitle>
            <CardDescription>
              Examens de laboratoire disponibles
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredTests.length === 0 ? (
              <div className="text-center py-8">
                <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {labTestTypes.length === 0 
                    ? "Aucun type d'examen configuré"
                    : "Aucun résultat trouvé"}
                </h3>
                <p className="text-gray-600 mb-4">
                  {labTestTypes.length === 0
                    ? "Commencez par créer des types d'examens pour votre laboratoire."
                    : "Essayez de modifier vos critères de recherche."}
                </p>
                {labTestTypes.length === 0 && (
                  <Button onClick={() => setIsDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Créer le premier examen
                  </Button>
                )}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Nom</TableHead>
                    <TableHead>Catégorie</TableHead>
                    <TableHead>Échantillon</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Préparation</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTests.map((test) => (
                    <TableRow key={test.id}>
                      <TableCell className="font-mono text-sm">
                        {test.code}
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{test.name}</p>
                          {test.description && (
                            <p className="text-sm text-gray-500 truncate max-w-xs">
                              {test.description}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{test.category}</Badge>
                      </TableCell>
                      <TableCell>
                        {test.sampleType && (
                          <Badge variant="secondary">{test.sampleType}</Badge>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        {test.price.toLocaleString()} FCFA
                      </TableCell>
                      <TableCell>
                        {test.preparation && (
                          <Badge variant="outline" className="text-xs">
                            {test.preparation}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
