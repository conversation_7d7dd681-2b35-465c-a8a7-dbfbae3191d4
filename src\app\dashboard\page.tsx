"use client";

import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Users,
  Calendar,
  Stethoscope,
  TrendingUp,
  Heart,
  Activity,
  Clock,
  DollarSign,
  Plus,
  ArrowUpRight,
  Building2,
  UserCheck,
} from "lucide-react";
import Link from "next/link";

export default function Dashboard() {
  const { data: session } = useSession();

  if (!session) {
    return <div>Chargement...</div>;
  }

  const stats = [
    {
      title: "Patients Total",
      value: "1,234",
      change: "+20.1% par rapport au mois dernier",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Consultations Aujourd'hui",
      value: "23",
      change: "+5 par rapport à hier",
      icon: Stethoscope,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Rendez-vous à venir",
      value: "12",
      change: "Pour les prochaines 24h",
      icon: Calendar,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Revenus du mois",
      value: "2,450,000 XOF",
      change: "+15% par rapport au mois dernier",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const quickActions = [
    {
      title: "Nouveau Patient",
      description: "Enregistrer un nouveau patient",
      href: "/dashboard/patients/new",
      icon: Users,
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      title: "Rendez-vous",
      description: "Programmer un nouveau rendez-vous",
      href: "/dashboard/appointments/new",
      icon: Calendar,
      color: "bg-green-600 hover:bg-green-700",
    },
    {
      title: "Consultation",
      description: "Démarrer une nouvelle consultation",
      href: "/dashboard/consultations/new",
      icon: Stethoscope,
      color: "bg-orange-600 hover:bg-orange-700",
    },
  ];

  const recentActivity = [
    {
      action: "Nouveau patient enregistré",
      patient: "Aminata Traoré",
      time: "Il y a 5 minutes",
      type: "patient",
      color: "bg-green-500",
    },
    {
      action: "Consultation terminée",
      patient: "Moussa Diallo",
      time: "Il y a 15 minutes",
      type: "consultation",
      color: "bg-blue-500",
    },
    {
      action: "Rendez-vous programmé",
      patient: "Fatoumata Keita",
      time: "Il y a 30 minutes",
      type: "appointment",
      color: "bg-orange-500",
    },
    {
      action: "Ordonnance créée",
      patient: "Ibrahim Sanogo",
      time: "Il y a 1 heure",
      type: "prescription",
      color: "bg-purple-500",
    },
  ];

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Tableau de bord
            </h1>
            <p className="text-gray-600 mt-2">
              Bienvenue, {session.user.name || session.user.email}
            </p>
            {session.user.organization && (
              <div className="flex items-center mt-2 space-x-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {session.user.organization.name}
                </span>
                <Badge variant="outline" className="text-xs">
                  {session.user.organization.subscriptionPlan}
                </Badge>
              </div>
            )}
          </div>
          <div className="flex space-x-3">
            <Button asChild>
              <Link href="/dashboard/patients/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Patient
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-500 mt-1 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Actions rapides
              </CardTitle>
              <CardDescription>
                Accès rapide aux fonctionnalités principales
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  asChild
                  className={`w-full justify-start h-auto p-4 ${action.color}`}
                >
                  <Link href={action.href}>
                    <div className="flex items-center space-x-3">
                      <action.icon className="h-5 w-5" />
                      <div className="text-left">
                        <div className="font-medium">{action.title}</div>
                        <div className="text-xs opacity-90">
                          {action.description}
                        </div>
                      </div>
                      <ArrowUpRight className="h-4 w-4 ml-auto" />
                    </div>
                  </Link>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Activité récente
              </CardTitle>
              <CardDescription>
                Dernières actions dans votre organisation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div
                      className={`w-3 h-3 rounded-full ${activity.color}`}
                    ></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.action}
                      </p>
                      <p className="text-sm text-gray-600">
                        Patient: {activity.patient}
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {activity.type}
                    </Badge>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/activity">
                    Voir toute l'activité
                    <ArrowUpRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Aperçu des performances
            </CardTitle>
            <CardDescription>
              Statistiques de votre organisation ce mois-ci
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">95%</div>
                <div className="text-sm text-gray-600">
                  Taux de satisfaction
                </div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">18 min</div>
                <div className="text-sm text-gray-600">
                  Temps d'attente moyen
                </div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">342</div>
                <div className="text-sm text-gray-600">
                  Consultations ce mois
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
