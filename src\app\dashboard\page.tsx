"use client";

import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Users,
  Calendar,
  Stethoscope,
  TrendingUp,
  Heart,
  Activity,
  Clock,
  DollarSign,
  Plus,
  ArrowUpRight,
  Building2,
  UserCheck,
} from "lucide-react";
import Link from "next/link";

export default function Dashboard() {
  const { data: session } = useSession();

  if (!session) {
    return <div>Chargement...</div>;
  }

  const stats = [
    {
      title: "Patients Total",
      value: "1,234",
      change: "+20.1% par rapport au mois dernier",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Consultations Aujourd'hui",
      value: "23",
      change: "+5 par rapport à hier",
      icon: Stethoscope,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Rendez-vous à venir",
      value: "12",
      change: "Pour les prochaines 24h",
      icon: Calendar,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Revenus du mois",
      value: "2,450,000 XOF",
      change: "+15% par rapport au mois dernier",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const quickActions = [
    {
      title: "Nouveau Patient",
      description: "Enregistrer un nouveau patient",
      href: "/dashboard/patients/new",
      icon: Users,
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      title: "Rendez-vous",
      description: "Programmer un nouveau rendez-vous",
      href: "/dashboard/appointments/new",
      icon: Calendar,
      color: "bg-green-600 hover:bg-green-700",
    },
    {
      title: "Consultation",
      description: "Démarrer une nouvelle consultation",
      href: "/dashboard/consultations/new",
      icon: Stethoscope,
      color: "bg-orange-600 hover:bg-orange-700",
    },
  ];

  const recentActivity = [
    {
      action: "Nouveau patient enregistré",
      patient: "Aminata Traoré",
      time: "Il y a 5 minutes",
      type: "patient",
      color: "bg-green-500",
    },
    {
      action: "Consultation terminée",
      patient: "Moussa Diallo",
      time: "Il y a 15 minutes",
      type: "consultation",
      color: "bg-blue-500",
    },
    {
      action: "Rendez-vous programmé",
      patient: "Fatoumata Keita",
      time: "Il y a 30 minutes",
      type: "appointment",
      color: "bg-orange-500",
    },
    {
      action: "Ordonnance créée",
      patient: "Ibrahim Sanogo",
      time: "Il y a 1 heure",
      type: "prescription",
      color: "bg-purple-500",
    },
  ];

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Tableau de bord
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Bienvenue, {session.user.name || session.user.email} 👋
            </p>
            {session.user.organization && (
              <div className="flex items-center mt-3 space-x-3">
                <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full border border-white/20 shadow-lg">
                  <Building2 className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700">
                    {session.user.organization.name}
                  </span>
                </div>
                <Badge className="bg-gradient-to-r from-emerald-400 to-teal-400 text-white border-0 shadow-lg">
                  {session.user.organization.subscriptionPlan}
                </Badge>
                <div className="status-indicator status-online"></div>
              </div>
            )}
          </div>
          <div className="flex space-x-3">
            <Button
              asChild
              className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <Link href="/dashboard/patients/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Patient
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-3 rounded-xl ${stat.bgColor} shadow-lg`}>
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-500 flex items-center bg-gray-50 px-2 py-1 rounded-full">
                  <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center text-gray-800">
                <div className="p-2 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-lg mr-3 shadow-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                Actions rapides
              </CardTitle>
              <CardDescription className="text-gray-600">
                Accès rapide aux fonctionnalités principales
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  asChild
                  className={`w-full justify-start h-auto p-4 ${action.color} shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border-0`}
                >
                  <Link href={action.href}>
                    <div className="flex items-center space-x-3">
                      <action.icon className="h-5 w-5" />
                      <div className="text-left">
                        <div className="font-medium">{action.title}</div>
                        <div className="text-xs opacity-90">
                          {action.description}
                        </div>
                      </div>
                      <ArrowUpRight className="h-4 w-4 ml-auto" />
                    </div>
                  </Link>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center text-gray-800">
                <div className="p-2 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-lg mr-3 shadow-lg">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                Activité récente
              </CardTitle>
              <CardDescription className="text-gray-600">
                Dernières actions dans votre organisation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-4 p-4 rounded-xl hover:bg-white/60 transition-all duration-200 hover:shadow-md border border-white/20"
                  >
                    <div
                      className={`w-4 h-4 rounded-full ${activity.color} shadow-lg animate-pulse`}
                    ></div>
                    <div className="flex-1">
                      <p className="text-sm font-semibold text-gray-900">
                        {activity.action}
                      </p>
                      <p className="text-sm text-gray-600 font-medium">
                        Patient: {activity.patient}
                      </p>
                      <p className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full inline-block mt-1">
                        {activity.time}
                      </p>
                    </div>
                    <Badge className="text-xs bg-gradient-to-r from-blue-400 to-indigo-400 text-white border-0 shadow-lg">
                      {activity.type}
                    </Badge>
                  </div>
                ))}
              </div>
              <div className="mt-6 pt-4 border-t border-white/20">
                <Button
                  variant="outline"
                  className="w-full bg-white/60 hover:bg-white/80 border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  asChild
                >
                  <Link href="/dashboard/activity">
                    Voir toute l'activité
                    <ArrowUpRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        <Card className="glass-card border-0 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center text-gray-800">
              <div className="p-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg mr-3 shadow-lg">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              Aperçu des performances
            </CardTitle>
            <CardDescription className="text-gray-600">
              Statistiques de votre organisation ce mois-ci
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="text-3xl font-bold text-blue-600 mb-2">95%</div>
                <div className="text-sm text-gray-600 font-medium">
                  Taux de satisfaction
                </div>
                <div className="mt-2">
                  <Heart className="h-4 w-4 text-blue-500 mx-auto" />
                </div>
              </div>
              <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  18 min
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Temps d'attente moyen
                </div>
                <div className="mt-2">
                  <Clock className="h-4 w-4 text-green-500 mx-auto" />
                </div>
              </div>
              <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  342
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Consultations ce mois
                </div>
                <div className="mt-2">
                  <UserCheck className="h-4 w-4 text-orange-500 mx-auto" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
