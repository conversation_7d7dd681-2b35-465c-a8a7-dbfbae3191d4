"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowLeft,
  Pill,
  AlertTriangle,
  CheckCircle,
  Save,
  Loader2,
  User,
  Calendar,
  Package,
  CreditCard,
} from "lucide-react";
import Link from "next/link";
import { getPrescriptionById } from "@/lib/actions/prescriptions";
import { createDispensing } from "@/lib/actions/pharmacy";
import { toast } from "sonner";

// Types
interface PrescriptionData {
  id: string;
  prescriptionNumber: string;
  prescriptionDate: string;
  patient: {
    firstName: string;
    lastName: string;
    patientNumber: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    dispensedQuantity: number;
    dosage: string;
    frequency: string;
    duration: string;
    instructions?: string;
    isExternal: boolean;
    medication?: {
      id: string;
      name: string;
      form: string;
      strength?: string;
      stockQuantity: number;
      price?: number;
    };
  }>;
}

interface DispensingItem {
  prescriptionItemId: string;
  medicationId: string;
  dispensedQuantity: number;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: string;
}

export default function DispensingDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [prescription, setPrescription] = useState<PrescriptionData | null>(
    null
  );

  // État du formulaire de dispensation
  const [dispensingData, setDispensingData] = useState({
    items: [] as DispensingItem[],
    paymentMethod: "CASH" as
      | "CASH"
      | "CARD"
      | "MOBILE"
      | "INSURANCE"
      | "CREDIT",
    paymentReference: "",
    discountAmount: 0,
    notes: "",
  });

  // Charger la prescription
  useEffect(() => {
    const loadPrescription = async () => {
      if (!params.id) return;

      try {
        const result = await getPrescriptionById(params.id as string);
        if (result.success && result.prescription) {
          const prescData = result.prescription as any;
          setPrescription(prescData);

          // Initialiser les items de dispensation
          const items = prescData.items
            .filter(
              (item: any) =>
                !item.isExternal && !item.isDispensed && item.medication
            )
            .map((item: any) => ({
              prescriptionItemId: item.id,
              medicationId: item.medication.id,
              dispensedQuantity: Math.min(
                item.quantity - (item.dispensedQuantity || 0),
                item.medication.stockQuantity || 0
              ),
              unitPrice: item.medication.price || 0,
              batchNumber: "",
              expiryDate: "",
            }));

          setDispensingData((prev) => ({ ...prev, items }));
        } else {
          toast.error("Prescription non trouvée");
          router.push("/dashboard/pharmacy/dispensing");
        }
      } catch (error) {
        console.error("Erreur:", error);
        toast.error("Erreur lors du chargement");
        router.push("/dashboard/pharmacy/dispensing");
      } finally {
        setLoading(false);
      }
    };

    loadPrescription();
  }, [params.id, router]);

  // Mettre à jour un item de dispensation
  const updateDispensingItem = (
    index: number,
    field: keyof DispensingItem,
    value: any
  ) => {
    setDispensingData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  // Calculer le total
  const calculateTotal = () => {
    const subtotal = dispensingData.items.reduce(
      (sum, item) => sum + item.unitPrice * item.dispensedQuantity,
      0
    );
    return subtotal - dispensingData.discountAmount;
  };

  // Valider la dispensation
  const validateDispensing = () => {
    if (dispensingData.items.length === 0) {
      toast.error("Aucun médicament à dispenser");
      return false;
    }

    for (const item of dispensingData.items) {
      if (item.dispensedQuantity <= 0) {
        toast.error("Toutes les quantités doivent être supérieures à 0");
        return false;
      }

      const prescItem = prescription?.items.find(
        (pi) => pi.id === item.prescriptionItemId
      );
      if (
        prescItem &&
        item.dispensedQuantity >
          prescItem.quantity - prescItem.dispensedQuantity
      ) {
        toast.error("Quantité dispensée supérieure à la quantité prescrite");
        return false;
      }

      if (
        prescItem?.medication &&
        item.dispensedQuantity > prescItem.medication.stockQuantity
      ) {
        toast.error(`Stock insuffisant pour ${prescItem.medication.name}`);
        return false;
      }
    }

    return true;
  };

  // Soumettre la dispensation
  const handleSubmit = async () => {
    if (!prescription || !validateDispensing()) return;

    setSaving(true);
    try {
      const result = await createDispensing({
        prescriptionId: prescription.id,
        items: dispensingData.items,
        paymentMethod: dispensingData.paymentMethod,
        paymentReference: dispensingData.paymentReference || undefined,
        discountAmount: dispensingData.discountAmount,
        notes: dispensingData.notes || undefined,
      });

      if (result.success) {
        toast.success("Dispensation créée avec succès");
        router.push("/dashboard/pharmacy/dispensing");
      } else {
        toast.error(result.error || "Erreur lors de la dispensation");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors de la dispensation");
    } finally {
      setSaving(false);
    }
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement de la prescription...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!prescription) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Prescription non trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Cette prescription n'existe pas ou n'est pas disponible pour
                dispensation.
              </p>
              <Button asChild>
                <Link href="/dashboard/pharmacy/dispensing">
                  Retour aux dispensations
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  const dispensableItems = prescription.items.filter(
    (item) => !item.isExternal && !item.isDispensed && item.medication
  );

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/pharmacy/dispensing">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                Dispensation - {prescription.prescriptionNumber}
              </h1>
              <p className="text-gray-600 mt-2">
                Dispenser les médicaments pour {prescription.patient.firstName}{" "}
                {prescription.patient.lastName}
              </p>
            </div>
          </div>
          <Button
            onClick={handleSubmit}
            disabled={saving || dispensingData.items.length === 0}
            className="w-full sm:w-auto"
          >
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Finaliser la Dispensation
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations de la prescription */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Informations Patient
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Patient
                  </Label>
                  <p className="text-lg font-semibold">
                    {prescription.patient.firstName}{" "}
                    {prescription.patient.lastName}
                  </p>
                  <p className="text-sm text-gray-500">
                    {prescription.patient.patientNumber}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Médecin prescripteur
                  </Label>
                  <p className="font-medium">
                    Dr. {prescription.doctor.firstName}{" "}
                    {prescription.doctor.lastName}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Date de prescription
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <p>
                      {new Date(
                        prescription.prescriptionDate
                      ).toLocaleDateString("fr-FR")}
                    </p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Numéro de prescription
                  </Label>
                  <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                    {prescription.prescriptionNumber}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Paiement */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Paiement
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="paymentMethod">Mode de paiement</Label>
                  <Select
                    value={dispensingData.paymentMethod}
                    onValueChange={(value: any) =>
                      setDispensingData((prev) => ({
                        ...prev,
                        paymentMethod: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">Espèces</SelectItem>
                      <SelectItem value="CARD">Carte bancaire</SelectItem>
                      <SelectItem value="MOBILE">Paiement mobile</SelectItem>
                      <SelectItem value="INSURANCE">Assurance</SelectItem>
                      <SelectItem value="CREDIT">Crédit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="paymentReference">
                    Référence de paiement
                  </Label>
                  <Input
                    id="paymentReference"
                    value={dispensingData.paymentReference}
                    onChange={(e) =>
                      setDispensingData((prev) => ({
                        ...prev,
                        paymentReference: e.target.value,
                      }))
                    }
                    placeholder="Numéro de transaction..."
                  />
                </div>

                <div>
                  <Label htmlFor="discountAmount">Remise (FCFA)</Label>
                  <Input
                    id="discountAmount"
                    type="number"
                    value={dispensingData.discountAmount}
                    onChange={(e) =>
                      setDispensingData((prev) => ({
                        ...prev,
                        discountAmount: parseFloat(e.target.value) || 0,
                      }))
                    }
                    min="0"
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={dispensingData.notes}
                    onChange={(e) =>
                      setDispensingData((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    placeholder="Notes sur la dispensation..."
                    rows={3}
                  />
                </div>

                {/* Résumé financier */}
                <div className="border-t pt-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Sous-total:</span>
                      <span>
                        {(
                          calculateTotal() + dispensingData.discountAmount
                        ).toLocaleString()}{" "}
                        FCFA
                      </span>
                    </div>
                    {dispensingData.discountAmount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Remise:</span>
                        <span>
                          -{dispensingData.discountAmount.toLocaleString()} FCFA
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between font-semibold text-lg border-t pt-2">
                      <span>Total:</span>
                      <span>{calculateTotal().toLocaleString()} FCFA</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Médicaments à dispenser */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Médicaments à Dispenser ({dispensableItems.length})
                </CardTitle>
                <CardDescription>
                  Ajustez les quantités et prix avant de finaliser la
                  dispensation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {dispensableItems.length === 0 ? (
                  <div className="text-center py-8">
                    <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun médicament à dispenser
                    </h3>
                    <p className="text-gray-600">
                      Tous les médicaments de cette prescription ont été
                      dispensés ou sont externes.
                    </p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[200px]">
                            Médicament
                          </TableHead>
                          <TableHead className="text-center">
                            Prescrit
                          </TableHead>
                          <TableHead className="text-center">Stock</TableHead>
                          <TableHead className="text-center">
                            Quantité
                          </TableHead>
                          <TableHead className="text-center">
                            Prix unitaire
                          </TableHead>
                          <TableHead className="text-center">Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {dispensableItems.map((item, index) => {
                          const dispensingItem = dispensingData.items[index];
                          const remainingQuantity =
                            item.quantity - (item.dispensedQuantity || 0);
                          const hasStock =
                            (item.medication?.stockQuantity || 0) >=
                            (dispensingItem?.dispensedQuantity || 0);

                          return (
                            <TableRow key={item.id}>
                              <TableCell>
                                <div>
                                  <p className="font-medium">
                                    {item.medication?.name ||
                                      "Médicament inconnu"}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {item.medication?.strength} •{" "}
                                    {item.medication?.form}
                                  </p>
                                  <div className="mt-1">
                                    <p className="text-xs text-gray-600">
                                      {item.dosage} • {item.frequency} •{" "}
                                      {item.duration}
                                    </p>
                                    {item.instructions && (
                                      <p className="text-xs text-blue-600 mt-1">
                                        {item.instructions}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-center">
                                  <p className="font-semibold">
                                    {remainingQuantity}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    restant
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  {hasStock ? (
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                  ) : (
                                    <AlertTriangle className="h-4 w-4 text-red-500" />
                                  )}
                                  <span
                                    className={
                                      hasStock
                                        ? "text-green-600"
                                        : "text-red-600"
                                    }
                                  >
                                    {item.medication?.stockQuantity || 0}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Input
                                  type="number"
                                  value={dispensingItem?.dispensedQuantity || 0}
                                  onChange={(e) =>
                                    updateDispensingItem(
                                      index,
                                      "dispensedQuantity",
                                      parseInt(e.target.value) || 0
                                    )
                                  }
                                  min="0"
                                  max={Math.min(
                                    remainingQuantity,
                                    item.medication?.stockQuantity || 0
                                  )}
                                  className="w-20"
                                />
                              </TableCell>
                              <TableCell>
                                <Input
                                  type="number"
                                  value={dispensingItem?.unitPrice || 0}
                                  onChange={(e) =>
                                    updateDispensingItem(
                                      index,
                                      "unitPrice",
                                      parseFloat(e.target.value) || 0
                                    )
                                  }
                                  min="0"
                                  step="0.01"
                                  className="w-24"
                                />
                              </TableCell>
                              <TableCell>
                                <p className="font-medium">
                                  {(
                                    (dispensingItem?.unitPrice || 0) *
                                    (dispensingItem?.dispensedQuantity || 0)
                                  ).toLocaleString()}{" "}
                                  FCFA
                                </p>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
