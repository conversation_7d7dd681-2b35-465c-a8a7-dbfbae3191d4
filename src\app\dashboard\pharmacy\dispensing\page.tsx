'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Search,
  Pill,
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  Loader2,
  Package,
  User,
  Calendar
} from "lucide-react"
import Link from 'next/link'
import { getPendingPrescriptions } from '@/lib/actions/pharmacy'
import { toast } from 'sonner'

// Types
interface PendingPrescription {
  id: string
  prescriptionNumber: string
  prescriptionDate: string
  patient: {
    firstName: string
    lastName: string
    patientNumber: string
  }
  doctor: {
    firstName: string
    lastName: string
  }
  items: Array<{
    id: string
    quantity: number
    dispensedQuantity: number
    dosage: string
    frequency: string
    duration: string
    medication: {
      id: string
      name: string
      form: string
      strength?: string
      stockQuantity: number
      price?: number
    }
  }>
}

export default function PharmacyDispensingPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(true)
  const [prescriptions, setPrescriptions] = useState<PendingPrescription[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredPrescriptions, setFilteredPrescriptions] = useState<PendingPrescription[]>([])

  // Charger les prescriptions en attente
  useEffect(() => {
    const loadPrescriptions = async () => {
      try {
        const result = await getPendingPrescriptions()
        if (result.success && result.prescriptions) {
          setPrescriptions(result.prescriptions as any)
          setFilteredPrescriptions(result.prescriptions as any)
        } else {
          toast.error('Erreur lors du chargement des prescriptions')
        }
      } catch (error) {
        console.error('Erreur:', error)
        toast.error('Erreur lors du chargement')
      } finally {
        setLoading(false)
      }
    }

    loadPrescriptions()
  }, [])

  // Filtrer les prescriptions par recherche
  useEffect(() => {
    if (!searchTerm) {
      setFilteredPrescriptions(prescriptions)
    } else {
      const filtered = prescriptions.filter(prescription =>
        prescription.prescriptionNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prescription.patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prescription.patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prescription.patient.patientNumber.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredPrescriptions(filtered)
    }
  }, [prescriptions, searchTerm])

  // Calculer les statistiques
  const stats = {
    total: prescriptions.length,
    urgent: prescriptions.filter(p =>
      new Date(p.prescriptionDate) < new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length,
    today: prescriptions.filter(p =>
      new Date(p.prescriptionDate).toDateString() === new Date().toDateString()
    ).length,
    totalItems: prescriptions.reduce((sum, p) => sum + p.items.length, 0)
  }

  // Vérifier si une prescription est urgente
  const isUrgent = (prescriptionDate: string) => {
    return new Date(prescriptionDate) < new Date(Date.now() - 24 * 60 * 60 * 1000)
  }

  // Vérifier la disponibilité du stock
  const checkStockAvailability = (items: any[]) => {
    const unavailable = items.filter(item =>
      item.medication && item.medication.stockQuantity < (item.quantity - (item.dispensedQuantity || 0))
    )
    return {
      available: unavailable.length === 0,
      unavailableCount: unavailable.length
    }
  }

  // Calculer le total estimé d'une prescription
  const calculateTotal = (items: any[]) => {
    return items.reduce((sum, item) => {
      const remainingQuantity = item.quantity - (item.dispensedQuantity || 0)
      const price = item.medication?.price || 0
      return sum + (price * remainingQuantity)
    }, 0)
  }

  if (!session) {
    return <div>Chargement...</div>
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/pharmacy">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Dispensation de Médicaments
              </h1>
              <p className="text-gray-600 mt-2">
                Dispenser les médicaments prescrits aux patients
              </p>
            </div>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Prescriptions
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.total}
                  </p>
                </div>
                <Pill className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Urgentes (>24h)
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.urgent}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Aujourd'hui
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.today}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Items à Dispenser
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {stats.totalItems}
                  </p>
                </div>
                <Package className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recherche */}
        <Card>
          <CardHeader>
            <CardTitle>Rechercher une Prescription</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Rechercher par numéro de prescription, nom du patient..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Liste des prescriptions */}
        <Card>
          <CardHeader>
            <CardTitle>
              Prescriptions en Attente ({filteredPrescriptions.length})
            </CardTitle>
            <CardDescription>
              Prescriptions prêtes pour dispensation
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                Chargement des prescriptions...
              </div>
            ) : filteredPrescriptions.length === 0 ? (
              <div className="text-center py-8">
                <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm ? 'Aucune prescription trouvée' : 'Aucune prescription en attente'}
                </h3>
                <p className="text-gray-600">
                  {searchTerm
                    ? 'Aucune prescription ne correspond à votre recherche.'
                    : 'Toutes les prescriptions ont été dispensées.'}
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Prescription</TableHead>
                    <TableHead>Patient</TableHead>
                    <TableHead>Médecin</TableHead>
                    <TableHead>Médicaments</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Total Estimé</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPrescriptions.map((prescription) => {
                    const stockStatus = checkStockAvailability(prescription.items)
                    const total = calculateTotal(prescription.items)
                    const urgent = isUrgent(prescription.prescriptionDate)

                    return (
                      <TableRow key={prescription.id} className={urgent ? 'bg-red-50' : ''}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{prescription.prescriptionNumber}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(prescription.prescriptionDate).toLocaleDateString('fr-FR')}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <div>
                              <p className="font-medium">
                                {prescription.patient.firstName} {prescription.patient.lastName}
                              </p>
                              <p className="text-sm text-gray-500">
                                {prescription.patient.patientNumber}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">
                            Dr. {prescription.doctor.firstName} {prescription.doctor.lastName}
                          </p>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {prescription.items.slice(0, 2).map((item) => (
                              <div key={item.id} className="text-sm">
                                <span className="font-medium">
                                  {item.medication?.name || 'Médicament externe'}
                                </span>
                                <span className="text-gray-500 ml-2">
                                  ({item.quantity - (item.dispensedQuantity || 0)} restant)
                                </span>
                              </div>
                            ))}
                            {prescription.items.length > 2 && (
                              <p className="text-xs text-gray-500">
                                +{prescription.items.length - 2} autre(s)
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {stockStatus.available ? (
                            <Badge className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Disponible
                            </Badge>
                          ) : (
                            <Badge variant="destructive">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              {stockStatus.unavailableCount} manquant(s)
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <p className="font-medium">
                            {total.toLocaleString()} FCFA
                          </p>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {urgent && (
                              <Badge variant="destructive" className="text-xs">
                                Urgent
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              En attente
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/dashboard/prescriptions/${prescription.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            <Button
                              size="sm"
                              disabled={!stockStatus.available}
                              asChild
                            >
                              <Link href={`/dashboard/pharmacy/dispensing/${prescription.id}`}>
                                <Pill className="h-4 w-4 mr-2" />
                                Dispenser
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
