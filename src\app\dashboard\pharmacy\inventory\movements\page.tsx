'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useSearchParams } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Plus,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Save,
  Loader2,
  Package,
  History
} from "lucide-react"
import Link from 'next/link'
import { getMedications } from '@/lib/actions/prescriptions'
import { createPharmacyMovement, getPharmacyMovements } from '@/lib/actions/pharmacy'
import { toast } from 'sonner'

// Types
interface Medication {
  id: string
  name: string
  stockQuantity: number
  form: string
  strength?: string
}

interface PharmacyMovement {
  id: string
  type: string
  quantity: number
  previousStock: number
  newStock: number
  reason?: string
  reference?: string
  unitPrice?: number
  totalValue?: number
  batchNumber?: string
  expiryDate?: string
  createdAt: string
  medication: {
    name: string
    form: string
    strength?: string
  }
  user: {
    firstName: string
    lastName: string
  }
}

export default function PharmacyMovementsPage() {
  const { data: session } = useSession()
  const searchParams = useSearchParams()
  const preselectedMedicationId = searchParams.get('medicationId')
  
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [medications, setMedications] = useState<Medication[]>([])
  const [movements, setMovements] = useState<PharmacyMovement[]>([])
  const [selectedMedication, setSelectedMedication] = useState<Medication | null>(null)

  // Formulaire
  const [formData, setFormData] = useState({
    medicationId: preselectedMedicationId || '',
    type: 'IN' as 'IN' | 'OUT' | 'ADJUSTMENT',
    quantity: '',
    reason: '',
    reference: '',
    unitPrice: '',
    expiryDate: '',
    batchNumber: ''
  })

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        const [medicationsResult, movementsResult] = await Promise.all([
          getMedications({ limit: 1000 }),
          getPharmacyMovements({ limit: 20 })
        ])

        if (medicationsResult.success && medicationsResult.medications) {
          setMedications(medicationsResult.medications as any)
          
          // Présélectionner le médicament si spécifié
          if (preselectedMedicationId) {
            const preselected = medicationsResult.medications.find(
              (m: any) => m.id === preselectedMedicationId
            )
            if (preselected) {
              setSelectedMedication(preselected as any)
            }
          }
        }

        if (movementsResult.success && movementsResult.movements) {
          setMovements(movementsResult.movements as any)
        }
      } catch (error) {
        console.error('Erreur:', error)
        toast.error('Erreur lors du chargement')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [preselectedMedicationId])

  // Sélectionner un médicament
  const handleMedicationSelect = (medicationId: string) => {
    const medication = medications.find(m => m.id === medicationId)
    setSelectedMedication(medication || null)
    setFormData(prev => ({ ...prev, medicationId }))
  }

  // Soumettre le formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.medicationId || !formData.quantity) {
      toast.error('Veuillez remplir tous les champs requis')
      return
    }

    setSaving(true)
    try {
      const result = await createPharmacyMovement({
        medicationId: formData.medicationId,
        type: formData.type,
        quantity: parseInt(formData.quantity),
        reason: formData.reason || undefined,
        reference: formData.reference || undefined,
        unitPrice: formData.unitPrice ? parseFloat(formData.unitPrice) : undefined,
        expiryDate: formData.expiryDate || undefined,
        batchNumber: formData.batchNumber || undefined
      })

      if (result.success) {
        toast.success('Mouvement de stock créé avec succès')
        
        // Réinitialiser le formulaire
        setFormData({
          medicationId: preselectedMedicationId || '',
          type: 'IN',
          quantity: '',
          reason: '',
          reference: '',
          unitPrice: '',
          expiryDate: '',
          batchNumber: ''
        })

        // Recharger les données
        const [medicationsResult, movementsResult] = await Promise.all([
          getMedications({ limit: 1000 }),
          getPharmacyMovements({ limit: 20 })
        ])

        if (medicationsResult.success) {
          setMedications(medicationsResult.medications as any)
          if (formData.medicationId) {
            const updated = medicationsResult.medications.find(
              (m: any) => m.id === formData.medicationId
            )
            setSelectedMedication(updated as any || null)
          }
        }

        if (movementsResult.success) {
          setMovements(movementsResult.movements as any)
        }
      } else {
        toast.error(result.error || 'Erreur lors de la création')
      }
    } catch (error) {
      console.error('Erreur:', error)
      toast.error('Erreur lors de la création')
    } finally {
      setSaving(false)
    }
  }

  // Obtenir l'icône du type de mouvement
  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'IN':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'OUT':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'ADJUSTMENT':
        return <RotateCcw className="h-4 w-4 text-blue-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  // Obtenir le badge du type de mouvement
  const getMovementBadge = (type: string) => {
    switch (type) {
      case 'IN':
        return <Badge className="bg-green-100 text-green-800">Entrée</Badge>
      case 'OUT':
        return <Badge className="bg-red-100 text-red-800">Sortie</Badge>
      case 'ADJUSTMENT':
        return <Badge className="bg-blue-100 text-blue-800">Ajustement</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  if (!session) {
    return <div>Chargement...</div>
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/pharmacy/inventory">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour à l'inventaire
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Mouvements de Stock
              </h1>
              <p className="text-gray-600 mt-2">
                Gérer les entrées, sorties et ajustements de stock
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Formulaire de mouvement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                Nouveau Mouvement
              </CardTitle>
              <CardDescription>
                Ajouter une entrée, sortie ou ajustement de stock
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Sélection du médicament */}
                <div>
                  <Label htmlFor="medicationId">Médicament *</Label>
                  <Select
                    value={formData.medicationId}
                    onValueChange={handleMedicationSelect}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un médicament..." />
                    </SelectTrigger>
                    <SelectContent>
                      {medications.map((medication) => (
                        <SelectItem key={medication.id} value={medication.id}>
                          {medication.name} {medication.strength} - {medication.form}
                          <span className="text-xs text-gray-500 ml-2">
                            (Stock: {medication.stockQuantity})
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Stock actuel */}
                {selectedMedication && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium text-gray-700">
                      Stock actuel: {selectedMedication.stockQuantity} unités
                    </p>
                  </div>
                )}

                {/* Type de mouvement */}
                <div>
                  <Label htmlFor="type">Type de mouvement *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: 'IN' | 'OUT' | 'ADJUSTMENT') => 
                      setFormData(prev => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="IN">Entrée (Livraison, Retour)</SelectItem>
                      <SelectItem value="OUT">Sortie (Péremption, Perte)</SelectItem>
                      <SelectItem value="ADJUSTMENT">Ajustement d'inventaire</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Quantité */}
                <div>
                  <Label htmlFor="quantity">
                    {formData.type === 'ADJUSTMENT' ? 'Nouveau stock *' : 'Quantité *'}
                  </Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
                    placeholder={formData.type === 'ADJUSTMENT' ? 'Stock final' : 'Quantité'}
                    min="0"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Prix unitaire */}
                  <div>
                    <Label htmlFor="unitPrice">Prix unitaire (FCFA)</Label>
                    <Input
                      id="unitPrice"
                      type="number"
                      value={formData.unitPrice}
                      onChange={(e) => setFormData(prev => ({ ...prev, unitPrice: e.target.value }))}
                      placeholder="Prix unitaire"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  {/* Référence */}
                  <div>
                    <Label htmlFor="reference">Référence</Label>
                    <Input
                      id="reference"
                      value={formData.reference}
                      onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                      placeholder="Bon de livraison, etc."
                    />
                  </div>
                </div>

                {formData.type === 'IN' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Date d'expiration */}
                    <div>
                      <Label htmlFor="expiryDate">Date d'expiration</Label>
                      <Input
                        id="expiryDate"
                        type="date"
                        value={formData.expiryDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                      />
                    </div>

                    {/* Numéro de lot */}
                    <div>
                      <Label htmlFor="batchNumber">Numéro de lot</Label>
                      <Input
                        id="batchNumber"
                        value={formData.batchNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, batchNumber: e.target.value }))}
                        placeholder="Numéro de lot"
                      />
                    </div>
                  </div>
                )}

                {/* Raison */}
                <div>
                  <Label htmlFor="reason">Raison</Label>
                  <Textarea
                    id="reason"
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                    placeholder="Raison du mouvement..."
                    rows={3}
                  />
                </div>

                {/* Bouton de soumission */}
                <Button type="submit" disabled={saving} className="w-full">
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Enregistrer le mouvement
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Historique des mouvements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <History className="h-5 w-5 mr-2" />
                Mouvements Récents
              </CardTitle>
              <CardDescription>
                Derniers mouvements de stock
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : movements.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun mouvement
                  </h3>
                  <p className="text-gray-600">
                    Aucun mouvement de stock enregistré.
                  </p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {movements.map((movement) => (
                    <div
                      key={movement.id}
                      className="p-3 border rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getMovementIcon(movement.type)}
                          <div>
                            <h4 className="font-medium text-sm">
                              {movement.medication.name}
                            </h4>
                            <p className="text-xs text-gray-500">
                              {movement.medication.strength} • {movement.medication.form}
                            </p>
                          </div>
                        </div>
                        {getMovementBadge(movement.type)}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-2">
                        <div>
                          <span className="font-medium">Quantité:</span> {movement.quantity}
                        </div>
                        <div>
                          <span className="font-medium">Stock:</span> {movement.previousStock} → {movement.newStock}
                        </div>
                      </div>

                      {movement.reason && (
                        <p className="text-xs text-gray-500 mb-2">
                          {movement.reason}
                        </p>
                      )}

                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>
                          {movement.user.firstName} {movement.user.lastName}
                        </span>
                        <span>
                          {new Date(movement.createdAt).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
