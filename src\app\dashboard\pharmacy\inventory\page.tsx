'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Package,
  Search,
  Filter,
  Plus,
  ArrowLeft,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Loader2,
  Eye
} from "lucide-react"
import Link from 'next/link'
import { getMedications } from '@/lib/actions/prescriptions'
import { toast } from 'sonner'

// Types
interface Medication {
  id: string
  name: string
  genericName?: string
  strength?: string
  form: string
  category: string
  stockQuantity: number
  minStockLevel: number
  maxStockLevel: number
  price?: number
  isActive: boolean
}

export default function PharmacyInventoryPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(true)
  const [medications, setMedications] = useState<Medication[]>([])
  const [filteredMedications, setFilteredMedications] = useState<Medication[]>([])
  
  // Filtres
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [stockFilter, setStockFilter] = useState('all') // all, low, normal, high

  // Charger les médicaments
  useEffect(() => {
    const loadMedications = async () => {
      try {
        const result = await getMedications({ limit: 1000 })
        if (result.success && result.medications) {
          setMedications(result.medications as any)
          setFilteredMedications(result.medications as any)
        } else {
          toast.error('Erreur lors du chargement des médicaments')
        }
      } catch (error) {
        console.error('Erreur:', error)
        toast.error('Erreur lors du chargement')
      } finally {
        setLoading(false)
      }
    }

    loadMedications()
  }, [])

  // Appliquer les filtres
  useEffect(() => {
    let filtered = medications

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(med =>
        med.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        med.genericName?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filtre par catégorie
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(med => med.category === categoryFilter)
    }

    // Filtre par niveau de stock
    if (stockFilter !== 'all') {
      filtered = filtered.filter(med => {
        switch (stockFilter) {
          case 'low':
            return med.stockQuantity <= med.minStockLevel
          case 'normal':
            return med.stockQuantity > med.minStockLevel && med.stockQuantity < med.maxStockLevel
          case 'high':
            return med.stockQuantity >= med.maxStockLevel
          default:
            return true
        }
      })
    }

    setFilteredMedications(filtered)
  }, [medications, searchTerm, categoryFilter, stockFilter])

  // Obtenir le badge de stock
  const getStockBadge = (medication: Medication) => {
    if (medication.stockQuantity <= medication.minStockLevel) {
      return <Badge variant="destructive">Stock Faible</Badge>
    } else if (medication.stockQuantity >= medication.maxStockLevel) {
      return <Badge variant="secondary">Stock Élevé</Badge>
    } else {
      return <Badge variant="default">Stock Normal</Badge>
    }
  }

  // Obtenir l'icône de tendance
  const getStockIcon = (medication: Medication) => {
    if (medication.stockQuantity <= medication.minStockLevel) {
      return <TrendingDown className="h-4 w-4 text-red-500" />
    } else if (medication.stockQuantity >= medication.maxStockLevel) {
      return <TrendingUp className="h-4 w-4 text-blue-500" />
    } else {
      return <Package className="h-4 w-4 text-green-500" />
    }
  }

  // Statistiques
  const stats = {
    total: medications.length,
    lowStock: medications.filter(m => m.stockQuantity <= m.minStockLevel).length,
    normalStock: medications.filter(m => m.stockQuantity > m.minStockLevel && m.stockQuantity < m.maxStockLevel).length,
    highStock: medications.filter(m => m.stockQuantity >= m.maxStockLevel).length
  }

  if (!session) {
    return <div>Chargement...</div>
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/pharmacy">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Inventaire Pharmacie
              </h1>
              <p className="text-gray-600 mt-2">
                Gestion des stocks de médicaments
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href="/dashboard/pharmacy/inventory/movements">
                <Plus className="h-4 w-4 mr-2" />
                Mouvement de Stock
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/medications/new">
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Médicament
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Médicaments
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.total}
                  </p>
                </div>
                <Package className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Stock Faible
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.lowStock}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Stock Normal
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.normalStock}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Stock Élevé
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.highStock}
                  </p>
                </div>
                <TrendingDown className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle>Filtres</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher un médicament..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Catégorie" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les catégories</SelectItem>
                  <SelectItem value="ANTIBIOTIC">Antibiotique</SelectItem>
                  <SelectItem value="ANALGESIC">Antalgique</SelectItem>
                  <SelectItem value="ANTI_INFLAMMATORY">Anti-inflammatoire</SelectItem>
                  <SelectItem value="ANTIHYPERTENSIVE">Antihypertenseur</SelectItem>
                  <SelectItem value="ANTIDIABETIC">Antidiabétique</SelectItem>
                  <SelectItem value="VITAMIN">Vitamine</SelectItem>
                  <SelectItem value="OTHER">Autre</SelectItem>
                </SelectContent>
              </Select>

              <Select value={stockFilter} onValueChange={setStockFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Niveau de stock" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les niveaux</SelectItem>
                  <SelectItem value="low">Stock faible</SelectItem>
                  <SelectItem value="normal">Stock normal</SelectItem>
                  <SelectItem value="high">Stock élevé</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Table des médicaments */}
        <Card>
          <CardHeader>
            <CardTitle>
              Médicaments ({filteredMedications.length})
            </CardTitle>
            <CardDescription>
              Liste des médicaments avec leur niveau de stock
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                Chargement des médicaments...
              </div>
            ) : filteredMedications.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun médicament trouvé
                </h3>
                <p className="text-gray-600 mb-4">
                  Aucun médicament ne correspond aux critères de recherche.
                </p>
                <Button asChild>
                  <Link href="/dashboard/medications/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter un médicament
                  </Link>
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Médicament</TableHead>
                    <TableHead>Catégorie</TableHead>
                    <TableHead>Stock Actuel</TableHead>
                    <TableHead>Niveaux</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMedications.map((medication) => (
                    <TableRow key={medication.id}>
                      <TableCell>
                        <div>
                          <div className="flex items-center space-x-2">
                            {getStockIcon(medication)}
                            <div>
                              <p className="font-medium">{medication.name}</p>
                              {medication.genericName && (
                                <p className="text-sm text-gray-500">
                                  {medication.genericName}
                                </p>
                              )}
                              <p className="text-xs text-gray-400">
                                {medication.strength} • {medication.form}
                              </p>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {medication.category}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-center">
                          <p className="text-lg font-semibold">
                            {medication.stockQuantity}
                          </p>
                          <p className="text-xs text-gray-500">unités</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-xs text-gray-500">
                          <p>Min: {medication.minStockLevel}</p>
                          <p>Max: {medication.maxStockLevel}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {medication.price ? (
                          <p className="font-medium">
                            {medication.price.toLocaleString()} FCFA
                          </p>
                        ) : (
                          <p className="text-gray-400">Non défini</p>
                        )}
                      </TableCell>
                      <TableCell>
                        {getStockBadge(medication)}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/medications/${medication.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/pharmacy/inventory/movements?medicationId=${medication.id}`}>
                              <Plus className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
