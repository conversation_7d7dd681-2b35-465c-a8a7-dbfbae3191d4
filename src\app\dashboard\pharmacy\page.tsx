'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Pill,
  Package,
  AlertTriangle,
  TrendingUp,
  Users,
  ShoppingCart,
  Plus,
  Eye,
  ArrowUpDown,
  Loader2
} from "lucide-react"
import Link from 'next/link'
import { getLowStockMedications, getPendingPrescriptions } from '@/lib/actions/pharmacy'
import { toast } from 'sonner'

// Types
interface LowStockMedication {
  id: string
  name: string
  stockQuantity: number
  minStockLevel: number
  form: string
  strength?: string
}

interface PendingPrescription {
  id: string
  prescriptionNumber: string
  prescriptionDate: string
  patient: {
    firstName: string
    lastName: string
    patientNumber: string
  }
  doctor: {
    firstName: string
    lastName: string
  }
  items: Array<{
    id: string
    quantity: number
    dispensedQuantity: number
    medication: {
      id: string
      name: string
      form: string
      strength?: string
      stockQuantity: number
      price?: number
    }
  }>
}

export default function PharmacyDashboardPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(true)
  const [lowStockMedications, setLowStockMedications] = useState<LowStockMedication[]>([])
  const [pendingPrescriptions, setPendingPrescriptions] = useState<PendingPrescription[]>([])

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        const [lowStockResult, pendingResult] = await Promise.all([
          getLowStockMedications(),
          getPendingPrescriptions()
        ])

        if (lowStockResult.success && lowStockResult.medications) {
          setLowStockMedications(lowStockResult.medications)
        }

        if (pendingResult.success && pendingResult.prescriptions) {
          setPendingPrescriptions(pendingResult.prescriptions as any)
        }
      } catch (error) {
        console.error('Erreur lors du chargement:', error)
        toast.error('Erreur lors du chargement des données')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Calculer les statistiques
  const stats = {
    lowStockCount: lowStockMedications.length,
    pendingPrescriptions: pendingPrescriptions.length,
    totalPendingItems: pendingPrescriptions.reduce((sum, p) => sum + p.items.length, 0),
    urgentDispensing: pendingPrescriptions.filter(p => 
      new Date(p.prescriptionDate) < new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length
  }

  if (!session) {
    return <div>Chargement...</div>
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Pharmacie
            </h1>
            <p className="text-gray-600 mt-2">
              Gestion des stocks et dispensation des médicaments
            </p>
          </div>
          <div className="flex space-x-3">
            <Button asChild>
              <Link href="/dashboard/pharmacy/inventory">
                <Package className="h-4 w-4 mr-2" />
                Inventaire
              </Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard/pharmacy/dispensing">
                <Pill className="h-4 w-4 mr-2" />
                Dispensation
              </Link>
            </Button>
          </div>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Stock Faible
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.lowStockCount}
                  </p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Prescriptions en Attente
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.pendingPrescriptions}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Items à Dispenser
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.totalPendingItems}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ShoppingCart className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Dispensations Urgentes
                  </p>
                  <p className="text-2xl font-bold text-orange-600">
                    {stats.urgentDispensing}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Médicaments en stock faible */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                    Stock Faible
                  </CardTitle>
                  <CardDescription>
                    Médicaments nécessitant un réapprovisionnement
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/pharmacy/inventory">
                    <Eye className="h-4 w-4 mr-2" />
                    Voir tout
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : lowStockMedications.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun stock faible
                  </h3>
                  <p className="text-gray-600">
                    Tous les médicaments ont un stock suffisant.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {lowStockMedications.slice(0, 5).map((medication) => (
                    <div
                      key={medication.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {medication.name}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {medication.strength} • {medication.form}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="destructive">
                          {medication.stockQuantity} / {medication.minStockLevel}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {lowStockMedications.length > 5 && (
                    <div className="text-center pt-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/dashboard/pharmacy/inventory">
                          Voir {lowStockMedications.length - 5} de plus
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Prescriptions en attente */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <Pill className="h-5 w-5 text-blue-500 mr-2" />
                    Prescriptions en Attente
                  </CardTitle>
                  <CardDescription>
                    Prescriptions prêtes pour dispensation
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/pharmacy/dispensing">
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    Dispenser
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : pendingPrescriptions.length === 0 ? (
                <div className="text-center py-8">
                  <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucune prescription en attente
                  </h3>
                  <p className="text-gray-600">
                    Toutes les prescriptions ont été dispensées.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {pendingPrescriptions.slice(0, 5).map((prescription) => {
                    const isUrgent = new Date(prescription.prescriptionDate) < 
                      new Date(Date.now() - 24 * 60 * 60 * 1000)
                    
                    return (
                      <div
                        key={prescription.id}
                        className={`p-3 border rounded-lg ${isUrgent ? 'border-orange-200 bg-orange-50' : ''}`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {prescription.patient.firstName} {prescription.patient.lastName}
                            </h4>
                            <p className="text-sm text-gray-500">
                              {prescription.prescriptionNumber}
                            </p>
                          </div>
                          <div className="text-right">
                            {isUrgent && (
                              <Badge variant="destructive" className="mb-1">
                                Urgent
                              </Badge>
                            )}
                            <p className="text-xs text-gray-500">
                              {prescription.items.length} médicament(s)
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-500">
                            Dr. {prescription.doctor.firstName} {prescription.doctor.lastName}
                          </p>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/pharmacy/dispensing/${prescription.id}`}>
                              Dispenser
                            </Link>
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                  {pendingPrescriptions.length > 5 && (
                    <div className="text-center pt-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/dashboard/pharmacy/dispensing">
                          Voir {pendingPrescriptions.length - 5} de plus
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Actions rapides */}
        <Card>
          <CardHeader>
            <CardTitle>Actions Rapides</CardTitle>
            <CardDescription>
              Accès rapide aux fonctionnalités principales
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-20 flex-col" asChild>
                <Link href="/dashboard/pharmacy/inventory/movements">
                  <Plus className="h-6 w-6 mb-2" />
                  Ajouter Stock
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col" asChild>
                <Link href="/dashboard/pharmacy/dispensing">
                  <Pill className="h-6 w-6 mb-2" />
                  Nouvelle Dispensation
                </Link>
              </Button>
              <Button variant="outline" className="h-20 flex-col" asChild>
                <Link href="/dashboard/pharmacy/reports">
                  <TrendingUp className="h-6 w-6 mb-2" />
                  Rapports
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
