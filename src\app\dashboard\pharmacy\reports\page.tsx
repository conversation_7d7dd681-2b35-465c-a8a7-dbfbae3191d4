'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Package,
  DollarSign,
  Calendar,
  Download,
  Loader2
} from "lucide-react"
import Link from 'next/link'
import { getPharmacyMovements } from '@/lib/actions/pharmacy'
import { getMedications } from '@/lib/actions/prescriptions'
import { toast } from 'sonner'

// Types
interface PharmacyMovement {
  id: string
  type: string
  quantity: number
  previousStock: number
  newStock: number
  reason?: string
  totalValue?: number
  createdAt: string
  medication: {
    name: string
    form: string
    strength?: string
  }
  user: {
    firstName: string
    lastName: string
  }
}

interface Medication {
  id: string
  name: string
  stockQuantity: number
  minStockLevel: number
  price?: number
  form: string
  strength?: string
}

export default function PharmacyReportsPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(true)
  const [movements, setMovements] = useState<PharmacyMovement[]>([])
  const [medications, setMedications] = useState<Medication[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState('7') // jours
  const [selectedType, setSelectedType] = useState('all')

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        const [movementsResult, medicationsResult] = await Promise.all([
          getPharmacyMovements({ limit: 1000 }),
          getMedications({ limit: 1000 })
        ])

        if (movementsResult.success && movementsResult.movements) {
          setMovements(movementsResult.movements as any)
        }

        if (medicationsResult.success && medicationsResult.medications) {
          setMedications(medicationsResult.medications as any)
        }
      } catch (error) {
        console.error('Erreur:', error)
        toast.error('Erreur lors du chargement des données')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Filtrer les mouvements par période
  const getFilteredMovements = () => {
    const days = parseInt(selectedPeriod)
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)

    let filtered = movements.filter(movement => 
      new Date(movement.createdAt) >= cutoffDate
    )

    if (selectedType !== 'all') {
      filtered = filtered.filter(movement => movement.type === selectedType)
    }

    return filtered
  }

  // Calculer les statistiques
  const calculateStats = () => {
    const filteredMovements = getFilteredMovements()
    
    const entriesCount = filteredMovements.filter(m => m.type === 'IN').length
    const exitsCount = filteredMovements.filter(m => m.type === 'OUT').length
    const adjustmentsCount = filteredMovements.filter(m => m.type === 'ADJUSTMENT').length
    
    const totalValue = filteredMovements
      .filter(m => m.totalValue)
      .reduce((sum, m) => sum + (m.totalValue || 0), 0)
    
    const lowStockCount = medications.filter(m => 
      m.stockQuantity <= m.minStockLevel
    ).length
    
    const totalStockValue = medications.reduce((sum, m) => 
      sum + (m.stockQuantity * (m.price || 0)), 0
    )

    return {
      entriesCount,
      exitsCount,
      adjustmentsCount,
      totalMovements: filteredMovements.length,
      totalValue,
      lowStockCount,
      totalStockValue,
      totalMedications: medications.length
    }
  }

  // Obtenir les médicaments les plus mouvementés
  const getTopMovedMedications = () => {
    const filteredMovements = getFilteredMovements()
    const medicationMovements: { [key: string]: { name: string, count: number, totalQuantity: number } } = {}

    filteredMovements.forEach(movement => {
      const key = movement.medication.name
      if (!medicationMovements[key]) {
        medicationMovements[key] = {
          name: movement.medication.name,
          count: 0,
          totalQuantity: 0
        }
      }
      medicationMovements[key].count++
      medicationMovements[key].totalQuantity += Math.abs(movement.quantity)
    })

    return Object.values(medicationMovements)
      .sort((a, b) => b.totalQuantity - a.totalQuantity)
      .slice(0, 10)
  }

  // Obtenir les mouvements récents
  const getRecentMovements = () => {
    return getFilteredMovements()
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 20)
  }

  // Obtenir l'icône du type de mouvement
  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'IN':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'OUT':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'ADJUSTMENT':
        return <Package className="h-4 w-4 text-blue-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  // Obtenir le badge du type de mouvement
  const getMovementBadge = (type: string) => {
    switch (type) {
      case 'IN':
        return <Badge className="bg-green-100 text-green-800">Entrée</Badge>
      case 'OUT':
        return <Badge className="bg-red-100 text-red-800">Sortie</Badge>
      case 'ADJUSTMENT':
        return <Badge className="bg-blue-100 text-blue-800">Ajustement</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const stats = calculateStats()
  const topMedications = getTopMovedMedications()
  const recentMovements = getRecentMovements()

  if (!session) {
    return <div>Chargement...</div>
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/pharmacy">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Rapports Pharmacie
              </h1>
              <p className="text-gray-600 mt-2">
                Analyses et statistiques de la pharmacie
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exporter PDF
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exporter Excel
            </Button>
          </div>
        </div>

        {/* Filtres */}
        <Card>
          <CardHeader>
            <CardTitle>Filtres</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Période
                </label>
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 derniers jours</SelectItem>
                    <SelectItem value="30">30 derniers jours</SelectItem>
                    <SelectItem value="90">3 derniers mois</SelectItem>
                    <SelectItem value="365">12 derniers mois</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Type de mouvement
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les types</SelectItem>
                    <SelectItem value="IN">Entrées</SelectItem>
                    <SelectItem value="OUT">Sorties</SelectItem>
                    <SelectItem value="ADJUSTMENT">Ajustements</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Mouvements
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.totalMovements}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Valeur des Mouvements
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.totalValue.toLocaleString()} FCFA
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Stock Faible
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.lowStockCount}
                  </p>
                </div>
                <Package className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Valeur Stock Total
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {stats.totalStockValue.toLocaleString()} FCFA
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Médicaments les plus mouvementés */}
          <Card>
            <CardHeader>
              <CardTitle>Médicaments les Plus Mouvementés</CardTitle>
              <CardDescription>
                Top 10 des médicaments par quantité de mouvements
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : topMedications.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Aucun mouvement trouvé</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {topMedications.map((med, index) => (
                    <div key={med.name} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-semibold text-blue-600">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{med.name}</p>
                          <p className="text-sm text-gray-500">
                            {med.count} mouvement(s)
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{med.totalQuantity}</p>
                        <p className="text-xs text-gray-500">unités</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Mouvements récents */}
          <Card>
            <CardHeader>
              <CardTitle>Mouvements Récents</CardTitle>
              <CardDescription>
                Derniers mouvements de stock
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : recentMovements.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Aucun mouvement récent</p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {recentMovements.map((movement) => (
                    <div key={movement.id} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getMovementIcon(movement.type)}
                          <div>
                            <h4 className="font-medium text-sm">
                              {movement.medication.name}
                            </h4>
                            <p className="text-xs text-gray-500">
                              {movement.medication.strength} • {movement.medication.form}
                            </p>
                          </div>
                        </div>
                        {getMovementBadge(movement.type)}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-2">
                        <div>
                          <span className="font-medium">Quantité:</span> {movement.quantity}
                        </div>
                        <div>
                          <span className="font-medium">Stock:</span> {movement.previousStock} → {movement.newStock}
                        </div>
                      </div>

                      {movement.reason && (
                        <p className="text-xs text-gray-500 mb-2">
                          {movement.reason}
                        </p>
                      )}

                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>
                          {movement.user.firstName} {movement.user.lastName}
                        </span>
                        <span>
                          {new Date(movement.createdAt).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Répartition des mouvements */}
        <Card>
          <CardHeader>
            <CardTitle>Répartition des Mouvements</CardTitle>
            <CardDescription>
              Analyse des types de mouvements sur la période sélectionnée
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 border rounded-lg">
                <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-green-600">{stats.entriesCount}</h3>
                <p className="text-sm text-gray-600">Entrées de stock</p>
              </div>
              
              <div className="text-center p-6 border rounded-lg">
                <TrendingDown className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-red-600">{stats.exitsCount}</h3>
                <p className="text-sm text-gray-600">Sorties de stock</p>
              </div>
              
              <div className="text-center p-6 border rounded-lg">
                <Package className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-blue-600">{stats.adjustmentsCount}</h3>
                <p className="text-sm text-gray-600">Ajustements</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
