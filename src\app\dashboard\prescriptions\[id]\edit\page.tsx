'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  Pill,
  Package,
  Loader2,
  AlertCircle
} from "lucide-react"
import Link from 'next/link'
import { getPrescriptionById, updatePrescription, getMedications } from '@/lib/actions/prescriptions'
import { toast } from 'sonner'

// Types
interface PrescriptionData {
  id: string
  prescriptionNumber: string
  prescriptionDate: string
  status: string
  generalInstructions?: string
  notes?: string
  expiresAt?: string
  patient: {
    id: string
    firstName: string
    lastName: string
    patientNumber: string
  }
  doctor: {
    firstName: string
    lastName: string
    role: string
  }
  items: Array<{
    id: string
    dosage: string
    frequency: string
    duration: string
    timing?: string
    route?: string
    instructions?: string
    quantity: number
    isExternal: boolean
    externalMedicationName?: string
    externalMedicationForm?: string
    externalMedicationStrength?: string
    externalMedicationCategory?: string
    estimatedPrice?: number
    medication?: {
      id: string
      name: string
      genericName?: string
      strength?: string
      form: string
      category: string
    } | null
  }>
}

interface Medication {
  id: string
  name: string
  genericName?: string
  strength?: string
  form: string
  category: string
  price?: number
}

export default function EditPrescriptionPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [prescription, setPrescription] = useState<PrescriptionData | null>(null)
  const [medications, setMedications] = useState<Medication[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  
  // État du formulaire
  const [formData, setFormData] = useState({
    generalInstructions: '',
    notes: '',
    items: [] as any[]
  })

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      if (!params.id) return

      try {
        const [prescriptionResult, medicationsResult] = await Promise.all([
          getPrescriptionById(params.id as string),
          getMedications({ isActive: true, limit: 1000 })
        ])

        if (prescriptionResult.success && prescriptionResult.prescription) {
          const prescData = prescriptionResult.prescription as any
          setPrescription(prescData)
          setFormData({
            generalInstructions: prescData.generalInstructions || '',
            notes: prescData.notes || '',
            items: prescData.items.map((item: any) => ({
              id: item.id,
              medicationId: item.medicationId,
              isExternal: item.isExternal || false,
              externalMedicationName: item.externalMedicationName || '',
              externalMedicationForm: item.externalMedicationForm || '',
              externalMedicationStrength: item.externalMedicationStrength || '',
              externalMedicationCategory: item.externalMedicationCategory || 'OTHER',
              estimatedPrice: item.estimatedPrice || 0,
              dosage: item.dosage,
              frequency: item.frequency,
              duration: item.duration,
              timing: item.timing || '',
              route: item.route || '',
              instructions: item.instructions || '',
              quantity: item.quantity
            }))
          })
        } else {
          toast.error('Prescription non trouvée')
          router.push('/dashboard/prescriptions')
        }

        if (medicationsResult.success && medicationsResult.medications) {
          setMedications(medicationsResult.medications)
        }
      } catch (error) {
        console.error('Erreur lors du chargement:', error)
        toast.error('Erreur lors du chargement')
        router.push('/dashboard/prescriptions')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [params.id, router])

  // Ajouter un médicament interne
  const addInternalMedication = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        id: `new-${Date.now()}`,
        medicationId: '',
        isExternal: false,
        dosage: '',
        frequency: '',
        duration: '',
        timing: '',
        route: 'Orale',
        instructions: '',
        quantity: 1
      }]
    }))
  }

  // Ajouter un médicament externe
  const addExternalMedication = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        id: `new-${Date.now()}`,
        isExternal: true,
        externalMedicationName: '',
        externalMedicationForm: 'Comprimé',
        externalMedicationStrength: '',
        externalMedicationCategory: 'OTHER',
        estimatedPrice: 0,
        dosage: '',
        frequency: '',
        duration: '',
        timing: '',
        route: 'Orale',
        instructions: '',
        quantity: 1
      }]
    }))
  }

  // Supprimer un médicament
  const removeMedication = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  // Mettre à jour un médicament
  const updateMedication = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  // Sauvegarder
  const handleSave = async () => {
    if (!prescription) return

    setSaving(true)
    try {
      const result = await updatePrescription(prescription.id, {
        generalInstructions: formData.generalInstructions,
        notes: formData.notes,
        items: formData.items.map(item => ({
          medicationId: item.isExternal ? undefined : item.medicationId,
          isExternal: item.isExternal,
          externalMedicationName: item.externalMedicationName,
          externalMedicationForm: item.externalMedicationForm,
          externalMedicationStrength: item.externalMedicationStrength,
          externalMedicationCategory: item.externalMedicationCategory,
          estimatedPrice: item.estimatedPrice,
          dosage: item.dosage,
          frequency: item.frequency,
          duration: item.duration,
          timing: item.timing,
          route: item.route,
          instructions: item.instructions,
          quantity: item.quantity
        }))
      })

      if (result.success) {
        toast.success('Prescription mise à jour avec succès')
        router.push(`/dashboard/prescriptions/${prescription.id}`)
      } else {
        toast.error(result.error || 'Erreur lors de la mise à jour')
      }
    } catch (error) {
      console.error('Erreur:', error)
      toast.error('Erreur lors de la mise à jour')
    } finally {
      setSaving(false)
    }
  }

  if (!session) {
    return <div>Chargement...</div>
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement de la prescription...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  if (!prescription) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Prescription non trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Cette prescription n'existe pas ou vous n'avez pas les permissions pour la modifier.
              </p>
              <Button asChild>
                <Link href="/dashboard/prescriptions">
                  Retour aux prescriptions
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/prescriptions/${prescription.id}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Modifier Prescription {prescription.prescriptionNumber}
              </h1>
              <p className="text-gray-600 mt-2">
                Patient: {prescription.patient.firstName} {prescription.patient.lastName}
              </p>
            </div>
          </div>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Sauvegarder
          </Button>
        </div>

        {/* Formulaire */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Instructions */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Instructions et Notes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="generalInstructions">Instructions générales</Label>
                  <Textarea
                    id="generalInstructions"
                    value={formData.generalInstructions}
                    onChange={(e) => setFormData(prev => ({ ...prev, generalInstructions: e.target.value }))}
                    placeholder="Instructions pour le patient..."
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="notes">Notes du médecin</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Notes privées..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Médicaments */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Médicaments Prescrits</CardTitle>
                    <CardDescription>
                      Modifiez les médicaments et leurs posologies
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={addInternalMedication}>
                      <Package className="h-4 w-4 mr-2" />
                      Médicament Interne
                    </Button>
                    <Button variant="outline" size="sm" onClick={addExternalMedication}>
                      <Pill className="h-4 w-4 mr-2" />
                      Médicament Externe
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {formData.items.length === 0 ? (
                  <div className="text-center py-8">
                    <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun médicament prescrit
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Ajoutez des médicaments à cette prescription.
                    </p>
                    <div className="flex justify-center space-x-2">
                      <Button variant="outline" onClick={addInternalMedication}>
                        <Package className="h-4 w-4 mr-2" />
                        Médicament Interne
                      </Button>
                      <Button variant="outline" onClick={addExternalMedication}>
                        <Pill className="h-4 w-4 mr-2" />
                        Médicament Externe
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {formData.items.map((item, index) => (
                      <Card key={item.id} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold">Médicament {index + 1}</h4>
                              {item.isExternal ? (
                                <Badge variant="outline" className="bg-orange-50 text-orange-700">
                                  Externe
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-green-50 text-green-700">
                                  Interne
                                </Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeMedication(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Sélection du médicament */}
                          {item.isExternal ? (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <Label>Nom du médicament</Label>
                                <Input
                                  value={item.externalMedicationName}
                                  onChange={(e) => updateMedication(index, 'externalMedicationName', e.target.value)}
                                  placeholder="Nom du médicament..."
                                />
                              </div>
                              <div>
                                <Label>Forme</Label>
                                <Select
                                  value={item.externalMedicationForm}
                                  onValueChange={(value) => updateMedication(index, 'externalMedicationForm', value)}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Comprimé">Comprimé</SelectItem>
                                    <SelectItem value="Gélule">Gélule</SelectItem>
                                    <SelectItem value="Sirop">Sirop</SelectItem>
                                    <SelectItem value="Injection">Injection</SelectItem>
                                    <SelectItem value="Pommade">Pommade</SelectItem>
                                    <SelectItem value="Gouttes">Gouttes</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label>Dosage/Force</Label>
                                <Input
                                  value={item.externalMedicationStrength}
                                  onChange={(e) => updateMedication(index, 'externalMedicationStrength', e.target.value)}
                                  placeholder="ex: 500mg, 250mg/5ml..."
                                />
                              </div>
                              <div>
                                <Label>Prix estimé (FCFA)</Label>
                                <Input
                                  type="number"
                                  value={item.estimatedPrice}
                                  onChange={(e) => updateMedication(index, 'estimatedPrice', parseFloat(e.target.value) || 0)}
                                  placeholder="Prix estimé..."
                                />
                              </div>
                            </div>
                          ) : (
                            <div className="mb-4">
                              <Label>Médicament</Label>
                              <Select
                                value={item.medicationId}
                                onValueChange={(value) => updateMedication(index, 'medicationId', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner un médicament..." />
                                </SelectTrigger>
                                <SelectContent>
                                  {medications.map((med) => (
                                    <SelectItem key={med.id} value={med.id}>
                                      {med.name} {med.strength} - {med.form}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}

                          {/* Posologie */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <Label>Dosage</Label>
                              <Input
                                value={item.dosage}
                                onChange={(e) => updateMedication(index, 'dosage', e.target.value)}
                                placeholder="ex: 1 comprimé, 5ml..."
                              />
                            </div>
                            <div>
                              <Label>Fréquence</Label>
                              <Input
                                value={item.frequency}
                                onChange={(e) => updateMedication(index, 'frequency', e.target.value)}
                                placeholder="ex: 3 fois par jour..."
                              />
                            </div>
                            <div>
                              <Label>Durée</Label>
                              <Input
                                value={item.duration}
                                onChange={(e) => updateMedication(index, 'duration', e.target.value)}
                                placeholder="ex: 7 jours, 2 semaines..."
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <Label>Quantité</Label>
                              <Input
                                type="number"
                                value={item.quantity}
                                onChange={(e) => updateMedication(index, 'quantity', parseInt(e.target.value) || 1)}
                                min="1"
                              />
                            </div>
                            <div>
                              <Label>Voie d'administration</Label>
                              <Select
                                value={item.route}
                                onValueChange={(value) => updateMedication(index, 'route', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Orale">Orale</SelectItem>
                                  <SelectItem value="Intraveineuse">Intraveineuse</SelectItem>
                                  <SelectItem value="Intramusculaire">Intramusculaire</SelectItem>
                                  <SelectItem value="Topique">Topique</SelectItem>
                                  <SelectItem value="Sublinguale">Sublinguale</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Moment de prise</Label>
                              <Input
                                value={item.timing}
                                onChange={(e) => updateMedication(index, 'timing', e.target.value)}
                                placeholder="ex: Avant repas..."
                              />
                            </div>
                          </div>

                          {/* Instructions spéciales */}
                          <div className="mt-4">
                            <Label>Instructions spéciales</Label>
                            <Textarea
                              value={item.instructions}
                              onChange={(e) => updateMedication(index, 'instructions', e.target.value)}
                              placeholder="Instructions particulières pour ce médicament..."
                              rows={2}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
