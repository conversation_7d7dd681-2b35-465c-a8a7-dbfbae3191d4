"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Printer,
  Download,
  User,
  Calendar,
  Pill,
  FileText,
  Stethoscope,
  Phone,
  Mail,
  AlertCircle,
  Loader2,
  Clock,
  CheckCircle,
  XCircle,
  Package,
} from "lucide-react";
import Link from "next/link";
import { getPrescriptionById } from "@/lib/actions/prescriptions";
import { formatDate, formatDateTime } from "@/lib/utils/date-utils";
import {
  getPatientInitials,
  calculateAge,
  formatGender,
} from "@/lib/utils/patient-utils";

// Types
interface PrescriptionData {
  id: string;
  prescriptionNumber: string;
  prescriptionDate: string;
  status: string;
  generalInstructions?: string;
  notes?: string;
  expiresAt?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
    dateOfBirth: string;
    gender: string;
    bloodType?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
    phone?: string;
    email?: string;
  };
  consultation?: {
    id: string;
    consultationDate: string;
    diagnosis?: string;
  };
  items: Array<{
    id: string;
    dosage: string;
    frequency: string;
    duration: string;
    timing?: string;
    route?: string;
    instructions?: string;
    quantity: number;
    // Support des médicaments externes
    isExternal: boolean;
    externalMedicationName?: string;
    externalMedicationForm?: string;
    externalMedicationStrength?: string;
    externalMedicationCategory?: string;
    estimatedPrice?: number;
    // Médicament interne (peut être null pour les externes)
    medication?: {
      id: string;
      name: string;
      genericName?: string;
      strength?: string;
      form: string;
      category: string;
      manufacturer?: string;
      unitPrice?: number;
    } | null;
  }>;
}

export default function PrescriptionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [prescription, setPrescription] = useState<PrescriptionData | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  // Charger les données de la prescription
  useEffect(() => {
    const loadPrescription = async () => {
      if (!params.id) return;

      try {
        console.log("🔄 Chargement de la prescription:", params.id);
        const result = await getPrescriptionById(params.id as string);

        if (result.success && result.prescription) {
          console.log("✅ Prescription chargée:", result.prescription);
          setPrescription(result.prescription as any);
        } else {
          console.error("❌ Erreur lors du chargement:", result.error);
          router.push("/dashboard/prescriptions");
        }
      } catch (error) {
        console.error("💥 Exception lors du chargement:", error);
        router.push("/dashboard/prescriptions");
      } finally {
        setLoading(false);
      }
    };

    loadPrescription();
  }, [params.id, router]);

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: {
        label: "Active",
        className: "bg-green-100 text-green-800",
        icon: CheckCircle,
      },
      EXPIRED: {
        label: "Expirée",
        className: "bg-red-100 text-red-800",
        icon: XCircle,
      },
      DISPENSED: {
        label: "Dispensée",
        className: "bg-blue-100 text-blue-800",
        icon: Package,
      },
      CANCELLED: {
        label: "Annulée",
        className: "bg-gray-100 text-gray-800",
        icon: XCircle,
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    const IconComponent = config.icon;

    return (
      <Badge variant="secondary" className={config.className}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  // Obtenir le badge de catégorie
  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      ANTIBIOTIC: {
        label: "Antibiotique",
        className: "bg-red-50 text-red-700",
      },
      ANALGESIC: { label: "Antalgique", className: "bg-blue-50 text-blue-700" },
      ANTI_INFLAMMATORY: {
        label: "Anti-inflammatoire",
        className: "bg-orange-50 text-orange-700",
      },
      ANTIHYPERTENSIVE: {
        label: "Antihypertenseur",
        className: "bg-purple-50 text-purple-700",
      },
      ANTIDIABETIC: {
        label: "Antidiabétique",
        className: "bg-green-50 text-green-700",
      },
      VITAMIN: { label: "Vitamine", className: "bg-yellow-50 text-yellow-700" },
      OTHER: { label: "Autre", className: "bg-gray-50 text-gray-700" },
    };

    const config =
      categoryConfig[category as keyof typeof categoryConfig] ||
      categoryConfig.OTHER;
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Calculer le coût total
  const calculateTotalCost = () => {
    if (!prescription) return 0;
    return prescription.items.reduce((total, item) => {
      if (item.isExternal) {
        // Pour les médicaments externes, utiliser le prix estimé
        const estimatedPrice = item.estimatedPrice || 0;
        return total + estimatedPrice * item.quantity;
      } else {
        // Pour les médicaments internes, utiliser le prix unitaire
        const unitPrice = item.medication?.unitPrice || 0;
        return total + unitPrice * item.quantity;
      }
    }, 0);
  };

  // Calculer le coût des médicaments internes
  const calculateInternalCost = () => {
    if (!prescription) return 0;
    return prescription.items
      .filter((item) => !item.isExternal)
      .reduce((total, item) => {
        const unitPrice = item.medication?.unitPrice || 0;
        return total + unitPrice * item.quantity;
      }, 0);
  };

  // Calculer le coût des médicaments externes
  const calculateExternalCost = () => {
    if (!prescription) return 0;
    return prescription.items
      .filter((item) => item.isExternal)
      .reduce((total, item) => {
        const estimatedPrice = item.estimatedPrice || 0;
        return total + estimatedPrice * item.quantity;
      }, 0);
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement de la prescription...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!prescription) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Prescription non trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Cette prescription n'existe pas ou vous n'avez pas les
                permissions pour y accéder.
              </p>
              <Button asChild>
                <Link href="/dashboard/prescriptions">
                  Retour aux prescriptions
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/prescriptions">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Prescription {prescription.prescriptionNumber}
              </h1>
              <p className="text-gray-600 mt-2">
                {prescription.patient.firstName} {prescription.patient.lastName}{" "}
                • {formatDateTime(prescription.prescriptionDate)}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(prescription.status)}
            <div className="flex space-x-2">
              <Button variant="outline" asChild>
                <Link href={`/dashboard/prescriptions/${prescription.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Modifier
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link
                  href={`/dashboard/prescriptions/${prescription.id}/print`}
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Imprimer
                </Link>
              </Button>
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Télécharger PDF
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations du patient */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Patient
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src="" />
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {getPatientInitials(
                        prescription.patient.firstName,
                        prescription.patient.lastName
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">
                      {prescription.patient.firstName}{" "}
                      {prescription.patient.lastName}
                    </h3>
                    <p className="text-gray-600 font-mono text-sm">
                      {prescription.patient.patientNumber}
                    </p>
                    <div className="flex items-center space-x-3 mt-2">
                      <span className="text-sm text-gray-500">
                        {calculateAge(prescription.patient.dateOfBirth)} ans
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatGender(prescription.patient.gender)}
                      </span>
                      {prescription.patient.bloodType && (
                        <Badge
                          variant="outline"
                          className="bg-red-50 text-red-700"
                        >
                          {prescription.patient.bloodType}
                        </Badge>
                      )}
                    </div>
                    <div className="mt-3 space-y-1">
                      {prescription.patient.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="h-3 w-3 mr-2" />
                          {prescription.patient.phone}
                        </div>
                      )}
                      {prescription.patient.email && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="h-3 w-3 mr-2" />
                          {prescription.patient.email}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Informations du médecin */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Stethoscope className="h-5 w-5 mr-2" />
                  Médecin Prescripteur
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <h3 className="font-semibold">
                    Dr. {prescription.doctor.firstName}{" "}
                    {prescription.doctor.lastName}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {prescription.doctor.role}
                  </p>
                  <div className="mt-3 space-y-1">
                    {prescription.doctor.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-3 w-3 mr-2" />
                        {prescription.doctor.phone}
                      </div>
                    )}
                    {prescription.doctor.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-3 w-3 mr-2" />
                        {prescription.doctor.email}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Informations de la consultation */}
            {prescription.consultation && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Consultation Liée
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <Calendar className="h-3 w-3 mr-2 text-gray-400" />
                      {formatDateTime(
                        prescription.consultation.consultationDate
                      )}
                    </div>
                    {prescription.consultation.diagnosis && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">
                          Diagnostic :
                        </p>
                        <p className="text-sm text-gray-600">
                          {prescription.consultation.diagnosis}
                        </p>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                      className="w-full mt-3"
                    >
                      <Link
                        href={`/dashboard/consultations/${prescription.consultation.id}`}
                      >
                        Voir la consultation
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Médicaments prescrits */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Pill className="h-5 w-5 mr-2" />
                  Médicaments Prescrits ({prescription.items.length})
                </CardTitle>
                <CardDescription>
                  Détails de la posologie et instructions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {prescription.items.map((item, index) => (
                    <Card
                      key={item.id}
                      className="border-l-4 border-l-blue-500"
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold text-lg text-gray-900">
                                {item.isExternal
                                  ? item.externalMedicationName
                                  : item.medication?.name ||
                                    "Médicament inconnu"}
                              </h4>
                              {item.isExternal && (
                                <Badge
                                  variant="outline"
                                  className="bg-orange-50 text-orange-700"
                                >
                                  Externe
                                </Badge>
                              )}
                            </div>
                            {!item.isExternal &&
                              item.medication?.genericName && (
                                <p className="text-sm text-gray-500">
                                  {item.medication.genericName}
                                </p>
                              )}
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-sm text-gray-500">
                                {item.isExternal
                                  ? `${item.externalMedicationStrength} • ${item.externalMedicationForm}`
                                  : `${item.medication?.strength} • ${item.medication?.form}`}
                              </span>
                              {getCategoryBadge(
                                item.isExternal
                                  ? item.externalMedicationCategory || "OTHER"
                                  : item.medication?.category || "OTHER"
                              )}
                            </div>
                            {!item.isExternal &&
                              item.medication?.manufacturer && (
                                <p className="text-xs text-gray-400 mt-1">
                                  {item.medication.manufacturer}
                                </p>
                              )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-700">
                              Quantité: {item.quantity}
                            </p>
                            {item.isExternal
                              ? item.estimatedPrice && (
                                  <p className="text-sm text-green-600">
                                    {item.estimatedPrice} FCFA/unité (estimé)
                                  </p>
                                )
                              : item.medication?.unitPrice && (
                                  <p className="text-sm text-gray-500">
                                    {item.medication.unitPrice} FCFA/unité
                                  </p>
                                )}
                          </div>
                        </div>

                        <Separator className="my-3" />

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div>
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Dosage
                            </p>
                            <p className="text-sm font-medium text-gray-900 mt-1">
                              {item.dosage}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Fréquence
                            </p>
                            <p className="text-sm font-medium text-gray-900 mt-1">
                              {item.frequency}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Durée
                            </p>
                            <p className="text-sm font-medium text-gray-900 mt-1">
                              {item.duration}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Voie
                            </p>
                            <p className="text-sm font-medium text-gray-900 mt-1">
                              {item.route || "Orale"}
                            </p>
                          </div>
                        </div>

                        {item.timing && (
                          <div className="mt-3">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Moment de prise
                            </p>
                            <p className="text-sm text-gray-700 mt-1">
                              {item.timing}
                            </p>
                          </div>
                        )}

                        {item.instructions && (
                          <div className="mt-3">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              Instructions spéciales
                            </p>
                            <p className="text-sm text-gray-700 mt-1">
                              {item.instructions}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Instructions générales */}
            {(prescription.generalInstructions || prescription.notes) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Instructions et Notes
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {prescription.generalInstructions && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Instructions générales pour le patient
                      </h4>
                      <p className="text-gray-700 bg-blue-50 p-3 rounded-lg">
                        {prescription.generalInstructions}
                      </p>
                    </div>
                  )}
                  {prescription.notes && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Notes du médecin
                      </h4>
                      <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                        {prescription.notes}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Résumé financier */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Résumé Financier
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {prescription.items.map((item) => (
                    <div
                      key={item.id}
                      className="flex justify-between items-center"
                    >
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-sm">
                            {item.isExternal
                              ? item.externalMedicationName
                              : item.medication?.name || "Médicament inconnu"}
                          </p>
                          {item.isExternal && (
                            <Badge
                              variant="outline"
                              className="bg-orange-50 text-orange-700 text-xs"
                            >
                              Externe
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500">
                          {item.quantity} ×{" "}
                          {item.isExternal
                            ? item.estimatedPrice || 0
                            : item.medication?.unitPrice || 0}{" "}
                          FCFA {item.isExternal ? "(estimé)" : ""}
                        </p>
                      </div>
                      <p className="font-medium">
                        {(item.isExternal
                          ? (item.estimatedPrice || 0) * item.quantity
                          : (item.medication?.unitPrice || 0) * item.quantity
                        ).toLocaleString()}{" "}
                        FCFA
                      </p>
                    </div>
                  ))}
                  <Separator />
                  <div className="flex justify-between items-center font-semibold text-lg">
                    <span>Total</span>
                    <span className="text-blue-600">
                      {calculateTotalCost().toLocaleString()} FCFA
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Informations sur l'expiration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Informations Temporelles
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Date de prescription
                    </p>
                    <p className="text-sm font-semibold text-gray-900">
                      {formatDateTime(prescription.prescriptionDate)}
                    </p>
                  </div>
                  {prescription.expiresAt && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Date d'expiration
                      </p>
                      <p className="text-sm font-semibold text-gray-900">
                        {formatDateTime(prescription.expiresAt)}
                      </p>
                      {new Date(prescription.expiresAt) < new Date() && (
                        <Badge variant="destructive" className="mt-1">
                          Expirée
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
