"use client";

import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Printer,
  Download,
  FileText,
  Calendar,
  User,
  Stethoscope,
  Pill,
  Phone,
  Mail,
  MapPin,
  Loader2,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";
import { getPrescriptionById } from "@/lib/actions/prescriptions";
import { formatDate, formatDateTime } from "@/lib/utils/date-utils";
import {
  getPatientInitials,
  calculateAge,
  formatGender,
} from "@/lib/utils/patient-utils";

// Types
interface PrescriptionData {
  id: string;
  prescriptionNumber: string;
  prescriptionDate: string;
  status: string;
  generalInstructions?: string;
  notes?: string;
  expiresAt?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
    dateOfBirth: string;
    gender: string;
    bloodType?: string;
    address?: string;
  };
  doctor: {
    firstName: string;
    lastName: string;
    role: string;
    phone?: string;
    email?: string;
    licenseNumber?: string;
  };
  consultation?: {
    id: string;
    consultationDate: string;
    diagnosis?: string;
  };
  items: Array<{
    id: string;
    dosage: string;
    frequency: string;
    duration: string;
    timing?: string;
    route?: string;
    instructions?: string;
    quantity: number;
    // Support des médicaments externes
    isExternal: boolean;
    externalMedicationName?: string;
    externalMedicationForm?: string;
    externalMedicationStrength?: string;
    externalMedicationCategory?: string;
    estimatedPrice?: number;
    // Médicament interne (peut être null pour les externes)
    medication?: {
      id: string;
      name: string;
      genericName?: string;
      strength?: string;
      form: string;
      category: string;
      manufacturer?: string;
    } | null;
  }>;
}

export default function PrintPrescriptionPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [prescription, setPrescription] = useState<PrescriptionData | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  // Charger les données de la prescription
  useEffect(() => {
    const loadPrescription = async () => {
      if (!params.id) return;

      try {
        console.log(
          "🔄 Chargement de la prescription pour impression:",
          params.id
        );
        const result = await getPrescriptionById(params.id as string);

        if (result.success && result.prescription) {
          console.log(
            "✅ Prescription chargée pour impression:",
            result.prescription
          );
          setPrescription(result.prescription as any);
        } else {
          console.error("❌ Erreur lors du chargement:", result.error);
          router.push("/dashboard/prescriptions");
        }
      } catch (error) {
        console.error("💥 Exception lors du chargement:", error);
        router.push("/dashboard/prescriptions");
      } finally {
        setLoading(false);
      }
    };

    loadPrescription();
  }, [params.id, router]);

  // Fonction d'impression
  const handlePrint = () => {
    window.print();
  };

  // Fonction de téléchargement PDF (simulation)
  const handleDownloadPDF = () => {
    // TODO: Implémenter la génération PDF réelle
    alert("Fonctionnalité de téléchargement PDF à implémenter");
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement de l'ordonnance...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (!prescription) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Prescription non trouvée
              </h3>
              <p className="text-gray-600 mb-4">
                Cette prescription n'existe pas ou vous n'avez pas les
                permissions pour y accéder.
              </p>
              <Button asChild>
                <Link href="/dashboard/prescriptions">
                  Retour aux prescriptions
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header - Masqué à l'impression */}
        <div className="flex items-center justify-between print:hidden">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/prescriptions/${prescription.id}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Ordonnance {prescription.prescriptionNumber}
              </h1>
              <p className="text-gray-600 mt-2">Aperçu avant impression</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleDownloadPDF}>
              <Download className="h-4 w-4 mr-2" />
              Télécharger PDF
            </Button>
            <Button onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Imprimer
            </Button>
          </div>
        </div>

        {/* Ordonnance - Format d'impression */}
        <div className="bg-white border rounded-lg p-8 max-w-4xl mx-auto print:border-0 print:shadow-none print:max-w-none">
          {/* En-tête de l'ordonnance */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              ORDONNANCE MÉDICALE
            </h1>
            <div className="text-sm text-gray-600">
              <p>GlobalCare Solutions - Système de Gestion Hospitalière</p>
              <p>Hôpital Principal - Bamako, Mali</p>
            </div>
          </div>

          {/* Informations du médecin */}
          <div className="mb-6">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-2">
                  Dr. {prescription.doctor.firstName}{" "}
                  {prescription.doctor.lastName}
                </h2>
                <p className="text-gray-600">{prescription.doctor.role}</p>
                {prescription.doctor.licenseNumber && (
                  <p className="text-sm text-gray-500">
                    N° Ordre: {prescription.doctor.licenseNumber}
                  </p>
                )}
                <div className="mt-2 space-y-1">
                  {prescription.doctor.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-3 w-3 mr-2" />
                      {prescription.doctor.phone}
                    </div>
                  )}
                  {prescription.doctor.email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="h-3 w-3 mr-2" />
                      {prescription.doctor.email}
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">Ordonnance N°</p>
                <p className="font-mono font-semibold">
                  {prescription.prescriptionNumber}
                </p>
                <p className="text-sm text-gray-500 mt-2">Date</p>
                <p className="font-semibold">
                  {formatDate(prescription.prescriptionDate)}
                </p>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Informations du patient */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <User className="h-5 w-5 mr-2" />
              Informations Patient
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-semibold text-gray-900">
                  {prescription.patient.firstName}{" "}
                  {prescription.patient.lastName}
                </p>
                <p className="text-sm text-gray-600">
                  N° Patient: {prescription.patient.patientNumber}
                </p>
                <p className="text-sm text-gray-600">
                  {calculateAge(prescription.patient.dateOfBirth)} ans •{" "}
                  {formatGender(prescription.patient.gender)}
                </p>
                {prescription.patient.bloodType && (
                  <p className="text-sm text-gray-600">
                    Groupe sanguin: {prescription.patient.bloodType}
                  </p>
                )}
              </div>
              <div>
                {prescription.patient.phone && (
                  <div className="flex items-center text-sm text-gray-600 mb-1">
                    <Phone className="h-3 w-3 mr-2" />
                    {prescription.patient.phone}
                  </div>
                )}
                {prescription.patient.email && (
                  <div className="flex items-center text-sm text-gray-600 mb-1">
                    <Mail className="h-3 w-3 mr-2" />
                    {prescription.patient.email}
                  </div>
                )}
                {prescription.patient.address && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-3 w-3 mr-2" />
                    {prescription.patient.address}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Diagnostic (si disponible) */}
          {prescription.consultation?.diagnosis && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Diagnostic
              </h3>
              <p className="text-gray-700 bg-gray-50 p-3 rounded">
                {prescription.consultation.diagnosis}
              </p>
            </div>
          )}

          <Separator className="my-6" />

          {/* Médicaments prescrits */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Pill className="h-5 w-5 mr-2" />
              Prescription
            </h3>
            <div className="space-y-4">
              {prescription.items.map((item, index) => (
                <div
                  key={item.id}
                  className={`border-l-4 pl-4 py-2 ${
                    item.isExternal
                      ? "border-l-orange-500"
                      : "border-l-blue-500"
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900">
                          {index + 1}.{" "}
                          {item.isExternal
                            ? item.externalMedicationName
                            : item.medication?.name || "Médicament inconnu"}
                        </h4>
                        {item.isExternal && (
                          <Badge
                            variant="outline"
                            className="bg-orange-50 text-orange-700 text-xs"
                          >
                            Externe
                          </Badge>
                        )}
                      </div>
                      {!item.isExternal && item.medication?.genericName && (
                        <p className="text-sm text-gray-600">
                          ({item.medication.genericName})
                        </p>
                      )}
                      <p className="text-sm text-gray-500">
                        {item.isExternal
                          ? `${item.externalMedicationStrength} • ${item.externalMedicationForm}`
                          : `${item.medication?.strength} • ${item.medication?.form}`}
                      </p>
                      {item.isExternal && item.estimatedPrice && (
                        <p className="text-xs text-green-600 font-medium">
                          Prix estimé: {item.estimatedPrice} FCFA/unité
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        Qté: {item.quantity}
                      </p>
                      {item.isExternal && (
                        <p className="text-xs text-orange-600">
                          À acheter en pharmacie
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Dosage:</span>
                      <p className="text-gray-600">{item.dosage}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">
                        Fréquence:
                      </span>
                      <p className="text-gray-600">{item.frequency}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Durée:</span>
                      <p className="text-gray-600">{item.duration}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Voie:</span>
                      <p className="text-gray-600">{item.route || "Orale"}</p>
                    </div>
                  </div>

                  {item.timing && (
                    <div className="mt-2">
                      <span className="font-medium text-gray-700 text-sm">
                        Moment:
                      </span>
                      <span className="text-gray-600 text-sm ml-2">
                        {item.timing}
                      </span>
                    </div>
                  )}

                  {item.instructions && (
                    <div className="mt-2">
                      <span className="font-medium text-gray-700 text-sm">
                        Instructions:
                      </span>
                      <p className="text-gray-600 text-sm italic">
                        {item.instructions}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Instructions générales */}
          {prescription.generalInstructions && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Instructions Générales
              </h3>
              <p className="text-gray-700 bg-blue-50 p-3 rounded border-l-4 border-l-blue-500">
                {prescription.generalInstructions}
              </p>
            </div>
          )}

          <Separator className="my-6" />

          {/* Pied de page */}
          <div className="flex justify-between items-end">
            <div>
              {prescription.expiresAt && (
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Valable jusqu'au:</span>{" "}
                  {formatDate(prescription.expiresAt)}
                </p>
              )}
              <p className="text-xs text-gray-500 mt-2">
                Ordonnance générée le {formatDateTime(new Date())}
              </p>
            </div>
            <div className="text-center">
              <div className="border-t border-gray-300 pt-2 mt-8 w-48">
                <p className="text-sm font-medium">Signature du médecin</p>
                <p className="text-xs text-gray-500 mt-1">
                  Dr. {prescription.doctor.firstName}{" "}
                  {prescription.doctor.lastName}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
