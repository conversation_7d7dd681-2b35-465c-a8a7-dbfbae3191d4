"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  Plus,
  Save,
  Search,
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON>ader2,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import {
  createPrescription,
  getMedications,
} from "@/lib/actions/prescriptions";
import { getPatients } from "@/lib/actions/patients-new";

// Types
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  patientNumber: string;
  phone?: string;
  email?: string;
}

interface Medication {
  id: string;
  name: string;
  genericName?: string;
  strength?: string;
  form: string;
  category: string;
  manufacturer?: string;
}

interface PrescriptionItem {
  medicationId?: string; // Optionnel pour médicaments externes
  medication?: Medication;
  // Champs pour médicaments externes
  isExternal: boolean;
  externalMedicationName?: string;
  externalMedicationForm?: string;
  externalMedicationStrength?: string;
  externalMedicationCategory?: string;
  estimatedPrice?: number;
  // Champs communs
  dosage: string;
  frequency: string;
  duration: string;
  timing?: string;
  route?: string;
  instructions?: string;
  quantity: number;
}

export default function NewPrescriptionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();

  // États pour les données
  const [patients, setPatients] = useState<Patient[]>([]);
  const [medications, setMedications] = useState<Medication[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showMedicationDialog, setShowMedicationDialog] = useState(false);
  const [showExternalMedicationDialog, setShowExternalMedicationDialog] =
    useState(false);
  const [medicationSearch, setMedicationSearch] = useState("");

  // États pour le formulaire de médicament externe
  const [externalMedicationForm, setExternalMedicationForm] = useState({
    name: "",
    form: "Comprimé",
    strength: "",
    category: "OTHER",
    estimatedPrice: 0,
  });

  // États pour le formulaire
  const [formData, setFormData] = useState({
    patientId: "",
    consultationId: searchParams.get("consultationId") || "",
    generalInstructions: "",
    notes: "",
    expiresAt: "",
  });

  const [prescriptionItems, setPrescriptionItems] = useState<
    PrescriptionItem[]
  >([]);

  // Charger les données initiales
  useEffect(() => {
    const loadData = async () => {
      try {
        const [patientsResult, medicationsResult] = await Promise.all([
          getPatients({}),
          getMedications({ isActive: true, limit: 100 }),
        ]);

        if (patientsResult.success && patientsResult.patients) {
          setPatients(patientsResult.patients);
        }

        if (medicationsResult.success && medicationsResult.medications) {
          setMedications(medicationsResult.medications);
        }

        // Si un patient est spécifié dans l'URL
        const patientId = searchParams.get("patientId");
        if (patientId) {
          setFormData((prev) => ({ ...prev, patientId }));
        }
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [searchParams]);

  // Gérer les changements de formulaire
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Ajouter un médicament interne à la prescription
  const addMedication = (medication: Medication) => {
    const newItem: PrescriptionItem = {
      medicationId: medication.id,
      medication,
      isExternal: false, // Médicament interne
      dosage: "1 comprimé",
      frequency: "3 fois par jour",
      duration: "7 jours",
      timing: "avant les repas",
      route: "orale",
      instructions: "",
      quantity: 21, // 3 fois par jour × 7 jours
    };

    setPrescriptionItems((prev) => [...prev, newItem]);
    setShowMedicationDialog(false);
    setMedicationSearch("");
  };

  // Ajouter un médicament externe à la prescription
  const addExternalMedication = (medicationData: {
    name: string;
    form: string;
    strength: string;
    category: string;
    estimatedPrice?: number;
  }) => {
    const newItem: PrescriptionItem = {
      isExternal: true, // Médicament externe
      externalMedicationName: medicationData.name,
      externalMedicationForm: medicationData.form,
      externalMedicationStrength: medicationData.strength,
      externalMedicationCategory: medicationData.category,
      estimatedPrice: medicationData.estimatedPrice,
      dosage: "1 comprimé",
      frequency: "3 fois par jour",
      duration: "7 jours",
      timing: "avant les repas",
      route: "orale",
      instructions: "",
      quantity: 21,
    };

    setPrescriptionItems((prev) => [...prev, newItem]);
    setShowExternalMedicationDialog(false);
  };

  // Supprimer un médicament de la prescription
  const removeMedication = (index: number) => {
    setPrescriptionItems((prev) => prev.filter((_, i) => i !== index));
  };

  // Mettre à jour un item de prescription
  const updatePrescriptionItem = (
    index: number,
    field: string,
    value: string | number
  ) => {
    setPrescriptionItems((prev) =>
      prev.map((item, i) => (i === index ? { ...item, [field]: value } : item))
    );
  };

  // Sauvegarder la prescription
  const handleSave = async () => {
    if (!formData.patientId) {
      alert("Veuillez sélectionner un patient");
      return;
    }

    if (prescriptionItems.length === 0) {
      alert("Veuillez ajouter au moins un médicament");
      return;
    }

    setSaving(true);
    try {
      const prescriptionData = {
        patientId: formData.patientId,
        consultationId: formData.consultationId || undefined,
        generalInstructions: formData.generalInstructions || undefined,
        notes: formData.notes || undefined,
        expiresAt: formData.expiresAt
          ? new Date(formData.expiresAt)
          : undefined,
        items: prescriptionItems.map((item) => ({
          medicationId: item.isExternal ? undefined : item.medicationId,
          // Champs pour médicaments externes
          isExternal: item.isExternal,
          externalMedicationName: item.externalMedicationName,
          externalMedicationForm: item.externalMedicationForm,
          externalMedicationStrength: item.externalMedicationStrength,
          externalMedicationCategory: item.externalMedicationCategory,
          estimatedPrice: item.estimatedPrice,
          // Champs communs
          dosage: item.dosage,
          frequency: item.frequency,
          duration: item.duration,
          timing: item.timing,
          route: item.route,
          instructions: item.instructions,
          quantity: item.quantity,
        })),
      };

      console.log("💾 Création de la prescription:", prescriptionData);

      const result = await createPrescription(prescriptionData);

      if (result.success) {
        console.log("✅ Prescription créée avec succès");
        router.push("/dashboard/prescriptions");
      } else {
        console.error("❌ Erreur lors de la création:", result.error);
        alert("Erreur lors de la création: " + result.error);
      }
    } catch (error) {
      console.error("💥 Exception lors de la création:", error);
      alert("Une erreur est survenue lors de la création");
    } finally {
      setSaving(false);
    }
  };

  // Filtrer les médicaments pour la recherche
  const filteredMedications = medications.filter(
    (med) =>
      med.name.toLowerCase().includes(medicationSearch.toLowerCase()) ||
      (med.genericName &&
        med.genericName.toLowerCase().includes(medicationSearch.toLowerCase()))
  );

  // Obtenir le badge de catégorie
  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      ANTIBIOTIC: {
        label: "Antibiotique",
        className: "bg-red-50 text-red-700",
      },
      ANALGESIC: { label: "Antalgique", className: "bg-blue-50 text-blue-700" },
      ANTI_INFLAMMATORY: {
        label: "Anti-inflammatoire",
        className: "bg-orange-50 text-orange-700",
      },
      ANTIHYPERTENSIVE: {
        label: "Antihypertenseur",
        className: "bg-purple-50 text-purple-700",
      },
      ANTIDIABETIC: {
        label: "Antidiabétique",
        className: "bg-green-50 text-green-700",
      },
      VITAMIN: { label: "Vitamine", className: "bg-yellow-50 text-yellow-700" },
      OTHER: { label: "Autre", className: "bg-gray-50 text-gray-700" },
    };

    const config =
      categoryConfig[category as keyof typeof categoryConfig] ||
      categoryConfig.OTHER;
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Chargement des données...
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/prescriptions">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Nouvelle Prescription
              </h1>
              <p className="text-gray-600 mt-2">
                Créer une nouvelle prescription médicale
              </p>
            </div>
          </div>
          <Button
            onClick={handleSave}
            disabled={
              saving || !formData.patientId || prescriptionItems.length === 0
            }
          >
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Créer la Prescription
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations générales */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Informations Générales
                </CardTitle>
                <CardDescription>
                  Sélectionnez le patient et ajoutez des instructions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="patient">Patient *</Label>
                  <Select
                    value={formData.patientId}
                    onValueChange={(value) =>
                      handleInputChange("patientId", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un patient" />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {patient.firstName} {patient.lastName}
                            </span>
                            <span className="text-sm text-gray-500">
                              {patient.patientNumber} •{" "}
                              {patient.phone ||
                                patient.email ||
                                "Pas de contact"}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="generalInstructions">
                    Instructions générales
                  </Label>
                  <Textarea
                    id="generalInstructions"
                    placeholder="Instructions générales pour le patient..."
                    value={formData.generalInstructions}
                    onChange={(e) =>
                      handleInputChange("generalInstructions", e.target.value)
                    }
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes du médecin</Label>
                  <Textarea
                    id="notes"
                    placeholder="Notes privées du médecin..."
                    value={formData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="expiresAt">
                    Date d'expiration (optionnel)
                  </Label>
                  <Input
                    id="expiresAt"
                    type="date"
                    value={formData.expiresAt}
                    onChange={(e) =>
                      handleInputChange("expiresAt", e.target.value)
                    }
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Médicaments prescrits */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Pill className="h-5 w-5 mr-2" />
                      Médicaments Prescrits
                    </CardTitle>
                    <CardDescription>
                      Ajoutez les médicaments avec leur posologie
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Dialog
                      open={showMedicationDialog}
                      onOpenChange={setShowMedicationDialog}
                    >
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Médicament interne
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Sélectionner un médicament</DialogTitle>
                          <DialogDescription>
                            Recherchez et sélectionnez un médicament à ajouter à
                            la prescription
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                              placeholder="Rechercher un médicament..."
                              value={medicationSearch}
                              onChange={(e) =>
                                setMedicationSearch(e.target.value)
                              }
                              className="pl-10"
                            />
                          </div>
                          <div className="max-h-96 overflow-y-auto space-y-2">
                            {filteredMedications.map((medication) => (
                              <div
                                key={medication.id}
                                className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                onClick={() => addMedication(medication)}
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="font-medium">
                                      {medication.name}
                                    </p>
                                    {medication.genericName && (
                                      <p className="text-sm text-gray-500">
                                        {medication.genericName}
                                      </p>
                                    )}
                                    <div className="flex items-center space-x-2 mt-1">
                                      <span className="text-xs text-gray-500">
                                        {medication.strength} •{" "}
                                        {medication.form}
                                      </span>
                                      {getCategoryBadge(medication.category)}
                                    </div>
                                  </div>
                                  <Button size="sm">
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                            {filteredMedications.length === 0 && (
                              <div className="text-center py-8">
                                <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-gray-500">
                                  Aucun médicament trouvé
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>

                    {/* Dialog pour médicaments externes */}
                    <Dialog
                      open={showExternalMedicationDialog}
                      onOpenChange={setShowExternalMedicationDialog}
                    >
                      <DialogTrigger asChild>
                        <Button variant="outline">
                          <Plus className="h-4 w-4 mr-2" />
                          Médicament externe
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>
                            Ajouter un médicament externe
                          </DialogTitle>
                          <DialogDescription>
                            Ajoutez un médicament non disponible dans la
                            pharmacie de l'hôpital
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="external-name">
                              Nom du médicament *
                            </Label>
                            <Input
                              id="external-name"
                              placeholder="Ex: Doliprane 1000mg"
                              value={externalMedicationForm.name}
                              onChange={(e) =>
                                setExternalMedicationForm((prev) => ({
                                  ...prev,
                                  name: e.target.value,
                                }))
                              }
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="external-form">Forme</Label>
                              <Select
                                value={externalMedicationForm.form}
                                onValueChange={(value) =>
                                  setExternalMedicationForm((prev) => ({
                                    ...prev,
                                    form: value,
                                  }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Comprimé">
                                    Comprimé
                                  </SelectItem>
                                  <SelectItem value="Gélule">Gélule</SelectItem>
                                  <SelectItem value="Sirop">Sirop</SelectItem>
                                  <SelectItem value="Injection">
                                    Injection
                                  </SelectItem>
                                  <SelectItem value="Pommade">
                                    Pommade
                                  </SelectItem>
                                  <SelectItem value="Gouttes">
                                    Gouttes
                                  </SelectItem>
                                  <SelectItem value="Suppositoire">
                                    Suppositoire
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label htmlFor="external-strength">Dosage</Label>
                              <Input
                                id="external-strength"
                                placeholder="Ex: 500mg"
                                value={externalMedicationForm.strength}
                                onChange={(e) =>
                                  setExternalMedicationForm((prev) => ({
                                    ...prev,
                                    strength: e.target.value,
                                  }))
                                }
                              />
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="external-category">Catégorie</Label>
                            <Select
                              value={externalMedicationForm.category}
                              onValueChange={(value) =>
                                setExternalMedicationForm((prev) => ({
                                  ...prev,
                                  category: value,
                                }))
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="ANTIBIOTIC">
                                  Antibiotique
                                </SelectItem>
                                <SelectItem value="ANALGESIC">
                                  Antalgique
                                </SelectItem>
                                <SelectItem value="ANTI_INFLAMMATORY">
                                  Anti-inflammatoire
                                </SelectItem>
                                <SelectItem value="ANTIHYPERTENSIVE">
                                  Antihypertenseur
                                </SelectItem>
                                <SelectItem value="ANTIDIABETIC">
                                  Antidiabétique
                                </SelectItem>
                                <SelectItem value="VITAMIN">
                                  Vitamine
                                </SelectItem>
                                <SelectItem value="OTHER">Autre</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label htmlFor="external-price">
                              Prix estimé (FCFA) - Optionnel
                            </Label>
                            <Input
                              id="external-price"
                              type="number"
                              placeholder="Ex: 2500"
                              value={
                                externalMedicationForm.estimatedPrice || ""
                              }
                              onChange={(e) =>
                                setExternalMedicationForm((prev) => ({
                                  ...prev,
                                  estimatedPrice: parseInt(e.target.value) || 0,
                                }))
                              }
                            />
                          </div>

                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() =>
                                setShowExternalMedicationDialog(false)
                              }
                            >
                              Annuler
                            </Button>
                            <Button
                              onClick={() => {
                                if (externalMedicationForm.name.trim()) {
                                  addExternalMedication(externalMedicationForm);
                                  setExternalMedicationForm({
                                    name: "",
                                    form: "Comprimé",
                                    strength: "",
                                    category: "OTHER",
                                    estimatedPrice: 0,
                                  });
                                }
                              }}
                              disabled={!externalMedicationForm.name.trim()}
                            >
                              Ajouter
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {prescriptionItems.length === 0 ? (
                  <div className="text-center py-8">
                    <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Aucun médicament ajouté
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Commencez par ajouter des médicaments à cette
                      prescription.
                    </p>
                    <div className="flex space-x-2 justify-center">
                      <Button onClick={() => setShowMedicationDialog(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Médicament interne
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowExternalMedicationDialog(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Médicament externe
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {prescriptionItems.map((item, index) => (
                      <Card
                        key={index}
                        className="border-l-4 border-l-blue-500"
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium text-lg">
                                  {item.isExternal
                                    ? item.externalMedicationName
                                    : item.medication?.name}
                                </h4>
                                {item.isExternal && (
                                  <Badge
                                    variant="outline"
                                    className="bg-orange-50 text-orange-700"
                                  >
                                    Externe
                                  </Badge>
                                )}
                              </div>
                              {!item.isExternal &&
                                item.medication?.genericName && (
                                  <p className="text-sm text-gray-500">
                                    {item.medication.genericName}
                                  </p>
                                )}
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-sm text-gray-500">
                                  {item.isExternal
                                    ? `${item.externalMedicationStrength} • ${item.externalMedicationForm}`
                                    : `${item.medication?.strength} • ${item.medication?.form}`}
                                </span>
                                {item.isExternal
                                  ? getCategoryBadge(
                                      item.externalMedicationCategory || "OTHER"
                                    )
                                  : item.medication &&
                                    getCategoryBadge(item.medication.category)}
                              </div>
                              {item.isExternal && item.estimatedPrice && (
                                <p className="text-sm text-green-600 mt-1">
                                  Prix estimé: {item.estimatedPrice} FCFA
                                </p>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeMedication(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <Label htmlFor={`dosage-${index}`}>Dosage</Label>
                              <Input
                                id={`dosage-${index}`}
                                value={item.dosage}
                                onChange={(e) =>
                                  updatePrescriptionItem(
                                    index,
                                    "dosage",
                                    e.target.value
                                  )
                                }
                                placeholder="Ex: 1 comprimé"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`frequency-${index}`}>
                                Fréquence
                              </Label>
                              <Input
                                id={`frequency-${index}`}
                                value={item.frequency}
                                onChange={(e) =>
                                  updatePrescriptionItem(
                                    index,
                                    "frequency",
                                    e.target.value
                                  )
                                }
                                placeholder="Ex: 3 fois par jour"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`duration-${index}`}>Durée</Label>
                              <Input
                                id={`duration-${index}`}
                                value={item.duration}
                                onChange={(e) =>
                                  updatePrescriptionItem(
                                    index,
                                    "duration",
                                    e.target.value
                                  )
                                }
                                placeholder="Ex: 7 jours"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`quantity-${index}`}>
                                Quantité
                              </Label>
                              <Input
                                id={`quantity-${index}`}
                                type="number"
                                value={item.quantity}
                                onChange={(e) =>
                                  updatePrescriptionItem(
                                    index,
                                    "quantity",
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                min="1"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                              <Label htmlFor={`timing-${index}`}>
                                Moment de prise
                              </Label>
                              <Select
                                value={item.timing || ""}
                                onValueChange={(value) =>
                                  updatePrescriptionItem(index, "timing", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="avant les repas">
                                    Avant les repas
                                  </SelectItem>
                                  <SelectItem value="après les repas">
                                    Après les repas
                                  </SelectItem>
                                  <SelectItem value="pendant les repas">
                                    Pendant les repas
                                  </SelectItem>
                                  <SelectItem value="à jeun">À jeun</SelectItem>
                                  <SelectItem value="au coucher">
                                    Au coucher
                                  </SelectItem>
                                  <SelectItem value="au réveil">
                                    Au réveil
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label htmlFor={`route-${index}`}>
                                Voie d'administration
                              </Label>
                              <Select
                                value={item.route || ""}
                                onValueChange={(value) =>
                                  updatePrescriptionItem(index, "route", value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="orale">Orale</SelectItem>
                                  <SelectItem value="sublinguale">
                                    Sublinguale
                                  </SelectItem>
                                  <SelectItem value="topique">
                                    Topique
                                  </SelectItem>
                                  <SelectItem value="intraveineuse">
                                    Intraveineuse
                                  </SelectItem>
                                  <SelectItem value="intramusculaire">
                                    Intramusculaire
                                  </SelectItem>
                                  <SelectItem value="sous-cutanée">
                                    Sous-cutanée
                                  </SelectItem>
                                  <SelectItem value="rectale">
                                    Rectale
                                  </SelectItem>
                                  <SelectItem value="nasale">Nasale</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="mt-4">
                            <Label htmlFor={`instructions-${index}`}>
                              Instructions spéciales
                            </Label>
                            <Textarea
                              id={`instructions-${index}`}
                              value={item.instructions || ""}
                              onChange={(e) =>
                                updatePrescriptionItem(
                                  index,
                                  "instructions",
                                  e.target.value
                                )
                              }
                              placeholder="Instructions spéciales pour ce médicament..."
                              rows={2}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Actions */}
        <Card>
          <CardContent className="flex items-center justify-between py-4">
            <div className="text-sm text-gray-600">
              {prescriptionItems.length} médicament(s) ajouté(s)
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/dashboard/prescriptions">Annuler</Link>
              </Button>
              <Button
                onClick={handleSave}
                disabled={
                  saving ||
                  !formData.patientId ||
                  prescriptionItems.length === 0
                }
              >
                {saving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Créer la Prescription
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
