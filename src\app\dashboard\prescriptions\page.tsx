"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Printer,
  Calendar,
  User,
  Pill,
  FileText,
  Loader2,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";
import { getPrescriptions } from "@/lib/actions/prescriptions";

// Types
interface Prescription {
  id: string;
  prescriptionNumber: string;
  prescriptionDate: string | Date;
  status: string;
  generalInstructions?: string;
  notes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    patientNumber: string;
    phone?: string;
    email?: string;
  };
  doctor: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  consultation?: {
    id: string;
    consultationDate: string | Date;
    type: string;
  };
  items: Array<{
    id: string;
    dosage: string;
    frequency: string;
    duration: string;
    quantity: number;
    // Support des médicaments externes
    isExternal: boolean;
    externalMedicationName?: string;
    externalMedicationForm?: string;
    externalMedicationStrength?: string;
    externalMedicationCategory?: string;
    estimatedPrice?: number;
    // Médicament interne (peut être null pour les externes)
    medication?: {
      id: string;
      name: string;
      genericName?: string;
      strength?: string;
      form: string;
      category: string;
    } | null;
  }>;
}

export default function PrescriptionsPage() {
  const { data: session } = useSession();
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  // Charger les prescriptions
  const loadPrescriptions = async () => {
    setLoading(true);
    try {
      const result = await getPrescriptions({
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        page: pagination.page,
        limit: pagination.limit,
      });

      if (result.success && result.prescriptions) {
        setPrescriptions(result.prescriptions as any);
        setPagination(result.pagination);
      } else {
        console.error(
          "Erreur lors du chargement des prescriptions:",
          result.error
        );
      }
    } catch (error) {
      console.error("Exception lors du chargement des prescriptions:", error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au montage et lors des changements de filtres
  useEffect(() => {
    loadPrescriptions();
  }, [searchTerm, statusFilter, pagination.page]);

  // Formater la date
  const formatDate = (dateInput: string | Date) => {
    const date =
      typeof dateInput === "string" ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { label: "Active", className: "bg-green-100 text-green-800" },
      COMPLETED: { label: "Terminée", className: "bg-blue-100 text-blue-800" },
      CANCELLED: { label: "Annulée", className: "bg-red-100 text-red-800" },
      EXPIRED: { label: "Expirée", className: "bg-gray-100 text-gray-800" },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    return (
      <Badge variant="secondary" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Obtenir le badge de catégorie de médicament
  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      ANTIBIOTIC: {
        label: "Antibiotique",
        className: "bg-red-50 text-red-700",
      },
      ANALGESIC: { label: "Antalgique", className: "bg-blue-50 text-blue-700" },
      ANTI_INFLAMMATORY: {
        label: "Anti-inflammatoire",
        className: "bg-orange-50 text-orange-700",
      },
      ANTIHYPERTENSIVE: {
        label: "Antihypertenseur",
        className: "bg-purple-50 text-purple-700",
      },
      ANTIDIABETIC: {
        label: "Antidiabétique",
        className: "bg-green-50 text-green-700",
      },
      VITAMIN: { label: "Vitamine", className: "bg-yellow-50 text-yellow-700" },
      OTHER: { label: "Autre", className: "bg-gray-50 text-gray-700" },
    };

    const config =
      categoryConfig[category as keyof typeof categoryConfig] ||
      categoryConfig.OTHER;
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (!session) {
    return <div>Chargement...</div>;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Prescriptions</h1>
            <p className="text-gray-600 mt-2">
              Gestion des prescriptions médicales et ordonnances
            </p>
          </div>
          <Button asChild>
            <Link href="/dashboard/prescriptions/new">
              <Plus className="h-4 w-4 mr-2" />
              Nouvelle Prescription
            </Link>
          </Button>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {pagination.total}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Pill className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Actives</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {prescriptions.filter((p) => p.status === "ACTIVE").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Aujourd'hui
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {
                      prescriptions.filter((p) => {
                        const today = new Date().toDateString();
                        const prescDate =
                          typeof p.prescriptionDate === "string"
                            ? new Date(p.prescriptionDate)
                            : p.prescriptionDate;
                        return prescDate.toDateString() === today;
                      }).length
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <User className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Patients</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(prescriptions.map((p) => p.patient.id)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filtres et recherche */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filtres et Recherche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher par numéro, patient..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">Tous les statuts</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="COMPLETED">Terminée</SelectItem>
                  <SelectItem value="CANCELLED">Annulée</SelectItem>
                  <SelectItem value="EXPIRED">Expirée</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Liste des prescriptions */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Prescriptions</CardTitle>
            <CardDescription>
              {pagination.total} prescription(s) trouvée(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                Chargement des prescriptions...
              </div>
            ) : prescriptions.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucune prescription trouvée
                </h3>
                <p className="text-gray-600 mb-4">
                  Commencez par créer une nouvelle prescription.
                </p>
                <Button asChild>
                  <Link href="/dashboard/prescriptions/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Nouvelle Prescription
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Numéro</TableHead>
                      <TableHead>Patient</TableHead>
                      <TableHead>Médecin</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Médicaments</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {prescriptions.map((prescription) => (
                      <TableRow key={prescription.id}>
                        <TableCell className="font-medium">
                          {prescription.prescriptionNumber}
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">
                              {prescription.patient.firstName}{" "}
                              {prescription.patient.lastName}
                            </p>
                            <p className="text-sm text-gray-500">
                              {prescription.patient.patientNumber}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">
                              Dr. {prescription.doctor.firstName}{" "}
                              {prescription.doctor.lastName}
                            </p>
                            <p className="text-sm text-gray-500">
                              {prescription.doctor.role}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatDate(prescription.prescriptionDate)}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {prescription.items.slice(0, 2).map((item) => (
                              <div
                                key={item.id}
                                className="flex items-center space-x-2"
                              >
                                <span className="text-sm font-medium">
                                  {item.isExternal
                                    ? item.externalMedicationName
                                    : item.medication?.name ||
                                      "Médicament inconnu"}
                                </span>
                                {item.isExternal && (
                                  <Badge
                                    variant="outline"
                                    className="bg-orange-50 text-orange-700"
                                  >
                                    Externe
                                  </Badge>
                                )}
                                {getCategoryBadge(
                                  item.isExternal
                                    ? item.externalMedicationCategory || "OTHER"
                                    : item.medication?.category || "OTHER"
                                )}
                              </div>
                            ))}
                            {prescription.items.length > 2 && (
                              <p className="text-xs text-gray-500">
                                +{prescription.items.length - 2} autre(s)
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(prescription.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/dashboard/prescriptions/${prescription.id}`}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  Voir détails
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/dashboard/prescriptions/${prescription.id}/edit`}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Modifier
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/dashboard/prescriptions/${prescription.id}/print`}
                                >
                                  <Printer className="h-4 w-4 mr-2" />
                                  Imprimer
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Page {pagination.page} sur {pagination.pages} ({pagination.total}{" "}
              résultats)
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                }
                disabled={pagination.page <= 1}
              >
                Précédent
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                }
                disabled={pagination.page >= pagination.pages}
              >
                Suivant
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
