"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  FileText,
  Download,
  Calendar,
  Users,
  DollarSign,
  Activity,
  Building2,
  TestTube,
  UserCheck,
  Plus,
  Eye,
  Filter,
} from "lucide-react";

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState("");
  const [dateRange, setDateRange] = useState("30d");

  // Types de rapports disponibles
  const reportTypes = [
    {
      id: "financial",
      title: "Rapport Financier",
      description: "Revenus, dépenses et analyse financière détaillée",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
      formats: ["PDF", "Excel"],
      frequency: "Mensuel",
    },
    {
      id: "patients",
      title: "Rapport Patients",
      description: "Statistiques des patients, nouveaux enregistrements",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      formats: ["PDF", "Excel"],
      frequency: "Hebdomadaire",
    },
    {
      id: "consultations",
      title: "Rapport Consultations",
      description: "Analyse des consultations et performances médicales",
      icon: Activity,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      formats: ["PDF", "Excel"],
      frequency: "Mensuel",
    },
    {
      id: "hospitalization",
      title: "Rapport Hospitalisations",
      description: "Occupation des lits, admissions et sorties",
      icon: Building2,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      formats: ["PDF", "Excel"],
      frequency: "Mensuel",
    },
    {
      id: "laboratory",
      title: "Rapport Laboratoire",
      description: "Analyses effectuées, résultats et statistiques",
      icon: TestTube,
      color: "text-red-600",
      bgColor: "bg-red-100",
      formats: ["PDF", "Excel"],
      frequency: "Mensuel",
    },
    {
      id: "hr",
      title: "Rapport RH",
      description: "Personnel, présences, congés et performances",
      icon: UserCheck,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
      formats: ["PDF", "Excel"],
      frequency: "Mensuel",
    },
  ];

  // Rapports récemment générés
  const recentReports = [
    {
      id: "1",
      title: "Rapport Financier - Janvier 2025",
      type: "financial",
      generatedAt: "2025-01-26 10:30",
      format: "PDF",
      size: "2.3 MB",
      status: "completed",
    },
    {
      id: "2",
      title: "Rapport Patients - Semaine 4",
      type: "patients",
      generatedAt: "2025-01-25 14:15",
      format: "Excel",
      size: "1.8 MB",
      status: "completed",
    },
    {
      id: "3",
      title: "Rapport Consultations - Janvier 2025",
      type: "consultations",
      generatedAt: "2025-01-24 09:45",
      format: "PDF",
      size: "3.1 MB",
      status: "completed",
    },
    {
      id: "4",
      title: "Rapport RH - Janvier 2025",
      type: "hr",
      generatedAt: "2025-01-23 16:20",
      format: "Excel",
      size: "1.2 MB",
      status: "processing",
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case "processing":
        return <Badge className="bg-yellow-100 text-yellow-800">En cours</Badge>;
      case "failed":
        return <Badge className="bg-red-100 text-red-800">Échec</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleGenerateReport = () => {
    // Logique de génération de rapport
    console.log("Génération du rapport:", selectedReport, dateRange);
    // Ici on appellerait l'API pour générer le rapport
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Rapports & Exports
            </h1>
            <p className="text-gray-600 mt-2">
              Générez et exportez des rapports détaillés en PDF ou Excel
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filtres
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Nouveau Rapport
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Générer un Nouveau Rapport</DialogTitle>
                  <DialogDescription>
                    Sélectionnez le type de rapport et la période
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="report-type">Type de rapport</Label>
                    <Select value={selectedReport} onValueChange={setSelectedReport}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choisir un type de rapport" />
                      </SelectTrigger>
                      <SelectContent>
                        {reportTypes.map((report) => (
                          <SelectItem key={report.id} value={report.id}>
                            {report.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="date-range">Période</Label>
                    <Select value={dateRange} onValueChange={setDateRange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7d">7 derniers jours</SelectItem>
                        <SelectItem value="30d">30 derniers jours</SelectItem>
                        <SelectItem value="90d">3 derniers mois</SelectItem>
                        <SelectItem value="1y">Cette année</SelectItem>
                        <SelectItem value="custom">Période personnalisée</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex space-x-2">
                    <Button onClick={handleGenerateReport} className="flex-1">
                      <FileText className="h-4 w-4 mr-2" />
                      Générer PDF
                    </Button>
                    <Button onClick={handleGenerateReport} variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Générer Excel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Types de rapports */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Types de Rapports Disponibles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {reportTypes.map((report) => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${report.bgColor}`}>
                      <report.icon className={`h-6 w-6 ${report.color}`} />
                    </div>
                    <Badge variant="outline">{report.frequency}</Badge>
                  </div>
                  <CardTitle className="text-lg">{report.title}</CardTitle>
                  <CardDescription>{report.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {report.formats.map((format) => (
                        <Badge key={format} variant="outline" className="text-xs">
                          {format}
                        </Badge>
                      ))}
                    </div>
                    <Button size="sm" variant="outline">
                      <Plus className="h-4 w-4 mr-1" />
                      Générer
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Rapports récents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Rapports Récents
            </CardTitle>
            <CardDescription>
              Historique des rapports générés récemment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentReports.map((report) => (
                <div
                  key={report.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-gray-500" />
                      <div>
                        <p className="font-medium text-gray-900">{report.title}</p>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                          <span>Généré le {report.generatedAt}</span>
                          <span>{report.format}</span>
                          <span>{report.size}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {getStatusBadge(report.status)}
                    {report.status === "completed" && (
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Voir
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Télécharger
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Rapports ce mois</p>
                  <p className="text-2xl font-bold text-gray-900">24</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Taille totale</p>
                  <p className="text-2xl font-bold text-gray-900">45.2 MB</p>
                </div>
                <Download className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Format populaire</p>
                  <p className="text-2xl font-bold text-gray-900">PDF</p>
                </div>
                <FileText className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Temps moyen</p>
                  <p className="text-2xl font-bold text-gray-900">2.3 min</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
