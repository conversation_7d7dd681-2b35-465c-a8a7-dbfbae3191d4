"use client";

import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Shield,
  Users,
  Building,
  Bell,
  Database,
  FileText,
  Lock,
  Globe,
  Palette,
  Mail,
  Smartphone,
} from "lucide-react";
import Link from "next/link";

const SETTINGS_SECTIONS = [
  {
    title: "Sécurité & Permissions",
    description: "G<PERSON><PERSON> les rôles, permissions et sécurité",
    icon: Shield,
    color: "bg-red-100 text-red-600",
    items: [
      {
        title: "Rôles & Permissions",
        description: "Configurez les permissions par rôle",
        href: "/dashboard/settings/permissions",
        icon: Lock,
      },
      {
        title: "Utilisateurs",
        description: "Gérez les comptes utilisateurs",
        href: "/dashboard/settings/users",
        icon: Users,
      },
      {
        title: "Sessions",
        description: "Surveillez les sessions actives",
        href: "/dashboard/settings/sessions",
        icon: Globe,
      },
    ],
  },
  {
    title: "Organisation",
    description: "Paramètres de votre établissement",
    icon: Building,
    color: "bg-blue-100 text-blue-600",
    items: [
      {
        title: "Informations Générales",
        description: "Nom, adresse, contact de l'hôpital",
        href: "/dashboard/settings/organization",
        icon: Building,
      },
      {
        title: "Départements",
        description: "Gérez les services médicaux",
        href: "/dashboard/personnel/departments",
        icon: Building,
      },
      {
        title: "Tarification",
        description: "Prix des consultations et services",
        href: "/dashboard/settings/pricing",
        icon: FileText,
      },
    ],
  },
  {
    title: "Notifications",
    description: "Configurez les alertes et notifications",
    icon: Bell,
    color: "bg-yellow-100 text-yellow-600",
    items: [
      {
        title: "Notifications Email",
        description: "Paramètres des emails automatiques",
        href: "/dashboard/settings/notifications/email",
        icon: Mail,
      },
      {
        title: "Notifications SMS",
        description: "Alertes par SMS pour les patients",
        href: "/dashboard/settings/notifications/sms",
        icon: Smartphone,
      },
      {
        title: "Rappels",
        description: "Rappels de rendez-vous automatiques",
        href: "/dashboard/settings/notifications/reminders",
        icon: Bell,
      },
    ],
  },
  {
    title: "Système",
    description: "Configuration technique et maintenance",
    icon: Database,
    color: "bg-green-100 text-green-600",
    items: [
      {
        title: "Base de Données",
        description: "Sauvegarde et maintenance",
        href: "/dashboard/settings/database",
        icon: Database,
      },
      {
        title: "Logs & Audit",
        description: "Journaux d'activité système",
        href: "/dashboard/settings/audit",
        icon: FileText,
      },
      {
        title: "Intégrations",
        description: "APIs et services externes",
        href: "/dashboard/settings/integrations",
        icon: Globe,
      },
    ],
  },
  {
    title: "Interface",
    description: "Personnalisation de l'interface",
    icon: Palette,
    color: "bg-purple-100 text-purple-600",
    items: [
      {
        title: "Thème & Couleurs",
        description: "Personnalisez l'apparence",
        href: "/dashboard/settings/theme",
        icon: Palette,
      },
      {
        title: "Langue",
        description: "Langue de l'interface",
        href: "/dashboard/settings/language",
        icon: Globe,
      },
      {
        title: "Préférences",
        description: "Paramètres utilisateur par défaut",
        href: "/dashboard/settings/preferences",
        icon: Settings,
      },
    ],
  },
];

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Paramètres & Configuration
            </h1>
            <p className="text-gray-600 mt-2">
              Configurez votre système hospitalier selon vos besoins
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Documentation
            </Button>
            <Button>
              <Database className="h-4 w-4 mr-2" />
              Sauvegarde
            </Button>
          </div>
        </div>

        {/* Sections de paramètres */}
        <div className="space-y-8">
          {SETTINGS_SECTIONS.map((section, sectionIndex) => (
            <Card key={sectionIndex}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <div className={`p-2 rounded-lg mr-3 ${section.color}`}>
                    <section.icon className="h-5 w-5" />
                  </div>
                  {section.title}
                </CardTitle>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {section.items.map((item, itemIndex) => (
                    <Link key={itemIndex} href={item.href}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 bg-gray-100 rounded-lg">
                              <item.icon className="h-4 w-4 text-gray-600" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900 mb-1">
                                {item.title}
                              </h3>
                              <p className="text-sm text-gray-600">
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Actions rapides */}
        <Card>
          <CardHeader>
            <CardTitle>Actions Rapides</CardTitle>
            <CardDescription>
              Raccourcis vers les tâches de maintenance courantes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex-col">
                <Database className="h-6 w-6 mb-2" />
                Sauvegarde DB
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Users className="h-6 w-6 mb-2" />
                Créer Utilisateur
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <FileText className="h-6 w-6 mb-2" />
                Export Données
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Shield className="h-6 w-6 mb-2" />
                Audit Sécurité
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Informations système */}
        <Card>
          <CardHeader>
            <CardTitle>Informations Système</CardTitle>
            <CardDescription>
              État actuel du système GlobalCare Solutions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">✓</div>
                <p className="text-sm font-medium text-gray-900 mt-1">
                  Système Opérationnel
                </p>
                <p className="text-xs text-gray-600">Tous les services actifs</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">v1.0.0</div>
                <p className="text-sm font-medium text-gray-900 mt-1">
                  Version Actuelle
                </p>
                <p className="text-xs text-gray-600">Dernière mise à jour</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">99.9%</div>
                <p className="text-sm font-medium text-gray-900 mt-1">
                  Disponibilité
                </p>
                <p className="text-xs text-gray-600">30 derniers jours</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
