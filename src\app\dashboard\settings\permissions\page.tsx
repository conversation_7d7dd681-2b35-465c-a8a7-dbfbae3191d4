"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { DashboardLayout } from "@/components/dashboard/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Shield,
  Users,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Check,
  X,
  Lock,
  Unlock,
} from "lucide-react";
import Link from "next/link";
import {
  getAllPermissions,
  getRolePermissions,
  updateRolePermission,
} from "@/lib/actions/permissions";
import { AdminOnly } from "@/components/auth/permission-guard";

const ROLES = [
  {
    value: "SUPER_ADMIN",
    label: "Super Administrateur",
    color: "bg-red-100 text-red-800",
  },
  {
    value: "ADMIN",
    label: "Administrateur",
    color: "bg-purple-100 text-purple-800",
  },
  { value: "DOCTOR", label: "Médecin", color: "bg-blue-100 text-blue-800" },
  {
    value: "NURSE",
    label: "Infirmier(ère)",
    color: "bg-green-100 text-green-800",
  },
  {
    value: "PHARMACIST",
    label: "Pharmacien",
    color: "bg-orange-100 text-orange-800",
  },
  {
    value: "RECEPTIONIST",
    label: "Réceptionniste",
    color: "bg-pink-100 text-pink-800",
  },
  {
    value: "TECHNICIAN",
    label: "Technicien",
    color: "bg-gray-100 text-gray-800",
  },
  {
    value: "LAB_TECHNICIAN",
    label: "Technicien Labo",
    color: "bg-cyan-100 text-cyan-800",
  },
  {
    value: "ACCOUNTANT",
    label: "Comptable",
    color: "bg-yellow-100 text-yellow-800",
  },
];

const RESOURCES = [
  { value: "PATIENTS", label: "Patients", icon: Users },
  { value: "CONSULTATIONS", label: "Consultations", icon: Users },
  { value: "PRESCRIPTIONS", label: "Prescriptions", icon: Users },
  { value: "MEDICATIONS", label: "Médicaments", icon: Users },
  { value: "PHARMACY", label: "Pharmacie", icon: Users },
  { value: "LABORATORY", label: "Laboratoire", icon: Users },
  { value: "HOSPITALIZATION", label: "Hospitalisation", icon: Users },
  { value: "EMPLOYEES", label: "Employés", icon: Users },
  { value: "DEPARTMENTS", label: "Départements", icon: Users },
  { value: "PAYMENTS", label: "Paiements", icon: Users },
  { value: "REPORTS", label: "Rapports", icon: Users },
  { value: "ANALYTICS", label: "Analytics", icon: Users },
  { value: "SETTINGS", label: "Paramètres", icon: Settings },
  { value: "USERS", label: "Utilisateurs", icon: Users },
  { value: "ROLES", label: "Rôles", icon: Shield },
];

const ACTIONS = [
  { value: "CREATE", label: "Créer", color: "bg-green-100 text-green-800" },
  { value: "READ", label: "Lire", color: "bg-blue-100 text-blue-800" },
  {
    value: "UPDATE",
    label: "Modifier",
    color: "bg-yellow-100 text-yellow-800",
  },
  { value: "DELETE", label: "Supprimer", color: "bg-red-100 text-red-800" },
  {
    value: "APPROVE",
    label: "Approuver",
    color: "bg-purple-100 text-purple-800",
  },
  { value: "REJECT", label: "Rejeter", color: "bg-red-100 text-red-800" },
  {
    value: "EXPORT",
    label: "Exporter",
    color: "bg-indigo-100 text-indigo-800",
  },
  { value: "PRINT", label: "Imprimer", color: "bg-gray-100 text-gray-800" },
  { value: "MANAGE", label: "Gérer", color: "bg-orange-100 text-orange-800" },
];

export default function PermissionsPage() {
  const [selectedRole, setSelectedRole] = useState("ADMIN");
  const [permissions, setPermissions] = useState<any[]>([]);
  const [rolePermissions, setRolePermissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Charger les données
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        const [permissionsResult, rolePermissionsResult] = await Promise.all([
          getAllPermissions(),
          getRolePermissions(selectedRole as any),
        ]);

        if (permissionsResult.success) {
          setPermissions(permissionsResult.permissions || []);
        } else {
          toast.error("Erreur lors du chargement des permissions");
        }

        if (rolePermissionsResult.success) {
          setRolePermissions(rolePermissionsResult.rolePermissions || []);
        } else {
          toast.error("Erreur lors du chargement des permissions du rôle");
        }
      } catch (error) {
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [selectedRole]);

  const handlePermissionToggle = async (
    permissionId: string,
    isGranted: boolean
  ) => {
    try {
      const result = await updateRolePermission(
        selectedRole as any,
        permissionId,
        isGranted
      );

      if (result.success) {
        toast.success("Permission mise à jour avec succès");
        // Recharger les permissions du rôle
        const rolePermissionsResult = await getRolePermissions(
          selectedRole as any
        );
        if (rolePermissionsResult.success) {
          setRolePermissions(rolePermissionsResult.rolePermissions || []);
        }
      } else {
        toast.error("Erreur lors de la mise à jour de la permission");
      }
    } catch (error) {
      toast.error("Erreur lors de la mise à jour");
    }
  };

  const getPermissionStatus = (permissionId: string) => {
    const rolePermission = rolePermissions.find(
      (rp) => rp.permissionId === permissionId
    );
    return rolePermission?.isGranted || false;
  };

  const getActionBadge = (action: string) => {
    const actionConfig = ACTIONS.find((a) => a.value === action);
    return (
      <Badge className={actionConfig?.color || "bg-gray-100 text-gray-800"}>
        {actionConfig?.label || action}
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = ROLES.find((r) => r.value === role);
    return (
      <Badge className={roleConfig?.color || "bg-gray-100 text-gray-800"}>
        {roleConfig?.label || role}
      </Badge>
    );
  };

  // Grouper les permissions par ressource
  const permissionsByResource = permissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = [];
    }
    acc[permission.resource].push(permission);
    return acc;
  }, {} as Record<string, any[]>);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Chargement des permissions...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <AdminOnly
        fallback={
          <div className="p-6 text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Accès Restreint
            </h2>
            <p className="text-gray-600">
              Seuls les administrateurs peuvent accéder à cette page.
            </p>
          </div>
        }
      >
        <div className="p-6 space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Gestion des Permissions
              </h1>
              <p className="text-gray-600 mt-2">
                Configurez les permissions par rôle pour sécuriser l'accès aux
                fonctionnalités
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/dashboard/settings">← Retour aux Paramètres</Link>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="roles" className="space-y-6">
            <TabsList>
              <TabsTrigger value="roles">Permissions par Rôle</TabsTrigger>
              <TabsTrigger value="users">Permissions Utilisateurs</TabsTrigger>
              <TabsTrigger value="audit">Journal d'Audit</TabsTrigger>
            </TabsList>

            <TabsContent value="roles" className="space-y-6">
              {/* Sélection du rôle */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="h-5 w-5 mr-2" />
                    Sélection du Rôle
                  </CardTitle>
                  <CardDescription>
                    Choisissez un rôle pour configurer ses permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <Select
                      value={selectedRole}
                      onValueChange={setSelectedRole}
                    >
                      <SelectTrigger className="w-64">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {ROLES.map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {getRoleBadge(selectedRole)}
                  </div>
                </CardContent>
              </Card>

              {/* Matrice des permissions */}
              <Card>
                <CardHeader>
                  <CardTitle>
                    Permissions pour{" "}
                    {ROLES.find((r) => r.value === selectedRole)?.label}
                  </CardTitle>
                  <CardDescription>
                    Activez ou désactivez les permissions pour ce rôle
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {RESOURCES.map((resource) => {
                      const resourcePermissions =
                        permissionsByResource[resource.value] || [];

                      if (resourcePermissions.length === 0) return null;

                      return (
                        <div
                          key={resource.value}
                          className="border rounded-lg p-4"
                        >
                          <h3 className="font-medium text-lg mb-4 flex items-center">
                            <resource.icon className="h-5 w-5 mr-2" />
                            {resource.label}
                          </h3>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {resourcePermissions.map((permission) => (
                              <div
                                key={permission.id}
                                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                              >
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2">
                                    {getActionBadge(permission.action)}
                                  </div>
                                  <p className="text-sm text-gray-600 mt-1">
                                    {permission.description}
                                  </p>
                                </div>
                                <Switch
                                  checked={getPermissionStatus(permission.id)}
                                  onCheckedChange={(checked) =>
                                    handlePermissionToggle(
                                      permission.id,
                                      checked
                                    )
                                  }
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Permissions Spécifiques par Utilisateur</CardTitle>
                  <CardDescription>
                    Accordez ou retirez des permissions spécifiques à des
                    utilisateurs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">
                      Fonctionnalité à implémenter
                    </p>
                    <p className="text-sm text-gray-400">
                      Gestion des permissions individuelles par utilisateur
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="audit" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Journal d'Audit des Permissions</CardTitle>
                  <CardDescription>
                    Historique des modifications de permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">
                      Journal d'audit à implémenter
                    </p>
                    <p className="text-sm text-gray-400">
                      Suivi des modifications de permissions et accès
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminOnly>
    </DashboardLayout>
  );
}
