@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Montserrat, sans-serif;
  --font-mono: Source Code Pro, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: Playfair Display, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.5rem;
  --background: oklch(0.9821 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.5676 0.2021 283.0838);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.8214 0.072 249.3482);
  --secondary-foreground: oklch(0.3211 0 0);
  --muted: oklch(0.8202 0.0213 91.6163);
  --muted-foreground: oklch(0.5382 0 0);
  --accent: oklch(0.6475 0.0642 117.426);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --border: oklch(0.8699 0 0);
  --input: oklch(0.8699 0 0);
  --ring: oklch(0.5676 0.2021 283.0838);
  --chart-1: oklch(0.5676 0.2021 283.0838);
  --chart-2: oklch(0.5261 0.1705 314.6534);
  --chart-3: oklch(0.339 0.1793 301.6848);
  --chart-4: oklch(0.6746 0.1414 261.338);
  --chart-5: oklch(0.588 0.0993 245.7394);
  --sidebar: oklch(0.9821 0 0);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.5676 0.2021 283.0838);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.6475 0.0642 117.426);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.8699 0 0);
  --sidebar-ring: oklch(0.5676 0.2021 283.0838);
  --destructive-foreground: oklch(1 0 0);
  --font-sans: Montserrat, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Source Code Pro, monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.1;
  --shadow-blur: 10px;
  --shadow-spread: -2px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 5px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 1px 2px -3px hsl(0 0% 0% / 0.1);
  --shadow: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 1px 2px -3px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 2px 4px -3px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 4px 6px -3px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 8px 10px -3px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.2303 0.0125 264.2926);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.321 0.0078 223.6661);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.321 0.0078 223.6661);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.5676 0.2021 283.0838);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.339 0.1793 301.6848);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.3867 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.6746 0.1414 261.338);
  --accent-foreground: oklch(0.9219 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --border: oklch(0.3867 0 0);
  --input: oklch(0.3867 0 0);
  --ring: oklch(0.5676 0.2021 283.0838);
  --chart-1: oklch(0.5676 0.2021 283.0838);
  --chart-2: oklch(0.5261 0.1705 314.6534);
  --chart-3: oklch(0.339 0.1793 301.6848);
  --chart-4: oklch(0.6746 0.1414 261.338);
  --chart-5: oklch(0.588 0.0993 245.7394);
  --sidebar: oklch(0.2303 0.0125 264.2926);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.5676 0.2021 283.0838);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.6746 0.1414 261.338);
  --sidebar-accent-foreground: oklch(0.9219 0 0);
  --sidebar-border: oklch(0.3867 0 0);
  --sidebar-ring: oklch(0.5676 0.2021 283.0838);
  --destructive-foreground: oklch(1 0 0);
  --radius: 0.5rem;
  --font-sans: Montserrat, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Source Code Pro, monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.1;
  --shadow-blur: 10px;
  --shadow-spread: -2px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 5px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 1px 2px -3px hsl(0 0% 0% / 0.1);
  --shadow: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 1px 2px -3px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 2px 4px -3px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 4px 6px -3px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.1),
    0px 8px 10px -3px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-white text-foreground;
    letter-spacing: var(--tracking-normal);
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Scrollbar personnalisée */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Sélection de texte */
  ::selection {
    @apply bg-blue-200 text-blue-900;
  }
}

/* Composants personnalisés */
@layer components {
  /* Cartes avec effet moderne */
  .glass-card {
    @apply bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg;
  }

  /* Animations personnalisées */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Effets de survol pour les cartes */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1 hover:scale-[1.02];
  }

  /* Ombres personnalisées */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
  }

  .shadow-glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
  }

  /* Indicateurs de statut */
  .status-indicator {
    @apply w-3 h-3 rounded-full animate-pulse;
  }

  .status-online {
    @apply bg-green-400 shadow-glow-green;
  }

  .status-offline {
    @apply bg-red-400 shadow-glow-red;
  }

  .status-busy {
    @apply bg-yellow-400;
  }
}

/* Animations keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utilitaires */
@layer utilities {
  /* Masquage du scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Troncature de texte avancée */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
