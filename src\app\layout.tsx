import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { ClientSessionProvider } from "@/components/providers/session-provider";
import { Toaster } from "sonner";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "GlobalCare Solutions - Gestion Hospitalière",
  description:
    "Plateforme SaaS révolutionnaire pour la gestion des hôpitaux et cliniques en Afrique",
  keywords:
    "hôpital, clinique, gestion médicale, SaaS, Afrique, Mali, patients, consultations",
  authors: [{ name: "GlobalCare Solutions" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className="h-full">
      <body className={`${inter.className} h-full antialiased bg-gray-50`}>
        <ClientSessionProvider>{children}</ClientSessionProvider>
        <Toaster position="top-right" richColors />
      </body>
    </html>
  );
}
