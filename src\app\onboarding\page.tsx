"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Heart,
  Building2,
  User,
  CreditCard,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Loader2,
} from "lucide-react";
import {
  createOrganizationWithAdmin,
  OnboardingData,
} from "@/lib/actions/onboarding";
import { validateOnboardingData } from "@/lib/validation";

export default function OnboardingPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const [formData, setFormData] = useState<OnboardingData>({
    // Organisation
    organizationName: "",
    organizationType: "hospital",
    organizationEmail: "",
    organizationPhone: "",
    organizationAddress: "",
    organizationCity: "",
    organizationCountry: "Mali",

    // Administrateur
    adminFirstName: "",
    adminLastName: "",
    adminEmail: "",
    adminPhone: "",
    adminPassword: "",

    // Plan
    subscriptionPlan: "basic",
  });

  const steps = [
    { id: 1, title: "Organisation", icon: Building2 },
    { id: 2, title: "Administrateur", icon: User },
    { id: 3, title: "Plan d'abonnement", icon: CreditCard },
    { id: 4, title: "Confirmation", icon: CheckCircle },
  ];

  const plans = [
    {
      id: "basic",
      name: "Basic",
      price: "50,000 FCFA/mois",
      features: [
        "10 utilisateurs",
        "1,000 patients",
        "5 GB stockage",
        "Support email",
      ],
    },
    {
      id: "premium",
      name: "Premium",
      price: "150,000 FCFA/mois",
      features: [
        "50 utilisateurs",
        "5,000 patients",
        "25 GB stockage",
        "Support prioritaire",
        "Télémédecine",
      ],
    },
    {
      id: "enterprise",
      name: "Enterprise",
      price: "Sur devis",
      features: [
        "200+ utilisateurs",
        "20,000+ patients",
        "100+ GB stockage",
        "Support dédié",
        "Intégrations avancées",
      ],
    },
  ];

  const handleInputChange = (field: keyof OnboardingData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setErrors([]); // Clear errors when user types
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setErrors([]);

    // Valider les données
    const validationErrors = validateOnboardingData(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      setIsLoading(false);
      return;
    }

    try {
      const result = await createOrganizationWithAdmin(formData);

      if (result.success) {
        // Rediriger vers la page de connexion avec un message de succès
        router.push(
          "/auth/signin?message=Organisation créée avec succès. Vous pouvez maintenant vous connecter."
        );
      } else {
        setErrors([result.error || "Erreur lors de la création"]);
      }
    } catch (error) {
      setErrors(["Erreur inattendue lors de la création"]);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="organizationName">
                  Nom de l'organisation *
                </Label>
                <Input
                  id="organizationName"
                  value={formData.organizationName}
                  onChange={(e) =>
                    handleInputChange("organizationName", e.target.value)
                  }
                  placeholder="Hôpital Central de Bamako"
                />
              </div>
              <div>
                <Label htmlFor="organizationType">Type d'établissement *</Label>
                <Select
                  value={formData.organizationType}
                  onValueChange={(value: any) =>
                    handleInputChange("organizationType", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hospital">Hôpital</SelectItem>
                    <SelectItem value="clinic">Clinique</SelectItem>
                    <SelectItem value="medical_center">
                      Centre médical
                    </SelectItem>
                    <SelectItem value="pharmacy">Pharmacie</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="organizationEmail">Email *</Label>
                <Input
                  id="organizationEmail"
                  type="email"
                  value={formData.organizationEmail}
                  onChange={(e) =>
                    handleInputChange("organizationEmail", e.target.value)
                  }
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="organizationPhone">Téléphone *</Label>
                <Input
                  id="organizationPhone"
                  value={formData.organizationPhone}
                  onChange={(e) =>
                    handleInputChange("organizationPhone", e.target.value)
                  }
                  placeholder="+223 20 22 27 12"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="organizationAddress">Adresse *</Label>
              <Input
                id="organizationAddress"
                value={formData.organizationAddress}
                onChange={(e) =>
                  handleInputChange("organizationAddress", e.target.value)
                }
                placeholder="Avenue de l'Indépendance, Quartier du Fleuve"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="organizationCity">Ville *</Label>
                <Input
                  id="organizationCity"
                  value={formData.organizationCity}
                  onChange={(e) =>
                    handleInputChange("organizationCity", e.target.value)
                  }
                  placeholder="Bamako"
                />
              </div>
              <div>
                <Label htmlFor="organizationCountry">Pays *</Label>
                <Select
                  value={formData.organizationCountry}
                  onValueChange={(value) =>
                    handleInputChange("organizationCountry", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Mali">Mali</SelectItem>
                    <SelectItem value="Sénégal">Sénégal</SelectItem>
                    <SelectItem value="Burkina Faso">Burkina Faso</SelectItem>
                    <SelectItem value="Côte d'Ivoire">Côte d'Ivoire</SelectItem>
                    <SelectItem value="Niger">Niger</SelectItem>
                    <SelectItem value="Guinée">Guinée</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="adminFirstName">Prénom *</Label>
                <Input
                  id="adminFirstName"
                  value={formData.adminFirstName}
                  onChange={(e) =>
                    handleInputChange("adminFirstName", e.target.value)
                  }
                  placeholder="Amadou"
                />
              </div>
              <div>
                <Label htmlFor="adminLastName">Nom *</Label>
                <Input
                  id="adminLastName"
                  value={formData.adminLastName}
                  onChange={(e) =>
                    handleInputChange("adminLastName", e.target.value)
                  }
                  placeholder="Diarra"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="adminEmail">Email *</Label>
                <Input
                  id="adminEmail"
                  type="email"
                  value={formData.adminEmail}
                  onChange={(e) =>
                    handleInputChange("adminEmail", e.target.value)
                  }
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="adminPhone">Téléphone</Label>
                <Input
                  id="adminPhone"
                  value={formData.adminPhone}
                  onChange={(e) =>
                    handleInputChange("adminPhone", e.target.value)
                  }
                  placeholder="+223 70 12 34 56"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="adminPassword">Mot de passe *</Label>
              <Input
                id="adminPassword"
                type="password"
                value={formData.adminPassword}
                onChange={(e) =>
                  handleInputChange("adminPassword", e.target.value)
                }
                placeholder="Minimum 8 caractères"
              />
              <p className="text-sm text-gray-500 mt-1">
                Le mot de passe doit contenir au moins 8 caractères
              </p>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan) => (
                <Card
                  key={plan.id}
                  className={`cursor-pointer transition-all ${
                    formData.subscriptionPlan === plan.id
                      ? "ring-2 ring-blue-600 border-blue-600"
                      : "hover:shadow-lg"
                  }`}
                  onClick={() => handleInputChange("subscriptionPlan", plan.id)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {plan.name}
                      {formData.subscriptionPlan === plan.id && (
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                      )}
                    </CardTitle>
                    <CardDescription className="text-lg font-semibold">
                      {plan.price}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Période d'essai gratuite :</strong> Tous les plans
                incluent 30 jours d'essai gratuit. Aucune carte de crédit
                requise pour commencer.
              </p>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Prêt à créer votre organisation
              </h3>
              <p className="text-gray-600">
                Vérifiez les informations ci-dessous avant de finaliser
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Organisation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p>
                    <strong>Nom :</strong> {formData.organizationName}
                  </p>
                  <p>
                    <strong>Type :</strong> {formData.organizationType}
                  </p>
                  <p>
                    <strong>Email :</strong> {formData.organizationEmail}
                  </p>
                  <p>
                    <strong>Ville :</strong> {formData.organizationCity},{" "}
                    {formData.organizationCountry}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Administrateur</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <p>
                    <strong>Nom :</strong> {formData.adminFirstName}{" "}
                    {formData.adminLastName}
                  </p>
                  <p>
                    <strong>Email :</strong> {formData.adminEmail}
                  </p>
                  <p>
                    <strong>Plan :</strong>{" "}
                    {
                      plans.find((p) => p.id === formData.subscriptionPlan)
                        ?.name
                    }
                  </p>
                </CardContent>
              </Card>
            </div>

            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <ul className="text-red-800 text-sm space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-2">
            <Heart className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">GlobalCare</span>
            <Badge variant="secondary">Onboarding</Badge>
          </div>
        </div>
      </header>

      {/* Progress Steps */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between max-w-2xl mx-auto">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    currentStep >= step.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  <step.icon className="h-5 w-5" />
                </div>
                <span
                  className={`ml-2 text-sm font-medium ${
                    currentStep >= step.id ? "text-blue-600" : "text-gray-500"
                  }`}
                >
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-0.5 mx-4 ${
                      currentStep > step.id ? "bg-blue-600" : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">
                {steps.find((s) => s.id === currentStep)?.title}
              </CardTitle>
              <CardDescription>
                {currentStep === 1 &&
                  "Informations sur votre établissement de santé"}
                {currentStep === 2 &&
                  "Créez le compte administrateur principal"}
                {currentStep === 3 && "Choisissez votre plan d'abonnement"}
                {currentStep === 4 && "Confirmez et créez votre organisation"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderStep()}

              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Précédent
                </Button>

                {currentStep < 4 ? (
                  <Button onClick={handleNext}>
                    Suivant
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Création en cours...
                      </>
                    ) : (
                      <>
                        Créer l'organisation
                        <CheckCircle className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
