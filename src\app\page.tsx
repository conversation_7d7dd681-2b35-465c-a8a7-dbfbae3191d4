import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Heart,
  Users,
  Calendar,
  FileText,
  Shield,
  Smartphone,
  TrendingUp,
  ArrowRight,
} from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Heart className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">GlobalCare</span>
            <Badge variant="secondary" className="ml-2">
              SaaS
            </Badge>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              href="#features"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Fonctionnalités
            </Link>
            <Link
              href="#pricing"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Tarifs
            </Link>
            <Link
              href="#about"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              À propos
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/auth/signin">Connexion</Link>
            </Button>
            <Button asChild>
              <Link href="/onboarding">Créer mon organisation</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge variant="outline" className="mb-4">
            🚀 Révolutionnez votre gestion hospitalière
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            La plateforme SaaS pour les
            <span className="text-blue-600 block">hôpitaux africains</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            GlobalCare Solutions transforme la gestion des hôpitaux et cliniques
            en Afrique avec une plateforme moderne, sécurisée et adaptée aux
            réalités locales.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/onboarding">
                Créer mon organisation
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6"
              asChild
            >
              <Link href="/auth/signin">Se connecter</Link>
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            ✅ Module Patients opérationnel • ✅ Interface moderne et intuitive
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Module Gestion des Patients
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Première étape de notre plateforme révolutionnaire
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Users className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>Gestion des Patients</CardTitle>
                <CardDescription>
                  Dossiers médicaux électroniques complets avec historique
                  détaillé et recherche avancée
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Calendar className="h-12 w-12 text-green-600 mb-4" />
                <CardTitle>Consultations</CardTitle>
                <CardDescription>
                  Gestion complète des consultations avec suivi des diagnostics
                  et traitements
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <FileText className="h-12 w-12 text-purple-600 mb-4" />
                <CardTitle>Prescriptions</CardTitle>
                <CardDescription>
                  Ordonnances électroniques sécurisées avec suivi des
                  traitements et posologies
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Shield className="h-12 w-12 text-red-600 mb-4" />
                <CardTitle>Multi-tenant SaaS</CardTitle>
                <CardDescription>
                  Architecture sécurisée permettant à plusieurs organisations de
                  coexister
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Smartphone className="h-12 w-12 text-orange-600 mb-4" />
                <CardTitle>Interface Moderne</CardTitle>
                <CardDescription>
                  UI/UX révolutionnaire avec shadcn/ui et Tailwind CSS pour une
                  expérience optimale
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <TrendingUp className="h-12 w-12 text-indigo-600 mb-4" />
                <CardTitle>Server Actions</CardTitle>
                <CardDescription>
                  Actions serveur Next.js pour des performances optimales et une
                  sécurité renforcée
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-blue-600 text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold mb-4">
            Découvrez le module Patients
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Première pierre de notre plateforme révolutionnaire pour la santé en
            Afrique
          </p>
          <Button
            size="lg"
            variant="secondary"
            className="text-lg px-8 py-6"
            asChild
          >
            <Link href="/dashboard">
              Accéder au Dashboard
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900 text-white">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Heart className="h-6 w-6 text-blue-400" />
            <span className="text-xl font-bold">GlobalCare Solutions</span>
          </div>
          <p className="text-gray-400 mb-4">
            Révolutionner la santé en Afrique grâce à la technologie
          </p>
          <p className="text-sm text-gray-500">
            &copy; 2024 GlobalCare Solutions. Module Patients - Version 1.0
          </p>
        </div>
      </footer>
    </div>
  );
}
