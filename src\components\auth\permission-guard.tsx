"use client";

import { useSession } from "next-auth/react";
import { ReactNode } from "react";
import { UserRole } from "@prisma/client";

interface PermissionGuardProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
  requiredRole?: UserRole;
  fallback?: ReactNode;
  requireAuth?: boolean;
}

const ROLE_HIERARCHY: UserRole[] = [
  "RECEPTIONIST",
  "TECHNICIAN",
  "LAB_TECHNICIAN", 
  "NURSE",
  "PHARMACIST",
  "ACCOUNTANT",
  "DOCTOR",
  "ADMIN",
  "SUPER_ADMIN",
];

export function PermissionGuard({
  children,
  allowedRoles,
  requiredRole,
  fallback = null,
  requireAuth = true,
}: PermissionGuardProps) {
  const { data: session, status } = useSession();

  // Si l'authentification est en cours
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Si l'authentification est requise mais l'utilisateur n'est pas connecté
  if (requireAuth && !session?.user) {
    return fallback || (
      <div className="text-center p-4">
        <p className="text-gray-500">Connexion requise</p>
      </div>
    );
  }

  // Si pas d'authentification requise et pas de session
  if (!requireAuth && !session?.user) {
    return <>{children}</>;
  }

  const userRole = session?.user?.role as UserRole;

  // Vérification par rôles autorisés
  if (allowedRoles && allowedRoles.length > 0) {
    if (!allowedRoles.includes(userRole)) {
      return fallback || (
        <div className="text-center p-4">
          <p className="text-gray-500">Permissions insuffisantes</p>
        </div>
      );
    }
  }

  // Vérification par rôle minimum requis (hiérarchique)
  if (requiredRole) {
    const userRoleIndex = ROLE_HIERARCHY.indexOf(userRole);
    const requiredRoleIndex = ROLE_HIERARCHY.indexOf(requiredRole);

    if (userRoleIndex < requiredRoleIndex) {
      return fallback || (
        <div className="text-center p-4">
          <p className="text-gray-500">Niveau d'autorisation insuffisant</p>
        </div>
      );
    }
  }

  return <>{children}</>;
}

// Hook pour vérifier les permissions
export function usePermissions() {
  const { data: session } = useSession();

  const hasRole = (role: UserRole): boolean => {
    return session?.user?.role === role;
  };

  const hasMinimumRole = (minimumRole: UserRole): boolean => {
    if (!session?.user?.role) return false;

    const userRoleIndex = ROLE_HIERARCHY.indexOf(session.user.role as UserRole);
    const minimumRoleIndex = ROLE_HIERARCHY.indexOf(minimumRole);

    return userRoleIndex >= minimumRoleIndex;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    if (!session?.user?.role) return false;
    return roles.includes(session.user.role as UserRole);
  };

  const isAdmin = (): boolean => {
    return hasAnyRole(["ADMIN", "SUPER_ADMIN"]);
  };

  const isSuperAdmin = (): boolean => {
    return hasRole("SUPER_ADMIN");
  };

  const isDoctor = (): boolean => {
    return hasRole("DOCTOR");
  };

  const isNurse = (): boolean => {
    return hasRole("NURSE");
  };

  const isPharmacist = (): boolean => {
    return hasRole("PHARMACIST");
  };

  const canManageUsers = (): boolean => {
    return hasAnyRole(["ADMIN", "SUPER_ADMIN"]);
  };

  const canManageSettings = (): boolean => {
    return hasAnyRole(["ADMIN", "SUPER_ADMIN"]);
  };

  const canViewReports = (): boolean => {
    return hasMinimumRole("ACCOUNTANT");
  };

  const canManagePharmacy = (): boolean => {
    return hasAnyRole(["PHARMACIST", "ADMIN", "SUPER_ADMIN"]);
  };

  const canManageLaboratory = (): boolean => {
    return hasAnyRole(["LAB_TECHNICIAN", "DOCTOR", "ADMIN", "SUPER_ADMIN"]);
  };

  const canCreateConsultations = (): boolean => {
    return hasAnyRole(["DOCTOR", "NURSE", "ADMIN", "SUPER_ADMIN"]);
  };

  const canManagePatients = (): boolean => {
    return hasMinimumRole("RECEPTIONIST");
  };

  const canViewAnalytics = (): boolean => {
    return hasMinimumRole("ACCOUNTANT");
  };

  return {
    // Vérifications de base
    hasRole,
    hasMinimumRole,
    hasAnyRole,
    
    // Rôles spécifiques
    isAdmin,
    isSuperAdmin,
    isDoctor,
    isNurse,
    isPharmacist,
    
    // Permissions fonctionnelles
    canManageUsers,
    canManageSettings,
    canViewReports,
    canManagePharmacy,
    canManageLaboratory,
    canCreateConsultations,
    canManagePatients,
    canViewAnalytics,
    
    // Informations utilisateur
    userRole: session?.user?.role as UserRole,
    userId: session?.user?.id,
    organizationId: session?.user?.organizationId,
  };
}

// Composants de convenance
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <PermissionGuard allowedRoles={["ADMIN", "SUPER_ADMIN"]} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function DoctorOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <PermissionGuard allowedRoles={["DOCTOR"]} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function MedicalStaffOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <PermissionGuard 
      allowedRoles={["DOCTOR", "NURSE", "ADMIN", "SUPER_ADMIN"]} 
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  );
}

export function PharmacistOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <PermissionGuard allowedRoles={["PHARMACIST", "ADMIN", "SUPER_ADMIN"]} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function MinimumRole({ 
  role, 
  children, 
  fallback 
}: { 
  role: UserRole; 
  children: ReactNode; 
  fallback?: ReactNode;
}) {
  return (
    <PermissionGuard requiredRole={role} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}
