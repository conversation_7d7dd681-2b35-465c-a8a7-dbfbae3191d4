"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Heart,
  LayoutDashboard,
  Users,
  Calendar,
  Stethoscope,
  Pill,
  FileText,
  Settings,
  LogOut,
  ChevronDown,
  Menu,
  X,
  UserCheck,
  Building2,
  BarChart3,
  CreditCard,
  TestTube,
} from "lucide-react";

const navigation = [
  {
    name: "Tableau de bord",
    href: "/dashboard",
    icon: LayoutDashboard,
    current: false,
  },
  {
    name: "Patients",
    href: "/dashboard/patients",
    icon: Users,
    current: false,
    badge: "Nouveau",
  },
  {
    name: "Consultations",
    href: "/dashboard/consultations",
    icon: Stethoscope,
    current: false,
  },
  {
    name: "Rendez-vous",
    href: "/dashboard/appointments",
    icon: Calendar,
    current: false,
  },
  {
    name: "Prescriptions",
    href: "/dashboard/prescriptions",
    icon: FileText,
    current: false,
  },
  {
    name: "Pharmacie",
    href: "/dashboard/pharmacy",
    icon: Pill,
    current: false,
  },
  {
    name: "Laboratoire",
    href: "/dashboard/laboratory",
    icon: TestTube,
    current: false,
  },
  {
    name: "Personnel/RH",
    href: "/dashboard/hr",
    icon: UserCheck,
    current: false,
  },
  {
    name: "Hospitalisations",
    href: "/dashboard/hospitalization",
    icon: Building2,
    current: false,
  },
  {
    name: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
    current: false,
    badge: "Nouveau",
  },
  {
    name: "Rapports",
    href: "/dashboard/reports",
    icon: FileText,
    current: false,
    badge: "Nouveau",
  },
  {
    name: "Facturation",
    href: "/dashboard/billing",
    icon: CreditCard,
    current: false,
  },
];

const adminNavigation = [
  {
    name: "Organisation",
    href: "/dashboard/organization",
    icon: Building2,
    current: false,
  },
  {
    name: "Paramètres",
    href: "/dashboard/settings",
    icon: Settings,
    current: false,
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { data: session } = useSession();

  const isAdmin =
    session?.user?.role === "ADMIN" || session?.user?.role === "SUPER_ADMIN";

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" });
  };

  const getUserInitials = () => {
    if (!session?.user) return "U";
    const firstName =
      session.user.name?.split(" ")[0] || session.user.email?.charAt(0) || "U";
    const lastName = session.user.name?.split(" ")[1]?.charAt(0) || "";
    return `${firstName.charAt(0)}${lastName}`.toUpperCase();
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SUPER_ADMIN":
        return "bg-red-100 text-red-800";
      case "ADMIN":
        return "bg-purple-100 text-purple-800";
      case "DOCTOR":
        return "bg-blue-100 text-blue-800";
      case "NURSE":
        return "bg-green-100 text-green-800";
      case "PHARMACIST":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "SUPER_ADMIN":
        return "Super Admin";
      case "ADMIN":
        return "Administrateur";
      case "DOCTOR":
        return "Médecin";
      case "NURSE":
        return "Infirmier(ère)";
      case "PHARMACIST":
        return "Pharmacien";
      case "RECEPTIONIST":
        return "Réceptionniste";
      case "TECHNICIAN":
        return "Technicien";
      case "ACCOUNTANT":
        return "Comptable";
      default:
        return role;
    }
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <X className="h-4 w-4" />
          ) : (
            <Menu className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "h-full w-64 bg-white border-r border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out",
          isMobileMenuOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl shadow-lg">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">GlobalCare</h1>
                <p className="text-xs text-gray-500">Solutions SaaS</p>
              </div>
            </div>
          </div>

          {/* Organization Info */}
          {session?.user?.organization && (
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center shadow-lg">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {session.user.organization.name}
                  </p>
                  <div className="flex items-center space-x-2">
                    <p className="text-xs text-gray-600">
                      Plan {session.user.organization.subscriptionPlan}
                    </p>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-[1.02]",
                    isActive
                      ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg shadow-blue-500/25"
                      : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <item.icon
                    className={cn(
                      "mr-3 h-5 w-5 flex-shrink-0 transition-colors",
                      isActive
                        ? "text-white"
                        : "text-gray-500 group-hover:text-gray-700"
                    )}
                  />
                  {item.name}
                  {item.badge && (
                    <Badge className="ml-auto text-xs bg-gradient-to-r from-emerald-400 to-teal-400 text-white border-0 shadow-lg">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              );
            })}

            {/* Admin Section */}
            {isAdmin && (
              <>
                <div className="pt-6 pb-2">
                  <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Administration
                  </h3>
                </div>
                {adminNavigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover:scale-[1.02]",
                        isActive
                          ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25"
                          : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5 flex-shrink-0 transition-colors",
                          isActive
                            ? "text-white"
                            : "text-gray-500 group-hover:text-gray-700"
                        )}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </>
            )}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-gray-100">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-start p-3 hover:bg-gray-50 rounded-xl transition-all duration-200"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10 ring-2 ring-blue-100">
                      <AvatarImage src={session?.user?.image || ""} />
                      <AvatarFallback className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-sm font-semibold">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {session?.user?.name || session?.user?.email}
                      </p>
                      <div className="flex items-center space-x-2">
                        <Badge
                          className={cn(
                            "text-xs border-0",
                            session?.user?.role === "ADMIN" ||
                              session?.user?.role === "SUPER_ADMIN"
                              ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                              : "bg-gradient-to-r from-emerald-500 to-teal-500 text-white"
                          )}
                        >
                          {getRoleLabel(session?.user?.role || "")}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Mon compte</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/profile">
                    <Settings className="mr-2 h-4 w-4" />
                    Profil
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="text-red-600"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Se déconnecter
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </>
  );
}
