"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
} from "lucide-react";

interface CalendarEvent {
  id: string;
  title: string;
  startDate: Date;
  endDate: Date;
  type: "ANNUAL" | "SICK" | "MATERNITY" | "EMERGENCY" | "UNPAID";
  status: "PENDING" | "APPROVED" | "REJECTED";
  employee: {
    firstName: string;
    lastName: string;
  };
}

interface CalendarViewProps {
  events: CalendarEvent[];
  onEventClick?: (event: CalendarEvent) => void;
}

const typeColors = {
  ANNUAL: "bg-blue-100 text-blue-800 border-blue-200",
  SICK: "bg-red-100 text-red-800 border-red-200",
  MATERNITY: "bg-pink-100 text-pink-800 border-pink-200",
  EMERGENCY: "bg-orange-100 text-orange-800 border-orange-200",
  UNPAID: "bg-gray-100 text-gray-800 border-gray-200",
};

const statusColors = {
  PENDING: "border-l-yellow-400",
  APPROVED: "border-l-green-400",
  REJECTED: "border-l-red-400",
};

const typeLabels = {
  ANNUAL: "Congé annuel",
  SICK: "Congé maladie",
  MATERNITY: "Congé maternité",
  EMERGENCY: "Congé d'urgence",
  UNPAID: "Congé sans solde",
};

export function CalendarView({ events, onEventClick }: CalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());

  // Obtenir le premier jour du mois
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
  
  // Obtenir le premier jour de la semaine à afficher (peut être du mois précédent)
  const firstDayOfWeek = new Date(firstDayOfMonth);
  firstDayOfWeek.setDate(firstDayOfMonth.getDate() - firstDayOfMonth.getDay());
  
  // Obtenir le dernier jour de la semaine à afficher (peut être du mois suivant)
  const lastDayOfWeek = new Date(lastDayOfMonth);
  lastDayOfWeek.setDate(lastDayOfMonth.getDate() + (6 - lastDayOfMonth.getDay()));

  // Générer tous les jours à afficher
  const days = [];
  const currentDay = new Date(firstDayOfWeek);
  
  while (currentDay <= lastDayOfWeek) {
    days.push(new Date(currentDay));
    currentDay.setDate(currentDay.getDate() + 1);
  }

  // Fonction pour vérifier si une date est dans une période de congé
  const getEventsForDate = (date: Date) => {
    return events.filter(event => {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);
      eventStart.setHours(0, 0, 0, 0);
      eventEnd.setHours(23, 59, 59, 999);
      
      return date >= eventStart && date <= eventEnd;
    });
  };

  // Navigation
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const monthNames = [
    "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
    "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
  ];

  const dayNames = ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"];

  return (
    <div className="bg-white rounded-lg border">
      {/* Header du calendrier */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h2>
          <Button variant="outline" size="sm" onClick={goToToday}>
            <CalendarIcon className="h-4 w-4 mr-2" />
            Aujourd'hui
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={goToNextMonth}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Grille du calendrier */}
      <div className="p-4">
        {/* En-têtes des jours */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Jours du calendrier */}
        <div className="grid grid-cols-7 gap-1">
          {days.map((day, index) => {
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();
            const isToday = day.toDateString() === new Date().toDateString();
            const dayEvents = getEventsForDate(day);

            return (
              <div
                key={index}
                className={`
                  min-h-[100px] p-1 border rounded-lg
                  ${isCurrentMonth ? "bg-white" : "bg-gray-50"}
                  ${isToday ? "ring-2 ring-blue-500" : ""}
                `}
              >
                {/* Numéro du jour */}
                <div className={`
                  text-sm font-medium mb-1
                  ${isCurrentMonth ? "text-gray-900" : "text-gray-400"}
                  ${isToday ? "text-blue-600" : ""}
                `}>
                  {day.getDate()}
                </div>

                {/* Événements du jour */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 2).map((event) => (
                    <div
                      key={event.id}
                      className={`
                        text-xs p-1 rounded border-l-2 cursor-pointer
                        ${typeColors[event.type]}
                        ${statusColors[event.status]}
                        hover:shadow-sm transition-shadow
                      `}
                      onClick={() => onEventClick?.(event)}
                      title={`${event.employee.firstName} ${event.employee.lastName} - ${typeLabels[event.type]}`}
                    >
                      <div className="font-medium truncate">
                        {event.employee.firstName} {event.employee.lastName}
                      </div>
                      <div className="truncate opacity-75">
                        {typeLabels[event.type]}
                      </div>
                    </div>
                  ))}
                  
                  {/* Indicateur s'il y a plus d'événements */}
                  {dayEvents.length > 2 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{dayEvents.length - 2} autre(s)
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Légende */}
      <div className="border-t p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Légende</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {/* Types de congés */}
          <div>
            <h4 className="text-xs font-medium text-gray-700 mb-2">Types de congés</h4>
            <div className="space-y-1">
              {Object.entries(typeLabels).map(([type, label]) => (
                <div key={type} className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded border ${typeColors[type as keyof typeof typeColors]}`}></div>
                  <span className="text-xs text-gray-600">{label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Statuts */}
          <div>
            <h4 className="text-xs font-medium text-gray-700 mb-2">Statuts</h4>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 border-l-2 border-l-yellow-400 bg-gray-100"></div>
                <span className="text-xs text-gray-600">En attente</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 border-l-2 border-l-green-400 bg-gray-100"></div>
                <span className="text-xs text-gray-600">Approuvé</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 border-l-2 border-l-red-400 bg-gray-100"></div>
                <span className="text-xs text-gray-600">Refusé</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
