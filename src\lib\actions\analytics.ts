"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// ===== TYPES =====

export interface KPIData {
  title: string;
  value: string;
  unit: string;
  change: string;
  trend: "up" | "down";
  previousValue?: number;
}

export interface DepartmentStats {
  name: string;
  patients: number;
  revenue: number;
  satisfaction: number;
  consultations: number;
  trend: "up" | "down";
}

export interface MonthlyData {
  month: string;
  patients: number;
  revenue: number;
  consultations: number;
  admissions?: number;
  labTests?: number;
}

export interface AnalyticsFilters {
  timeRange: "7d" | "30d" | "90d" | "1y";
  startDate?: string;
  endDate?: string;
  department?: string;
}

// ===== FONCTIONS UTILITAIRES =====

function getDateRange(timeRange: string) {
  const now = new Date();
  let startDate: Date;

  switch (timeRange) {
    case "7d":
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case "30d":
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case "90d":
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case "1y":
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return { startDate, endDate: now };
}

function calculatePercentageChange(current: number, previous: number): string {
  if (previous === 0) return "+100%";
  const change = ((current - previous) / previous) * 100;
  return `${change >= 0 ? "+" : ""}${change.toFixed(1)}%`;
}

// ===== KPIs PRINCIPAUX =====

export async function getMainKPIs(
  filters: AnalyticsFilters = { timeRange: "30d" }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const { startDate, endDate } = getDateRange(filters.timeRange);
    const organizationId = session.user.organizationId;

    // Période précédente pour comparaison
    const periodDuration = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodDuration);
    const previousEndDate = startDate;

    // 1. Revenus
    const currentRevenue = await prisma.payment.aggregate({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
        status: "PAID",
      },
      _sum: { amount: true },
    });

    const previousRevenue = await prisma.payment.aggregate({
      where: {
        organizationId,
        createdAt: { gte: previousStartDate, lte: previousEndDate },
        status: "PAID",
      },
      _sum: { amount: true },
    });

    // 2. Nouveaux patients
    const currentPatients = await prisma.patient.count({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
        isActive: true,
      },
    });

    const previousPatients = await prisma.patient.count({
      where: {
        organizationId,
        createdAt: { gte: previousStartDate, lte: previousEndDate },
        isActive: true,
      },
    });

    // 3. Consultations
    const currentConsultations = await prisma.consultation.count({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
      },
    });

    const previousConsultations = await prisma.consultation.count({
      where: {
        organizationId,
        createdAt: { gte: previousStartDate, lte: previousEndDate },
      },
    });

    // 4. Taux d'occupation des lits
    const totalBeds = await prisma.bed.count({
      where: {
        organizationId,
        isActive: true,
      },
    });

    const occupiedBeds = await prisma.bed.count({
      where: {
        organizationId,
        status: "OCCUPIED",
        isActive: true,
      },
    });

    const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

    // 5. Temps d'attente moyen (simulation)
    const avgWaitTime = 18; // En minutes - à calculer réellement plus tard

    // 6. Taux de satisfaction (simulation)
    const satisfactionRate = 94.8; // En pourcentage - à calculer réellement plus tard

    const kpis: KPIData[] = [
      {
        title: "Revenus",
        value: (currentRevenue._sum.amount || 0).toLocaleString(),
        unit: "XOF",
        change: calculatePercentageChange(
          currentRevenue._sum.amount || 0,
          previousRevenue._sum.amount || 0
        ),
        trend:
          (currentRevenue._sum.amount || 0) >=
          (previousRevenue._sum.amount || 0)
            ? "up"
            : "down",
      },
      {
        title: "Nouveaux Patients",
        value: currentPatients.toString(),
        unit: "patients",
        change: calculatePercentageChange(currentPatients, previousPatients),
        trend: currentPatients >= previousPatients ? "up" : "down",
      },
      {
        title: "Consultations",
        value: currentConsultations.toString(),
        unit: "consultations",
        change: calculatePercentageChange(
          currentConsultations,
          previousConsultations
        ),
        trend: currentConsultations >= previousConsultations ? "up" : "down",
      },
      {
        title: "Taux d'Occupation",
        value: occupancyRate.toFixed(1),
        unit: "%",
        change: "+3.2%", // Simulation
        trend: "up",
      },
      {
        title: "Temps d'Attente Moyen",
        value: avgWaitTime.toString(),
        unit: "minutes",
        change: "-5.3%", // Simulation (diminution = amélioration)
        trend: "up", // Trend up car c'est une amélioration
      },
      {
        title: "Taux de Satisfaction",
        value: satisfactionRate.toString(),
        unit: "%",
        change: "-1.2%", // Simulation
        trend: "down",
      },
    ];

    return { success: true, kpis };
  } catch (error) {
    console.error("Erreur lors de la récupération des KPIs:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== STATISTIQUES PAR DÉPARTEMENT =====

export async function getDepartmentStats(
  filters: AnalyticsFilters = { timeRange: "30d" }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const { startDate, endDate } = getDateRange(filters.timeRange);
    const organizationId = session.user.organizationId;

    // Récupérer les départements avec leurs statistiques
    const departments = await prisma.department.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        employees: {
          where: {
            status: "ACTIVE",
          },
          include: {
            user: {
              include: {
                consultations: {
                  where: {
                    createdAt: { gte: startDate, lte: endDate },
                  },
                  include: {
                    payments: {
                      where: {
                        status: "PAID",
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    const departmentStats: DepartmentStats[] = departments.map((dept) => {
      let totalPatients = 0;
      let totalRevenue = 0;
      let totalConsultations = 0;

      dept.employees.forEach((employee) => {
        if (employee.user) {
          totalConsultations += employee.user.consultations.length;

          employee.user.consultations.forEach((consultation) => {
            totalPatients++; // Compter chaque consultation comme un patient traité
            consultation.payments.forEach((payment) => {
              totalRevenue += payment.amount;
            });
          });
        }
      });

      return {
        name: dept.name,
        patients: totalPatients,
        revenue: totalRevenue,
        satisfaction: 90 + Math.random() * 10, // Simulation entre 90-100%
        consultations: totalConsultations,
        trend: Math.random() > 0.3 ? "up" : "down", // 70% de chance d'être en hausse
      };
    });

    return { success: true, departmentStats };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des stats par département:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== DONNÉES MENSUELLES =====

export async function getMonthlyData(
  filters: AnalyticsFilters = { timeRange: "1y" }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const organizationId = session.user.organizationId;
    const now = new Date();
    const monthlyData: MonthlyData[] = [];

    // Générer les données pour les 6 derniers mois
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);

      const monthName = date.toLocaleDateString("fr-FR", { month: "short" });

      // Patients du mois
      const patients = await prisma.patient.count({
        where: {
          organizationId,
          createdAt: { gte: date, lt: nextMonth },
          isActive: true,
        },
      });

      // Revenus du mois
      const revenue = await prisma.payment.aggregate({
        where: {
          organizationId,
          createdAt: { gte: date, lt: nextMonth },
          status: "PAID",
        },
        _sum: { amount: true },
      });

      // Consultations du mois
      const consultations = await prisma.consultation.count({
        where: {
          organizationId,
          createdAt: { gte: date, lt: nextMonth },
        },
      });

      // Admissions du mois
      const admissions = await prisma.admission.count({
        where: {
          organizationId,
          admissionDate: { gte: date, lt: nextMonth },
        },
      });

      // Tests de laboratoire du mois
      const labTests = await prisma.labOrder.count({
        where: {
          organizationId,
          createdAt: { gte: date, lt: nextMonth },
        },
      });

      monthlyData.push({
        month: monthName,
        patients,
        revenue: revenue._sum.amount || 0,
        consultations,
        admissions,
        labTests,
      });
    }

    return { success: true, monthlyData };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des données mensuelles:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== STATISTIQUES GLOBALES =====

export async function getGlobalStats() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const organizationId = session.user.organizationId;

    const stats = await Promise.all([
      // Total patients
      prisma.patient.count({
        where: { organizationId, isActive: true },
      }),

      // Total consultations
      prisma.consultation.count({
        where: { organizationId },
      }),

      // Total revenus
      prisma.payment.aggregate({
        where: { organizationId, status: "PAID" },
        _sum: { amount: true },
      }),

      // Total chambres
      prisma.room.count({
        where: { organizationId, isActive: true },
      }),

      // Total lits
      prisma.bed.count({
        where: { organizationId, isActive: true },
      }),

      // Total employés
      prisma.employee.count({
        where: { organizationId, status: "ACTIVE" },
      }),
    ]);

    return {
      success: true,
      stats: {
        totalPatients: stats[0],
        totalConsultations: stats[1],
        totalRevenue: stats[2]._sum.amount || 0,
        totalRooms: stats[3],
        totalBeds: stats[4],
        totalEmployees: stats[5],
      },
    };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des statistiques globales:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
