"use server";

import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  ConsultationStatus,
  ConsultationType,
  PaymentStatus,
} from "@prisma/client";

// Types pour les données de consultation/rendez-vous
export interface AppointmentFormData {
  patientId: string;
  doctorId: string;
  consultationDate: string;
  type: ConsultationType;
  chiefComplaint?: string;
  consultationFee?: number;
  duration?: number;
  notes?: string;
}

export interface AppointmentFilters {
  search?: string;
  doctorId?: string;
  patientId?: string;
  status?: ConsultationStatus;
  type?: ConsultationType;
  dateFrom?: string;
  dateTo?: string;
  paymentStatus?: PaymentStatus;
}

// Créer un nouveau rendez-vous
export async function createAppointment(data: AppointmentFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le patient existe et appartient à l'organisation
    const patient = await prisma.patient.findFirst({
      where: {
        id: data.patientId,
        organizationId: session.user.organizationId,
      },
    });

    if (!patient) {
      throw new Error("Patient non trouvé");
    }

    // Vérifier que le médecin existe et appartient à l'organisation
    const doctor = await prisma.user.findFirst({
      where: {
        id: data.doctorId,
        organizationId: session.user.organizationId,
        role: { in: ["DOCTOR", "ADMIN"] },
      },
    });

    if (!doctor) {
      throw new Error("Médecin non trouvé");
    }

    // Vérifier les conflits d'horaires
    const conflictingAppointment = await prisma.consultation.findFirst({
      where: {
        doctorId: data.doctorId,
        consultationDate: new Date(data.consultationDate),
        status: { in: ["SCHEDULED", "IN_PROGRESS"] as ConsultationStatus[] },
        organizationId: session.user.organizationId,
      },
    });

    if (conflictingAppointment) {
      throw new Error("Le médecin a déjà un rendez-vous à cette heure");
    }

    // Créer le rendez-vous (commence en attente de paiement)
    const appointment = await prisma.consultation.create({
      data: {
        ...data,
        consultationDate: new Date(data.consultationDate),
        status: "PENDING_PAYMENT" as ConsultationStatus,
        organizationId: session.user.organizationId,
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/appointments");
    return { success: true, appointment };
  } catch (error) {
    console.error("Erreur lors de la création du rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer la liste des rendez-vous avec filtres
export async function getAppointments(
  filters: AppointmentFilters = {},
  page = 1,
  limit = 20
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    // Filtres de recherche
    if (filters.search) {
      where.OR = [
        {
          patient: {
            firstName: { contains: filters.search, mode: "insensitive" },
          },
        },
        {
          patient: {
            lastName: { contains: filters.search, mode: "insensitive" },
          },
        },
        {
          patient: {
            patientNumber: { contains: filters.search, mode: "insensitive" },
          },
        },
        {
          doctor: {
            firstName: { contains: filters.search, mode: "insensitive" },
          },
        },
        {
          doctor: {
            lastName: { contains: filters.search, mode: "insensitive" },
          },
        },
        { chiefComplaint: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    if (filters.doctorId) {
      where.doctorId = filters.doctorId;
    }

    if (filters.patientId) {
      where.patientId = filters.patientId;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.paymentStatus) {
      where.paymentStatus = filters.paymentStatus;
    }

    // Filtres de date
    if (filters.dateFrom || filters.dateTo) {
      where.consultationDate = {};
      if (filters.dateFrom) {
        where.consultationDate.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.consultationDate.lte = new Date(filters.dateTo);
      }
    }

    const [appointments, total] = await Promise.all([
      prisma.consultation.findMany({
        where,
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              patientNumber: true,
              phone: true,
              email: true,
              dateOfBirth: true,
              gender: true,
            },
          },
          doctor: {
            select: {
              firstName: true,
              lastName: true,
              role: true,
            },
          },
        },
        orderBy: { consultationDate: "asc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.consultation.count({ where }),
    ]);

    return {
      success: true,
      appointments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les médecins disponibles
export async function getAvailableDoctors() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const doctors = await prisma.user.findMany({
      where: {
        organizationId: session.user.organizationId,
        role: { in: ["DOCTOR", "ADMIN"] },
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        role: true,
        phone: true,
        email: true,
      },
      orderBy: [{ role: "asc" }, { firstName: "asc" }],
    });

    return { success: true, doctors };
  } catch (error) {
    console.error("Erreur lors de la récupération des médecins:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour le statut d'un rendez-vous
export async function updateAppointmentStatus(
  id: string,
  status: ConsultationStatus,
  notes?: string
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const appointment = await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status,
        ...(notes && { notes }),
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/appointments");
    return { success: true, appointment };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer un rendez-vous par ID
export async function getAppointmentById(id: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const appointment = await prisma.consultation.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        doctor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
    });

    if (!appointment) {
      throw new Error("Rendez-vous non trouvé");
    }

    return { success: true, appointment };
  } catch (error) {
    console.error("Erreur lors de la récupération du rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour un rendez-vous
export async function updateAppointment(
  id: string,
  data: Partial<AppointmentFormData>
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le rendez-vous existe et appartient à l'organisation
    const existingAppointment = await prisma.consultation.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingAppointment) {
      throw new Error("Rendez-vous non trouvé");
    }

    // Si on change le patient, vérifier qu'il existe
    if (data.patientId && data.patientId !== existingAppointment.patientId) {
      const patient = await prisma.patient.findFirst({
        where: {
          id: data.patientId,
          organizationId: session.user.organizationId,
        },
      });

      if (!patient) {
        throw new Error("Patient non trouvé");
      }
    }

    // Si on change le médecin, vérifier qu'il existe
    if (data.doctorId && data.doctorId !== existingAppointment.doctorId) {
      const doctor = await prisma.user.findFirst({
        where: {
          id: data.doctorId,
          organizationId: session.user.organizationId,
          role: { in: ["DOCTOR", "ADMIN"] },
        },
      });

      if (!doctor) {
        throw new Error("Médecin non trouvé");
      }
    }

    // Si on change la date, vérifier les conflits d'horaires
    if (data.consultationDate) {
      const newDate =
        typeof data.consultationDate === "string"
          ? new Date(data.consultationDate)
          : data.consultationDate;

      const conflictingAppointment = await prisma.consultation.findFirst({
        where: {
          id: { not: id }, // Exclure le rendez-vous actuel
          doctorId: data.doctorId || existingAppointment.doctorId,
          consultationDate: newDate,
          status: { in: ["SCHEDULED", "IN_PROGRESS"] },
          organizationId: session.user.organizationId,
        },
      });

      if (conflictingAppointment) {
        throw new Error("Le médecin a déjà un rendez-vous à cette heure");
      }
    }

    // Préparer les données de mise à jour
    const updateData: any = {};

    if (data.patientId) updateData.patientId = data.patientId;
    if (data.doctorId) updateData.doctorId = data.doctorId;
    if (data.consultationDate) {
      updateData.consultationDate =
        typeof data.consultationDate === "string"
          ? new Date(data.consultationDate)
          : data.consultationDate;
    }
    if (data.type) updateData.type = data.type;
    if (data.chiefComplaint !== undefined)
      updateData.chiefComplaint = data.chiefComplaint;
    if (data.consultationFee !== undefined)
      updateData.consultationFee = data.consultationFee;
    if (data.notes !== undefined) updateData.notes = data.notes;

    // Mettre à jour le rendez-vous
    const appointment = await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: updateData,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        doctor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/appointments");
    revalidatePath(`/dashboard/appointments/${id}`);
    return { success: true, appointment };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Annuler un rendez-vous
export async function cancelAppointment(id: string, reason?: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status: "CANCELLED",
        notes: reason ? `Annulé: ${reason}` : "Rendez-vous annulé",
      },
    });

    revalidatePath("/dashboard/appointments");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de l'annulation du rendez-vous:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
