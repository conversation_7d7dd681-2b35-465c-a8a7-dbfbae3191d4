"use server";

import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ConsultationType, PaymentMethod, PaymentStatus } from "@prisma/client";
import { checkPermission, logAction } from "./permissions";

// Types pour la facturation
export interface ConsultationPriceData {
  consultationType: ConsultationType;
  basePrice: number;
  emergencyPrice?: number;
  insurancePrice?: number;
  description?: string;
}

export interface PaymentData {
  patientId: string;
  consultationId?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  description?: string;
  notes?: string;
}

// ===== GESTION DES TARIFS =====

// Créer ou mettre à jour un tarif de consultation
export async function upsertConsultationPrice(data: ConsultationPriceData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const consultationPrice = await prisma.consultationPrice.upsert({
      where: {
        organizationId_consultationType: {
          organizationId: session.user.organizationId,
          consultationType: data.consultationType,
        },
      },
      update: {
        basePrice: data.basePrice,
        emergencyPrice: data.emergencyPrice,
        insurancePrice: data.insurancePrice,
        description: data.description,
      },
      create: {
        ...data,
        organizationId: session.user.organizationId,
      },
    });

    revalidatePath("/dashboard/billing/prices");
    return { success: true, consultationPrice };
  } catch (error) {
    console.error("Erreur lors de la sauvegarde du tarif:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les tarifs de consultation
export async function getConsultationPrices() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const prices = await prisma.consultationPrice.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      orderBy: {
        consultationType: "asc",
      },
    });

    return { success: true, prices };
  } catch (error) {
    console.error("Erreur lors de la récupération des tarifs:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer le tarif pour un type de consultation
export async function getConsultationPrice(consultationType: ConsultationType) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const price = await prisma.consultationPrice.findUnique({
      where: {
        organizationId_consultationType: {
          organizationId: session.user.organizationId,
          consultationType,
        },
      },
    });

    return { success: true, price };
  } catch (error) {
    console.error("Erreur lors de la récupération du tarif:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== GESTION DES PAIEMENTS =====

// Générer un numéro de paiement unique
async function generatePaymentNumber(organizationId: string): Promise<string> {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");

  const prefix = `PAY-${year}${month}${day}`;

  // Trouver le dernier numéro de paiement du jour
  const lastPayment = await prisma.payment.findFirst({
    where: {
      organizationId,
      paymentNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      paymentNumber: "desc",
    },
  });

  let sequence = 1;
  if (lastPayment) {
    const lastSequence = parseInt(
      lastPayment.paymentNumber.split("-").pop() || "0"
    );
    sequence = lastSequence + 1;
  }

  return `${prefix}-${String(sequence).padStart(4, "0")}`;
}

// Créer un paiement
export async function createPayment(data: PaymentData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId || !session?.user?.id) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "PAYMENTS"
    );

    if (!canCreate) {
      await logAction(
        "CREATE",
        "PAYMENTS",
        undefined,
        undefined,
        data,
        false,
        "Permission refusée"
      );
      throw new Error("Vous n'avez pas l'autorisation de créer des paiements");
    }

    // Vérifier que le patient existe
    const patient = await prisma.patient.findFirst({
      where: {
        id: data.patientId,
        organizationId: session.user.organizationId,
      },
    });

    if (!patient) {
      throw new Error("Patient non trouvé");
    }

    // Vérifier la consultation si fournie
    if (data.consultationId) {
      const consultation = await prisma.consultation.findFirst({
        where: {
          id: data.consultationId,
          organizationId: session.user.organizationId,
          patientId: data.patientId,
        },
      });

      if (!consultation) {
        throw new Error("Consultation non trouvée");
      }
    }

    // Générer le numéro de paiement
    const paymentNumber = await generatePaymentNumber(
      session.user.organizationId
    );

    // Créer le paiement
    const payment = await prisma.payment.create({
      data: {
        ...data,
        paymentNumber,
        organizationId: session.user.organizationId,
        createdById: session.user.id,
        status: PaymentStatus.PAID,
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        consultation: {
          select: {
            type: true,
            consultationDate: true,
          },
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Mettre à jour le statut de paiement de la consultation si applicable
    if (data.consultationId) {
      await prisma.consultation.update({
        where: {
          id: data.consultationId,
        },
        data: {
          paymentStatus: PaymentStatus.PAID,
          consultationFee: data.amount,
        },
      });
    }

    revalidatePath("/dashboard/billing/payments");
    revalidatePath("/dashboard/appointments");
    if (data.consultationId) {
      revalidatePath(`/dashboard/consultations/${data.consultationId}`);
    }

    return { success: true, payment };
  } catch (error) {
    console.error("Erreur lors de la création du paiement:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les paiements avec filtres
export async function getPayments(filters?: {
  patientId?: string;
  consultationId?: string;
  paymentMethod?: PaymentMethod;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(session.user.id, "READ", "PAYMENTS");

    if (!canRead) {
      await logAction(
        "read",
        "PAYMENTS",
        undefined,
        undefined,
        undefined,
        false,
        "Permission refusée"
      );
      throw new Error(
        "Vous n'avez pas l'autorisation de consulter les paiements"
      );
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (filters?.patientId) {
      where.patientId = filters.patientId;
    }

    if (filters?.consultationId) {
      where.consultationId = filters.consultationId;
    }

    if (filters?.paymentMethod) {
      where.paymentMethod = filters.paymentMethod;
    }

    if (filters?.dateFrom || filters?.dateTo) {
      where.paymentDate = {};
      if (filters.dateFrom) {
        where.paymentDate.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.paymentDate.lte = new Date(filters.dateTo);
      }
    }

    if (filters?.search) {
      where.OR = [
        {
          paymentNumber: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          patient: {
            OR: [
              {
                firstName: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
              {
                patientNumber: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
            ],
          },
        },
      ];
    }

    const payments = await prisma.payment.findMany({
      where,
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        consultation: {
          select: {
            type: true,
            consultationDate: true,
          },
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        paymentDate: "desc",
      },
    });

    return { success: true, payments };
  } catch (error) {
    console.error("Erreur lors de la récupération des paiements:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
