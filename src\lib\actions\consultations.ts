'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { ConsultationStatus, ConsultationType, PaymentStatus } from '@prisma/client'

// Types pour les données vitales
export interface VitalSignsData {
  weight?: number
  height?: number
  bloodPressure?: string
  temperature?: number
  heartRate?: number
}

// Types pour la mise à jour d'une consultation
export interface ConsultationUpdateData {
  status?: ConsultationStatus
  chiefComplaint?: string
  symptoms?: string
  diagnosis?: string
  treatment?: string
  notes?: string
  vitalSigns?: VitalSignsData
  consultationFee?: number
  paymentStatus?: PaymentStatus
  duration?: number
}

export interface ConsultationFilters {
  search?: string
  doctorId?: string
  patientId?: string
  status?: ConsultationStatus
  type?: ConsultationType
  dateFrom?: string
  dateTo?: string
  paymentStatus?: PaymentStatus
}

// Récupérer la liste des consultations avec filtres
export async function getConsultations(filters: ConsultationFilters = {}, page = 1, limit = 20) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      throw new Error('Session utilisateur invalide')
    }

    const where: any = {
      organizationId: session.user.organizationId,
    }

    // Filtres de recherche
    if (filters.search) {
      where.OR = [
        { patient: { firstName: { contains: filters.search, mode: 'insensitive' } } },
        { patient: { lastName: { contains: filters.search, mode: 'insensitive' } } },
        { patient: { patientNumber: { contains: filters.search, mode: 'insensitive' } } },
        { doctor: { firstName: { contains: filters.search, mode: 'insensitive' } } },
        { doctor: { lastName: { contains: filters.search, mode: 'insensitive' } } },
        { chiefComplaint: { contains: filters.search, mode: 'insensitive' } },
        { diagnosis: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    if (filters.doctorId) {
      where.doctorId = filters.doctorId
    }

    if (filters.patientId) {
      where.patientId = filters.patientId
    }

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.type) {
      where.type = filters.type
    }

    if (filters.paymentStatus) {
      where.paymentStatus = filters.paymentStatus
    }

    // Filtres de date
    if (filters.dateFrom || filters.dateTo) {
      where.consultationDate = {}
      if (filters.dateFrom) {
        where.consultationDate.gte = new Date(filters.dateFrom)
      }
      if (filters.dateTo) {
        where.consultationDate.lte = new Date(filters.dateTo)
      }
    }

    const [consultations, total] = await Promise.all([
      prisma.consultation.findMany({
        where,
        include: {
          patient: {
            select: {
              firstName: true,
              lastName: true,
              patientNumber: true,
              phone: true,
              email: true,
              dateOfBirth: true,
              gender: true,
              bloodType: true,
            }
          },
          doctor: {
            select: {
              firstName: true,
              lastName: true,
              role: true,
            }
          }
        },
        orderBy: { consultationDate: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.consultation.count({ where })
    ])

    return {
      success: true,
      consultations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des consultations:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    }
  }
}

// Récupérer une consultation par ID avec toutes les informations
export async function getConsultationById(id: string) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      throw new Error('Session utilisateur invalide')
    }

    const consultation = await prisma.consultation.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        patient: {
          include: {
            consultations: {
              where: { id: { not: id } },
              orderBy: { consultationDate: 'desc' },
              take: 5,
              include: {
                doctor: {
                  select: {
                    firstName: true,
                    lastName: true,
                  }
                }
              }
            },
            prescriptions: {
              orderBy: { prescriptionDate: 'desc' },
              take: 5,
              include: {
                doctor: {
                  select: {
                    firstName: true,
                    lastName: true,
                  }
                }
              }
            }
          }
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
            role: true,
            phone: true,
            email: true,
          }
        },
        prescriptions: {
          include: {
            doctor: {
              select: {
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      }
    })

    if (!consultation) {
      throw new Error('Consultation non trouvée')
    }

    return { success: true, consultation }
  } catch (error) {
    console.error('Erreur lors de la récupération de la consultation:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    }
  }
}

// Démarrer une consultation (changer le statut de SCHEDULED à IN_PROGRESS)
export async function startConsultation(id: string) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      throw new Error('Session utilisateur invalide')
    }

    const consultation = await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
        status: 'SCHEDULED', // Seulement si le statut est SCHEDULED
      },
      data: {
        status: 'IN_PROGRESS',
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          }
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/consultations')
    revalidatePath(`/dashboard/consultations/${id}`)
    return { success: true, consultation }
  } catch (error) {
    console.error('Erreur lors du démarrage de la consultation:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    }
  }
}

// Mettre à jour une consultation avec les données médicales
export async function updateConsultation(id: string, data: ConsultationUpdateData) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      throw new Error('Session utilisateur invalide')
    }

    const updateData: any = { ...data }
    
    // Gérer les données vitales
    if (data.vitalSigns) {
      updateData.weight = data.vitalSigns.weight
      updateData.height = data.vitalSigns.height
      updateData.bloodPressure = data.vitalSigns.bloodPressure
      updateData.temperature = data.vitalSigns.temperature
      updateData.heartRate = data.vitalSigns.heartRate
      delete updateData.vitalSigns
    }

    const consultation = await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: updateData,
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          }
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/consultations')
    revalidatePath(`/dashboard/consultations/${id}`)
    return { success: true, consultation }
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la consultation:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    }
  }
}

// Terminer une consultation (changer le statut de IN_PROGRESS à COMPLETED)
export async function completeConsultation(id: string, finalData?: Partial<ConsultationUpdateData>) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      throw new Error('Session utilisateur invalide')
    }

    const updateData: any = {
      status: 'COMPLETED',
      ...finalData,
    }

    // Gérer les données vitales si présentes
    if (finalData?.vitalSigns) {
      updateData.weight = finalData.vitalSigns.weight
      updateData.height = finalData.vitalSigns.height
      updateData.bloodPressure = finalData.vitalSigns.bloodPressure
      updateData.temperature = finalData.vitalSigns.temperature
      updateData.heartRate = finalData.vitalSigns.heartRate
      delete updateData.vitalSigns
    }

    const consultation = await prisma.consultation.update({
      where: {
        id,
        organizationId: session.user.organizationId,
        status: 'IN_PROGRESS', // Seulement si le statut est IN_PROGRESS
      },
      data: updateData,
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          }
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    revalidatePath('/dashboard/appointments')
    revalidatePath('/dashboard/consultations')
    revalidatePath(`/dashboard/consultations/${id}`)
    return { success: true, consultation }
  } catch (error) {
    console.error('Erreur lors de la finalisation de la consultation:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    }
  }
}
