"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { RoomType, BedType, BedStatus, AdmissionStatus } from "@prisma/client";
import { checkPermission, logAction } from "./permissions";

// ===== TYPES =====

export interface RoomFormData {
  number: string;
  name: string;
  floor: number;
  roomType: RoomType;
  capacity: number;
  hasPrivateBathroom: boolean;
  hasAirConditioning: boolean;
  hasTV: boolean;
  hasWifi: boolean;
  description?: string;
  dailyRate?: number;
}

export interface BedFormData {
  number: string;
  roomId: string;
  bedType: BedType;
  isElectric: boolean;
  hasOxygen: boolean;
  hasMonitoring: boolean;
  description?: string;
}

export interface AdmissionFormData {
  patientId: string;
  roomId: string;
  bedId?: string;
  doctorId: string;
  admissionDate: string;
  expectedDischargeDate?: string;
  reason: string;
  diagnosis?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceInfo?: string;
  admissionNotes?: string;
}

// ===== CHAMBRES =====

export async function createRoom(data: RoomFormData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canUpdate = await checkPermission(
      session.user.id,
      "UPDATE",
      "HOSPITALIZATION",
      id
    );

    if (!canUpdate) {
      await logAction("UPDATE", "HOSPITALIZATION", id, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de modifier les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "HOSPITALIZATION"
    );

    if (!canRead) {
      await logAction("READ", "HOSPITALIZATION", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "HOSPITALIZATION"
    );

    if (!canRead) {
      await logAction("READ", "HOSPITALIZATION", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "HOSPITALIZATION"
    );

    if (!canRead) {
      await logAction("READ", "HOSPITALIZATION", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "HOSPITALIZATION"
    );

    if (!canCreate) {
      await logAction("CREATE", "HOSPITALIZATION", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canUpdate = await checkPermission(
      session.user.id,
      "UPDATE",
      "HOSPITALIZATION",
      id
    );

    if (!canUpdate) {
      await logAction("UPDATE", "HOSPITALIZATION", id, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de modifier les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "HOSPITALIZATION"
    );

    if (!canRead) {
      await logAction("READ", "HOSPITALIZATION", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canUpdate = await checkPermission(
      session.user.id,
      "UPDATE",
      "HOSPITALIZATION",
      id
    );

    if (!canUpdate) {
      await logAction("UPDATE", "HOSPITALIZATION", id, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de modifier les hospitalisations");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "HOSPITALIZATION"
    );

    if (!canCreate) {
      await logAction("CREATE", "HOSPITALIZATION", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer les hospitalisations");
    }

    // Vérifier que le numéro de chambre n'existe pas déjà
    const existingRoom = await prisma.room.findFirst({
      where: {
        number: data.number,
        organizationId: session.user.organizationId,
      },
    });

    if (existingRoom) {
      throw new Error("Ce numéro de chambre existe déjà");
    }

    const room = await prisma.room.create({
      data: {
        ...data,
        organizationId: session.user.organizationId,
        currency: "XOF",
      },
    });

    // Créer automatiquement les lits selon la capacité
    const beds = [];
    for (let i = 1; i <= data.capacity; i++) {
      const bedLetter = String.fromCharCode(64 + i); // A, B, C, etc.
      beds.push({
        number: bedLetter,
        roomId: room.id,
        organizationId: session.user.organizationId,
        bedType: "STANDARD" as BedType,
        status: "AVAILABLE" as BedStatus,
      });
    }

    await prisma.bed.createMany({
      data: beds,
    });

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/rooms");

    return { success: true, room };
  } catch (error) {
    console.error("Erreur lors de la création de la chambre:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function updateRoom(id: string, data: Partial<RoomFormData>) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const room = await prisma.room.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data,
    });

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/rooms");

    return { success: true, room };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la chambre:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getRooms() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const rooms = await prisma.room.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      include: {
        beds: {
          include: {
            admissions: {
              where: {
                status: "ADMITTED",
              },
              include: {
                patient: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            beds: true,
            admissions: {
              where: {
                status: "ADMITTED",
              },
            },
          },
        },
      },
      orderBy: [{ floor: "asc" }, { number: "asc" }],
    });

    return { success: true, rooms };
  } catch (error) {
    console.error("Erreur lors de la récupération des chambres:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== LITS =====

export async function updateBedStatus(bedId: string, status: BedStatus) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const bed = await prisma.bed.update({
      where: {
        id: bedId,
        organizationId: session.user.organizationId,
      },
      data: {
        status,
      },
    });

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/rooms");

    return { success: true, bed };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du lit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== ADMISSIONS =====

export async function createAdmission(data: AdmissionFormData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Générer le numéro d'admission
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");

    const lastAdmission = await prisma.admission.findFirst({
      where: {
        organizationId: session.user.organizationId,
        admissionNumber: {
          startsWith: `ADM-${dateStr}`,
        },
      },
      orderBy: {
        admissionNumber: "desc",
      },
    });

    let sequence = 1;
    if (lastAdmission) {
      const lastSequence = parseInt(
        lastAdmission.admissionNumber.split("-")[2]
      );
      sequence = lastSequence + 1;
    }

    const admissionNumber = `ADM-${dateStr}-${sequence
      .toString()
      .padStart(3, "0")}`;

    // Vérifier que le lit est disponible si spécifié
    if (data.bedId) {
      const bed = await prisma.bed.findFirst({
        where: {
          id: data.bedId,
          status: "AVAILABLE",
          organizationId: session.user.organizationId,
        },
      });

      if (!bed) {
        throw new Error("Le lit sélectionné n'est pas disponible");
      }
    }

    const admission = await prisma.admission.create({
      data: {
        ...data,
        admissionNumber,
        organizationId: session.user.organizationId,
        admissionDate: new Date(data.admissionDate),
        expectedDischargeDate: data.expectedDischargeDate
          ? new Date(data.expectedDischargeDate)
          : undefined,
      },
    });

    // Mettre à jour le statut du lit si spécifié
    if (data.bedId) {
      await prisma.bed.update({
        where: {
          id: data.bedId,
        },
        data: {
          status: "OCCUPIED",
        },
      });
    }

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/admissions");

    return { success: true, admission };
  } catch (error) {
    console.error("Erreur lors de la création de l'admission:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getAdmissions() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const admissions = await prisma.admission.findMany({
      where: {
        organizationId: session.user.organizationId,
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            phone: true,
            dateOfBirth: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        room: {
          select: {
            number: true,
            name: true,
            roomType: true,
          },
        },
        bed: {
          select: {
            number: true,
          },
        },
      },
      orderBy: {
        admissionDate: "desc",
      },
    });

    return { success: true, admissions };
  } catch (error) {
    console.error("Erreur lors de la récupération des admissions:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== PATIENTS ET MÉDECINS =====

export async function getActivePatients() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const patients = await prisma.patient.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
      },
      orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
    });

    return { success: true, patients };
  } catch (error) {
    console.error("Erreur lors de la récupération des patients:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getActiveDoctors() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const doctors = await prisma.user.findMany({
      where: {
        organizationId: session.user.organizationId,
        role: "DOCTOR",
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
      },
      orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
    });

    return { success: true, doctors };
  } catch (error) {
    console.error("Erreur lors de la récupération des médecins:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function updateAdmissionStatus(
  admissionId: string,
  status: AdmissionStatus,
  dischargeNotes?: string
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const updateData: any = {
      status,
    };

    if (status === "DISCHARGED") {
      updateData.actualDischargeDate = new Date();
      if (dischargeNotes) {
        updateData.dischargeNotes = dischargeNotes;
      }
    }

    const admission = await prisma.admission.update({
      where: {
        id: admissionId,
        organizationId: session.user.organizationId,
      },
      data: updateData,
      include: {
        bed: true,
      },
    });

    // Libérer le lit si le patient sort
    if (status === "DISCHARGED" && admission.bed) {
      await prisma.bed.update({
        where: {
          id: admission.bed.id,
        },
        data: {
          status: "AVAILABLE",
        },
      });
    }

    revalidatePath("/dashboard/hospitalization");
    revalidatePath("/dashboard/hospitalization/admissions");

    return { success: true, admission };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'admission:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
