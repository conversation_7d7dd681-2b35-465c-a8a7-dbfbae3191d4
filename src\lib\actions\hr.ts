"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import {
  ContractType,
  EmployeeStatus,
  Gender,
  AttendanceStatus,
  LeaveType,
  LeaveStatus,
} from "@prisma/client";

// ===== DÉPARTEMENTS =====

export interface DepartmentFormData {
  name: string;
  code: string;
  description?: string;
  managerId?: string;
}

// Créer un département
export async function createDepartment(data: DepartmentFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le code n'existe pas déjà
    const existingDepartment = await prisma.department.findFirst({
      where: {
        organizationId: session.user.organizationId,
        code: data.code,
      },
    });

    if (existingDepartment) {
      throw new Error("Un département avec ce code existe déjà");
    }

    const department = await prisma.department.create({
      data: {
        ...data,
        organizationId: session.user.organizationId,
      },
    });

    revalidatePath("/dashboard/hr");
    return { success: true, department };
  } catch (error) {
    console.error("Erreur lors de la création du département:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les départements
export async function getDepartments() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const departments = await prisma.department.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      include: {
        manager: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            employees: true,
            positions: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return { success: true, departments };
  } catch (error) {
    console.error("Erreur lors de la récupération des départements:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== POSTES =====

export interface PositionFormData {
  title: string;
  code: string;
  description?: string;
  departmentId: string;
  baseSalary?: number;
  permissions?: Record<string, any>;
}

// Créer un poste
export async function createPosition(data: PositionFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le code n'existe pas déjà
    const existingPosition = await prisma.position.findFirst({
      where: {
        organizationId: session.user.organizationId,
        code: data.code,
      },
    });

    if (existingPosition) {
      throw new Error("Un poste avec ce code existe déjà");
    }

    const position = await prisma.position.create({
      data: {
        ...data,
        organizationId: session.user.organizationId,
      },
    });

    revalidatePath("/dashboard/hr");
    return { success: true, position };
  } catch (error) {
    console.error("Erreur lors de la création du poste:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les postes
export async function getPositions() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const positions = await prisma.position.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      include: {
        department: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            employees: true,
          },
        },
      },
      orderBy: [{ department: { name: "asc" } }, { title: "asc" }],
    });

    return { success: true, positions };
  } catch (error) {
    console.error("Erreur lors de la récupération des postes:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== EMPLOYÉS =====

export interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: Gender;
  address?: string;
  departmentId: string;
  positionId: string;
  hireDate?: string;
  contractEndDate?: string;
  currentSalary?: number;
  contractType: ContractType;
  workingHours?: number;
  userId?: string;
}

// Créer un employé
export async function createEmployee(data: EmployeeFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Générer le numéro d'employé
    const today = new Date();
    const year = today.getFullYear();

    const todayEmployeesCount = await prisma.employee.count({
      where: {
        organizationId: session.user.organizationId,
        createdAt: {
          gte: new Date(year, 0, 1),
          lt: new Date(year + 1, 0, 1),
        },
      },
    });

    const employeeNumber = `EMP-${year}-${String(
      todayEmployeesCount + 1
    ).padStart(4, "0")}`;

    const employee = await prisma.employee.create({
      data: {
        ...data,
        employeeNumber,
        organizationId: session.user.organizationId,
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : undefined,
        hireDate: data.hireDate ? new Date(data.hireDate) : new Date(),
        contractEndDate: data.contractEndDate
          ? new Date(data.contractEndDate)
          : undefined,
      },
      include: {
        department: {
          select: {
            name: true,
          },
        },
        position: {
          select: {
            title: true,
          },
        },
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/hr");
    return { success: true, employee };
  } catch (error) {
    console.error("Erreur lors de la création de l'employé:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les employés
export async function getEmployees(filters?: {
  departmentId?: string;
  positionId?: string;
  status?: EmployeeStatus | "ALL";
}) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    // Filtres
    if (filters?.departmentId) {
      where.departmentId = filters.departmentId;
    }

    if (filters?.positionId) {
      where.positionId = filters.positionId;
    }

    if (filters?.status && filters.status !== "ALL") {
      where.status = filters.status;
    }

    const employees = await prisma.employee.findMany({
      where,
      include: {
        department: {
          select: {
            name: true,
          },
        },
        position: {
          select: {
            title: true,
          },
        },
        user: {
          select: {
            email: true,
            role: true,
          },
        },
      },
      orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
    });

    return { success: true, employees };
  } catch (error) {
    console.error("Erreur lors de la récupération des employés:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour le statut d'un employé
export async function updateEmployeeStatus(id: string, status: EmployeeStatus) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.employee.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status,
      },
    });

    revalidatePath("/dashboard/hr");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== PRÉSENCES =====

export interface AttendanceFormData {
  employeeId: string;
  date: string;
  checkIn?: string;
  checkOut?: string;
  breakStart?: string;
  breakEnd?: string;
  status: AttendanceStatus;
  notes?: string;
}

// Créer/Mettre à jour une présence
export async function createOrUpdateAttendance(data: AttendanceFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Calculer les heures travaillées
    let hoursWorked: number | undefined;
    let overtimeHours: number | undefined;

    if (data.checkIn && data.checkOut) {
      const checkInTime = new Date(`${data.date}T${data.checkIn}:00`);
      const checkOutTime = new Date(`${data.date}T${data.checkOut}:00`);
      let totalHours =
        (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60);

      // Soustraire la pause si elle existe
      if (data.breakStart && data.breakEnd) {
        const breakStartTime = new Date(`${data.date}T${data.breakStart}:00`);
        const breakEndTime = new Date(`${data.date}T${data.breakEnd}:00`);
        const breakHours =
          (breakEndTime.getTime() - breakStartTime.getTime()) /
          (1000 * 60 * 60);
        totalHours -= breakHours;
      }

      hoursWorked = Math.max(0, totalHours);

      // Calculer les heures supplémentaires (au-delà de 8h)
      if (hoursWorked > 8) {
        overtimeHours = hoursWorked - 8;
      }
    }

    const attendance = await prisma.attendance.upsert({
      where: {
        employeeId_date: {
          employeeId: data.employeeId,
          date: new Date(data.date),
        },
      },
      update: {
        checkIn: data.checkIn
          ? new Date(`${data.date}T${data.checkIn}:00`)
          : undefined,
        checkOut: data.checkOut
          ? new Date(`${data.date}T${data.checkOut}:00`)
          : undefined,
        breakStart: data.breakStart
          ? new Date(`${data.date}T${data.breakStart}:00`)
          : undefined,
        breakEnd: data.breakEnd
          ? new Date(`${data.date}T${data.breakEnd}:00`)
          : undefined,
        hoursWorked,
        overtimeHours,
        status: data.status,
        notes: data.notes,
      },
      create: {
        employeeId: data.employeeId,
        organizationId: session.user.organizationId,
        date: new Date(data.date),
        checkIn: data.checkIn
          ? new Date(`${data.date}T${data.checkIn}:00`)
          : undefined,
        checkOut: data.checkOut
          ? new Date(`${data.date}T${data.checkOut}:00`)
          : undefined,
        breakStart: data.breakStart
          ? new Date(`${data.date}T${data.breakStart}:00`)
          : undefined,
        breakEnd: data.breakEnd
          ? new Date(`${data.date}T${data.breakEnd}:00`)
          : undefined,
        hoursWorked,
        overtimeHours,
        status: data.status,
        notes: data.notes,
      },
    });

    revalidatePath("/dashboard/hr/attendance");
    return { success: true, attendance };
  } catch (error) {
    console.error("Erreur lors de la gestion de la présence:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les présences
export async function getAttendances(date?: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (date) {
      where.date = new Date(date);
    }

    const attendances = await prisma.attendance.findMany({
      where,
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            employeeNumber: true,
            department: {
              select: {
                name: true,
              },
            },
            position: {
              select: {
                title: true,
              },
            },
          },
        },
      },
      orderBy: [{ date: "desc" }, { employee: { lastName: "asc" } }],
    });

    return { success: true, attendances };
  } catch (error) {
    console.error("Erreur lors de la récupération des présences:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== DEMANDES DE CONGÉS =====

export interface LeaveRequestFormData {
  employeeId: string;
  leaveType: LeaveType;
  startDate: string;
  endDate: string;
  reason: string;
  attachments?: string;
}

// Créer une demande de congé
export async function createLeaveRequest(data: LeaveRequestFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Générer le numéro de demande
    const today = new Date();
    const year = today.getFullYear();

    const todayRequestsCount = await prisma.leaveRequest.count({
      where: {
        organizationId: session.user.organizationId,
        createdAt: {
          gte: new Date(year, 0, 1),
          lt: new Date(year + 1, 0, 1),
        },
      },
    });

    const requestNumber = `LEAVE-${year}-${String(
      todayRequestsCount + 1
    ).padStart(4, "0")}`;

    // Calculer le nombre de jours
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const totalDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

    const leaveRequest = await prisma.leaveRequest.create({
      data: {
        ...data,
        requestNumber,
        organizationId: session.user.organizationId,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        totalDays,
      },
    });

    revalidatePath("/dashboard/hr/leaves");
    return { success: true, leaveRequest };
  } catch (error) {
    console.error("Erreur lors de la création de la demande de congé:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les demandes de congés
export async function getLeaveRequests(filters?: {
  status?: LeaveStatus | "ALL";
  employeeId?: string;
}) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    // Filtres
    if (filters?.status && filters.status !== "ALL") {
      where.status = filters.status;
    }

    if (filters?.employeeId) {
      where.employeeId = filters.employeeId;
    }

    const leaveRequests = await prisma.leaveRequest.findMany({
      where,
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            employeeNumber: true,
            department: {
              select: {
                name: true,
              },
            },
            position: {
              select: {
                title: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { success: true, leaveRequests };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des demandes de congés:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Approuver/Refuser une demande de congé
export async function updateLeaveRequestStatus(
  id: string,
  status: LeaveStatus,
  rejectionReason?: string
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.leaveRequest.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status,
        approvedBy: session.user.id,
        approvedAt: new Date(),
        rejectionReason,
      },
    });

    revalidatePath("/dashboard/hr/leaves");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la demande:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
