"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { LabOrderStatus, LabUrgency, PaymentMethod } from "@prisma/client";
import { checkPermission, logAction } from "./permissions";

// ===== TYPES D'EXAMENS =====

export interface LabTestTypeFormData {
  name: string;
  code: string;
  category: string;
  price: number;
  description?: string;
  normalValues?: string;
  sampleType?: string;
  preparation?: string;
}

// Créer un type d'examen
export async function createLabTestType(data: LabTestTypeFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "LABORATORY"
    );

    if (!canRead) {
      await logAction("READ", "LABORATORY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canApprove = await checkPermission(
      session.user.id,
      "APPROVE",
      "LABORATORY",
      id
    );

    if (!canApprove) {
      await logAction("APPROVE", "LABORATORY", id, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de approuver le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "LABORATORY"
    );

    if (!canCreate) {
      await logAction("CREATE", "LABORATORY", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "LABORATORY"
    );

    if (!canCreate) {
      await logAction("CREATE", "LABORATORY", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canUpdate = await checkPermission(
      session.user.id,
      "UPDATE",
      "LABORATORY",
      id
    );

    if (!canUpdate) {
      await logAction("UPDATE", "LABORATORY", id, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de modifier le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "LABORATORY"
    );

    if (!canRead) {
      await logAction("READ", "LABORATORY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "LABORATORY"
    );

    if (!canRead) {
      await logAction("READ", "LABORATORY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "LABORATORY"
    );

    if (!canCreate) {
      await logAction("CREATE", "LABORATORY", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "LABORATORY"
    );

    if (!canRead) {
      await logAction("READ", "LABORATORY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter le laboratoire");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "LABORATORY"
    );

    if (!canCreate) {
      await logAction("CREATE", "LABORATORY", undefined, undefined, data, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de créer le laboratoire");
    }

    // Vérifier que le code n'existe pas déjà
    const existingTest = await prisma.labTestType.findFirst({
      where: {
        organizationId: session.user.organizationId,
        code: data.code,
      },
    });

    if (existingTest) {
      throw new Error("Un examen avec ce code existe déjà");
    }

    const labTestType = await prisma.labTestType.create({
      data: {
        ...data,
        organizationId: session.user.organizationId,
      },
    });

    revalidatePath("/dashboard/laboratory");
    return { success: true, labTestType };
  } catch (error) {
    console.error("Erreur lors de la création du type d'examen:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les types d'examens
export async function getLabTestTypes() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const labTestTypes = await prisma.labTestType.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      orderBy: [{ category: "asc" }, { name: "asc" }],
    });

    return { success: true, labTestTypes };
  } catch (error) {
    console.error("Erreur lors de la récupération des types d'examens:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== PRESCRIPTIONS D'EXAMENS =====

export interface LabOrderFormData {
  patientId: string;
  testTypeId: string;
  consultationId?: string;
  urgency: LabUrgency;
  clinicalInfo?: string;
  instructions?: string;
  isExternal?: boolean;
}

// Créer une prescription d'examen
export async function createLabOrder(data: LabOrderFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId || !session?.user?.id) {
      throw new Error("Session utilisateur invalide");
    }

    // Générer le numéro de prescription
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");

    // Compter les prescriptions du jour
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const todayEnd = new Date(todayStart);
    todayEnd.setDate(todayEnd.getDate() + 1);

    const todayOrdersCount = await prisma.labOrder.count({
      where: {
        organizationId: session.user.organizationId,
        createdAt: {
          gte: todayStart,
          lt: todayEnd,
        },
      },
    });

    const orderNumber = `LAB-${dateStr}-${String(todayOrdersCount + 1).padStart(
      4,
      "0"
    )}`;

    const labOrder = await prisma.labOrder.create({
      data: {
        patientId: data.patientId,
        testTypeId: data.testTypeId,
        consultationId: data.consultationId,
        urgency: data.urgency,
        clinicalInfo: data.clinicalInfo,
        instructions: data.instructions,
        orderNumber,
        doctorId: session.user.id,
        organizationId: session.user.organizationId,
        status: data.isExternal ? "EXTERNAL" : "PENDING_PAYMENT",
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        testType: {
          select: {
            name: true,
            price: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/laboratory");
    return { success: true, labOrder };
  } catch (error) {
    console.error(
      "Erreur lors de la création de la prescription d'examen:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les prescriptions d'examens
export async function getLabOrders(filters?: {
  status?: LabOrderStatus | "ALL";
  patientId?: string;
  testTypeId?: string;
  dateFrom?: string;
  dateTo?: string;
}) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    // Filtres
    if (filters?.status && filters.status !== "ALL") {
      where.status = filters.status;
    }

    if (filters?.patientId) {
      where.patientId = filters.patientId;
    }

    if (filters?.testTypeId) {
      where.testTypeId = filters.testTypeId;
    }

    if (filters?.dateFrom || filters?.dateTo) {
      where.orderDate = {};
      if (filters.dateFrom) {
        where.orderDate.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.orderDate.lte = new Date(filters.dateTo);
      }
    }

    const labOrders = await prisma.labOrder.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
          },
        },
        testType: {
          select: {
            name: true,
            category: true,
            price: true,
            sampleType: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        result: {
          select: {
            id: true,
            validatedAt: true,
          },
        },
        payment: {
          select: {
            id: true,
            amount: true,
            paymentDate: true,
          },
        },
      },
      orderBy: {
        orderDate: "desc",
      },
    });

    return { success: true, labOrders };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des prescriptions d'examens:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer les examens en attente de paiement
export async function getPendingPaymentLabOrders() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const labOrders = await prisma.labOrder.findMany({
      where: {
        organizationId: session.user.organizationId,
        status: "PENDING_PAYMENT",
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
          },
        },
        testType: {
          select: {
            name: true,
            category: true,
            price: true,
            sampleType: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        orderDate: "asc",
      },
    });

    return { success: true, labOrders };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des examens en attente:",
      error
    );
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour le statut d'une prescription d'examen
export async function updateLabOrderStatus(id: string, status: LabOrderStatus) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.labOrder.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status,
        ...(status === "SAMPLE_TAKEN" && { sampleDate: new Date() }),
        ...(status === "COMPLETED" && { resultDate: new Date() }),
      },
    });

    revalidatePath("/dashboard/laboratory");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== PAIEMENTS LABORATOIRE =====

export interface LabPaymentFormData {
  labOrderId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  reference?: string;
  notes?: string;
}

// Créer un paiement pour un examen de laboratoire
export async function createLabPayment(data: LabPaymentFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que l'examen existe et est en attente de paiement
    const labOrder = await prisma.labOrder.findFirst({
      where: {
        id: data.labOrderId,
        organizationId: session.user.organizationId,
        status: "PENDING_PAYMENT",
      },
      include: {
        testType: true,
      },
    });

    if (!labOrder) {
      throw new Error("Examen non trouvé ou déjà payé");
    }

    // Générer le numéro de paiement
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");

    const todayPaymentsCount = await prisma.labPayment.count({
      where: {
        organizationId: session.user.organizationId,
        createdAt: {
          gte: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
          lt: new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate() + 1
          ),
        },
      },
    });

    const paymentNumber = `LABPAY-${dateStr}-${String(
      todayPaymentsCount + 1
    ).padStart(4, "0")}`;

    // Créer le paiement et mettre à jour le statut de l'examen
    const [labPayment] = await prisma.$transaction([
      prisma.labPayment.create({
        data: {
          ...data,
          paymentNumber,
          organizationId: session.user.organizationId,
        },
      }),
      prisma.labOrder.update({
        where: { id: data.labOrderId },
        data: { status: "PAID" },
      }),
    ]);

    revalidatePath("/dashboard/laboratory");
    return { success: true, labPayment };
  } catch (error) {
    console.error("Erreur lors de la création du paiement:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== RÉSULTATS DE LABORATOIRE =====

export interface LabResultFormData {
  labOrderId: string;
  values: Record<string, any>;
  interpretation?: string;
  conclusion?: string;
  technician?: string;
  comments?: string;
}

// Créer ou mettre à jour un résultat de laboratoire
export async function createOrUpdateLabResult(data: LabResultFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que l'examen existe et est en cours
    const labOrder = await prisma.labOrder.findFirst({
      where: {
        id: data.labOrderId,
        organizationId: session.user.organizationId,
        status: { in: ["SAMPLE_TAKEN", "IN_PROGRESS"] },
      },
    });

    if (!labOrder) {
      throw new Error("Examen non trouvé ou pas en cours d'analyse");
    }

    // Créer ou mettre à jour le résultat
    const labResult = await prisma.labResult.upsert({
      where: {
        labOrderId: data.labOrderId,
      },
      update: {
        values: data.values,
        interpretation: data.interpretation,
        conclusion: data.conclusion,
        technician: data.technician,
        comments: data.comments,
      },
      create: {
        labOrderId: data.labOrderId,
        organizationId: session.user.organizationId,
        values: data.values,
        interpretation: data.interpretation,
        conclusion: data.conclusion,
        technician: data.technician,
        comments: data.comments,
      },
    });

    // Mettre à jour le statut de l'examen
    await prisma.labOrder.update({
      where: { id: data.labOrderId },
      data: { status: "IN_PROGRESS" },
    });

    revalidatePath("/dashboard/laboratory");
    return { success: true, labResult };
  } catch (error) {
    console.error("Erreur lors de la création/mise à jour du résultat:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Valider un résultat de laboratoire
export async function validateLabResult(
  labOrderId: string,
  validatedBy: string
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Mettre à jour le résultat et l'examen
    await prisma.$transaction([
      prisma.labResult.update({
        where: {
          labOrderId,
        },
        data: {
          validatedBy,
          validatedAt: new Date(),
        },
      }),
      prisma.labOrder.update({
        where: {
          id: labOrderId,
          organizationId: session.user.organizationId,
        },
        data: {
          status: "COMPLETED",
          resultDate: new Date(),
        },
      }),
    ]);

    revalidatePath("/dashboard/laboratory");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la validation du résultat:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer un résultat de laboratoire
export async function getLabResult(labOrderId: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const labResult = await prisma.labResult.findFirst({
      where: {
        labOrderId,
        organizationId: session.user.organizationId,
      },
      include: {
        labOrder: {
          include: {
            patient: {
              select: {
                firstName: true,
                lastName: true,
                patientNumber: true,
                phone: true,
              },
            },
            testType: {
              select: {
                name: true,
                category: true,
                normalValues: true,
                sampleType: true,
              },
            },
            doctor: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    return { success: true, labResult };
  } catch (error) {
    console.error("Erreur lors de la récupération du résultat:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
