"use server";

import { prisma } from "@/lib/prisma";
import { hashPassword } from "@/lib/auth";
import { revalidatePath } from "next/cache";
import {
  CreateOrganizationData,
  ApiResponse,
  OrganizationWithStats,
} from "@/lib/types";
import { UserRole } from "@prisma/client";

// Interface pour l'onboarding complet
export interface OnboardingData {
  // Données de l'organisation
  organizationName: string;
  organizationType: "hospital" | "clinic" | "medical_center" | "pharmacy";
  organizationEmail: string;
  organizationPhone: string;
  organizationAddress: string;
  organizationCity: string;
  organizationCountry: string;

  // Données de l'administrateur principal
  adminFirstName: string;
  adminLastName: string;
  adminEmail: string;
  adminPhone: string;
  adminPassword: string;

  // Plan d'abonnement
  subscriptionPlan: "basic" | "premium" | "enterprise";
}

// Créer une organisation complète avec son administrateur
export async function createOrganizationWithAdmin(
  data: OnboardingData
): Promise<
  ApiResponse<{ organization: OrganizationWithStats; adminUser: any }>
> {
  try {
    // Vérifier que l'email admin n'existe pas déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: data.adminEmail },
    });

    if (existingUser) {
      return {
        success: false,
        error: "Un utilisateur avec cet email existe déjà",
      };
    }

    // Générer un slug unique pour l'organisation
    const baseSlug = data.organizationName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

    let slug = baseSlug;
    let counter = 1;

    while (await prisma.organization.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Définir les limites selon le plan
    const planLimits = {
      basic: { maxUsers: 10, maxPatients: 1000, maxStorage: 5 },
      premium: { maxUsers: 50, maxPatients: 5000, maxStorage: 25 },
      enterprise: { maxUsers: 200, maxPatients: 20000, maxStorage: 100 },
    };

    const limits = planLimits[data.subscriptionPlan];

    // Créer l'organisation et l'administrateur dans une transaction
    const result = await prisma.$transaction(async (tx) => {
      // Créer l'organisation
      const organization = await tx.organization.create({
        data: {
          name: data.organizationName,
          slug,
          type: data.organizationType,
          email: data.organizationEmail,
          phone: data.organizationPhone,
          address: data.organizationAddress,
          city: data.organizationCity,
          country: data.organizationCountry,
          subscriptionPlan: data.subscriptionPlan,
          subscriptionStatus: "active",
          trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
          maxUsers: limits.maxUsers,
          maxPatients: limits.maxPatients,
          maxStorage: limits.maxStorage,
          isActive: true,
        },
        include: {
          _count: {
            select: {
              users: true,
              patients: true,
              consultations: true,
            },
          },
        },
      });

      // Hacher le mot de passe
      const hashedPassword = await hashPassword(data.adminPassword);

      // Créer l'utilisateur administrateur
      const adminUser = await tx.user.create({
        data: {
          email: data.adminEmail,
          password: hashedPassword,
          firstName: data.adminFirstName,
          lastName: data.adminLastName,
          phone: data.adminPhone,
          role: UserRole.ADMIN,
          organizationId: organization.id,
          isActive: true,
        },
        include: {
          organization: true,
        },
      });

      return { organization, adminUser };
    });

    // Envoyer un email de bienvenue (à implémenter)
    // await sendWelcomeEmail(result.adminUser.email, result.organization.name)

    revalidatePath("/admin/organizations");

    return {
      success: true,
      data: result,
      message: `Organisation "${data.organizationName}" créée avec succès`,
    };
  } catch (error) {
    console.error("Erreur lors de la création de l'organisation:", error);
    return {
      success: false,
      error: "Erreur lors de la création de l'organisation",
    };
  }
}

// Inviter un utilisateur dans une organisation
export async function inviteUserToOrganization(
  organizationId: string,
  inviterUserId: string,
  userData: {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    phone?: string;
  }
): Promise<ApiResponse> {
  try {
    // Vérifier que l'inviteur a les permissions
    const inviter = await prisma.user.findUnique({
      where: { id: inviterUserId },
      include: { organization: true },
    });

    if (!inviter || inviter.organizationId !== organizationId) {
      return {
        success: false,
        error: "Permissions insuffisantes",
      };
    }

    if (inviter.role !== "ADMIN" && inviter.role !== "SUPER_ADMIN") {
      return {
        success: false,
        error: "Seuls les administrateurs peuvent inviter des utilisateurs",
      };
    }

    // Vérifier les limites de l'organisation
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { _count: { select: { users: true } } },
    });

    if (!organization) {
      return {
        success: false,
        error: "Organisation non trouvée",
      };
    }

    if (organization._count.users >= organization.maxUsers) {
      return {
        success: false,
        error: `Limite d'utilisateurs atteinte (${organization.maxUsers})`,
      };
    }

    // Vérifier que l'email n'existe pas déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (existingUser) {
      return {
        success: false,
        error: "Un utilisateur avec cet email existe déjà",
      };
    }

    // Générer un mot de passe temporaire
    const tempPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = await hashPassword(tempPassword);

    // Créer l'utilisateur
    const newUser = await prisma.user.create({
      data: {
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        role: userData.role,
        organizationId,
        isActive: true,
      },
    });

    // Envoyer un email d'invitation avec le mot de passe temporaire
    // await sendInvitationEmail(userData.email, tempPassword, organization.name)

    revalidatePath("/dashboard/users");

    return {
      success: true,
      message: `Invitation envoyée à ${userData.email}`,
    };
  } catch (error) {
    console.error("Erreur lors de l'invitation:", error);
    return {
      success: false,
      error: "Erreur lors de l'invitation de l'utilisateur",
    };
  }
}
