'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { 
  CreateOrganizationData, 
  UpdateOrganizationData,
  ApiResponse,
  OrganizationWithStats,
  OrganizationStats
} from '@/lib/types'

// Créer une nouvelle organisation
export async function createOrganization(
  data: CreateOrganizationData
): Promise<ApiResponse<OrganizationWithStats>> {
  try {
    // Vérifier que le slug est unique
    const existingOrg = await prisma.organization.findUnique({
      where: { slug: data.slug }
    })

    if (existingOrg) {
      return {
        success: false,
        error: 'Ce nom d\'organisation est déjà utilisé'
      }
    }

    // Créer l'organisation
    const organization = await prisma.organization.create({
      data: {
        ...data,
        subscriptionPlan: data.subscriptionPlan || 'basic',
        trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 jours d'essai
      },
      include: {
        _count: {
          select: {
            users: true,
            patients: true,
            consultations: true
          }
        }
      }
    })

    revalidatePath('/admin/organizations')
    
    return {
      success: true,
      data: organization,
      message: 'Organisation créée avec succès'
    }
  } catch (error) {
    console.error('Erreur lors de la création de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la création de l\'organisation'
    }
  }
}

// Mettre à jour une organisation
export async function updateOrganization(
  data: UpdateOrganizationData
): Promise<ApiResponse<OrganizationWithStats>> {
  try {
    const organization = await prisma.organization.update({
      where: { id: data.id },
      data,
      include: {
        _count: {
          select: {
            users: true,
            patients: true,
            consultations: true
          }
        }
      }
    })

    revalidatePath('/admin/organizations')
    revalidatePath(`/admin/organizations/${data.id}`)
    
    return {
      success: true,
      data: organization,
      message: 'Organisation mise à jour avec succès'
    }
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la mise à jour de l\'organisation'
    }
  }
}

// Récupérer une organisation par ID
export async function getOrganizationById(
  id: string
): Promise<ApiResponse<OrganizationWithStats>> {
  try {
    const organization = await prisma.organization.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            users: true,
            patients: true,
            consultations: true,
            prescriptions: true
          }
        }
      }
    })

    if (!organization) {
      return {
        success: false,
        error: 'Organisation non trouvée'
      }
    }

    return {
      success: true,
      data: organization
    }
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la récupération de l\'organisation'
    }
  }
}

// Récupérer une organisation par slug
export async function getOrganizationBySlug(
  slug: string
): Promise<ApiResponse<OrganizationWithStats>> {
  try {
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        _count: {
          select: {
            users: true,
            patients: true,
            consultations: true,
            prescriptions: true
          }
        }
      }
    })

    if (!organization) {
      return {
        success: false,
        error: 'Organisation non trouvée'
      }
    }

    return {
      success: true,
      data: organization
    }
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la récupération de l\'organisation'
    }
  }
}

// Récupérer toutes les organisations (pour admin)
export async function getAllOrganizations(
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<{ organizations: OrganizationWithStats[], total: number, pages: number }>> {
  try {
    const skip = (page - 1) * limit

    const [organizations, total] = await Promise.all([
      prisma.organization.findMany({
        include: {
          _count: {
            select: {
              users: true,
              patients: true,
              consultations: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.organization.count()
    ])

    const pages = Math.ceil(total / limit)

    return {
      success: true,
      data: {
        organizations,
        total,
        pages
      }
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des organisations:', error)
    return {
      success: false,
      error: 'Erreur lors de la récupération des organisations'
    }
  }
}

// Récupérer les statistiques d'une organisation
export async function getOrganizationStats(
  organizationId: string
): Promise<ApiResponse<OrganizationStats>> {
  try {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))
    const startOfDay = new Date()
    startOfDay.setHours(0, 0, 0, 0)

    // Statistiques des patients
    const [
      totalPatients,
      activePatients,
      newPatientsThisMonth,
      patientsByGender
    ] = await Promise.all([
      prisma.patient.count({ where: { organizationId } }),
      prisma.patient.count({ where: { organizationId, isActive: true } }),
      prisma.patient.count({ 
        where: { 
          organizationId, 
          createdAt: { gte: startOfMonth } 
        } 
      }),
      prisma.patient.groupBy({
        by: ['gender'],
        where: { organizationId },
        _count: true
      })
    ])

    // Statistiques des consultations
    const [
      totalConsultations,
      consultationsToday,
      consultationsThisWeek,
      consultationsThisMonth,
      consultationsByStatus
    ] = await Promise.all([
      prisma.consultation.count({ where: { organizationId } }),
      prisma.consultation.count({ 
        where: { 
          organizationId, 
          consultationDate: { gte: startOfDay } 
        } 
      }),
      prisma.consultation.count({ 
        where: { 
          organizationId, 
          consultationDate: { gte: startOfWeek } 
        } 
      }),
      prisma.consultation.count({ 
        where: { 
          organizationId, 
          consultationDate: { gte: startOfMonth } 
        } 
      }),
      prisma.consultation.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true
      })
    ])

    // Statistiques de revenus
    const [
      totalRevenue,
      revenueThisMonth,
      pendingRevenue,
      paidRevenue
    ] = await Promise.all([
      prisma.consultation.aggregate({
        where: { organizationId },
        _sum: { consultationFee: true }
      }),
      prisma.consultation.aggregate({
        where: { 
          organizationId, 
          consultationDate: { gte: startOfMonth } 
        },
        _sum: { consultationFee: true }
      }),
      prisma.consultation.aggregate({
        where: { organizationId, paymentStatus: 'PENDING' },
        _sum: { consultationFee: true }
      }),
      prisma.consultation.aggregate({
        where: { organizationId, paymentStatus: 'PAID' },
        _sum: { consultationFee: true }
      })
    ])

    // Statistiques des utilisateurs
    const [totalUsers, activeUsers, usersByRole] = await Promise.all([
      prisma.user.count({ where: { organizationId } }),
      prisma.user.count({ where: { organizationId, isActive: true } }),
      prisma.user.groupBy({
        by: ['role'],
        where: { organizationId },
        _count: true
      })
    ])

    const stats: OrganizationStats = {
      patients: {
        total: totalPatients,
        active: activePatients,
        newThisMonth: newPatientsThisMonth,
        byGender: patientsByGender.reduce((acc, item) => {
          acc[item.gender] = item._count
          return acc
        }, {} as Record<string, number>)
      },
      consultations: {
        total: totalConsultations,
        today: consultationsToday,
        thisWeek: consultationsThisWeek,
        thisMonth: consultationsThisMonth,
        byStatus: consultationsByStatus.reduce((acc, item) => {
          acc[item.status] = item._count
          return acc
        }, {} as Record<string, number>)
      },
      revenue: {
        total: totalRevenue._sum.consultationFee || 0,
        thisMonth: revenueThisMonth._sum.consultationFee || 0,
        pending: pendingRevenue._sum.consultationFee || 0,
        paid: paidRevenue._sum.consultationFee || 0
      },
      users: {
        total: totalUsers,
        active: activeUsers,
        byRole: usersByRole.reduce((acc, item) => {
          acc[item.role] = item._count
          return acc
        }, {} as Record<string, number>)
      }
    }

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)
    return {
      success: false,
      error: 'Erreur lors de la récupération des statistiques'
    }
  }
}

// Suspendre une organisation
export async function suspendOrganization(
  id: string
): Promise<ApiResponse> {
  try {
    await prisma.organization.update({
      where: { id },
      data: { 
        subscriptionStatus: 'suspended',
        isActive: false 
      }
    })

    revalidatePath('/admin/organizations')
    
    return {
      success: true,
      message: 'Organisation suspendue avec succès'
    }
  } catch (error) {
    console.error('Erreur lors de la suspension de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la suspension de l\'organisation'
    }
  }
}

// Réactiver une organisation
export async function reactivateOrganization(
  id: string
): Promise<ApiResponse> {
  try {
    await prisma.organization.update({
      where: { id },
      data: { 
        subscriptionStatus: 'active',
        isActive: true 
      }
    })

    revalidatePath('/admin/organizations')
    
    return {
      success: true,
      message: 'Organisation réactivée avec succès'
    }
  } catch (error) {
    console.error('Erreur lors de la réactivation de l\'organisation:', error)
    return {
      success: false,
      error: 'Erreur lors de la réactivation de l\'organisation'
    }
  }
}
