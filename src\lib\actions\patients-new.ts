"use server";

import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Gender, BloodType, MaritalStatus } from "@prisma/client";
import { checkPermission, logAction } from "./permissions";

// Types pour les données patients
export interface PatientFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: Gender;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country: string;
  bloodType: BloodType;
  allergies?: string;
  chronicDiseases?: string;
  emergencyContact?: string;
  maritalStatus: MaritalStatus;
  occupation?: string;
  insurance?: string;
  notes?: string;
}

export interface PatientFilters {
  search?: string;
  gender?: Gender;
  bloodType?: BloodType;
  isActive?: boolean;
}

// Générer un numéro de patient unique
async function generatePatientNumber(organizationId: string): Promise<string> {
  const currentYear = new Date().getFullYear();
  const prefix = `PAT-${currentYear}`;

  // Compter les patients existants cette année
  const count = await prisma.patient.count({
    where: {
      organizationId,
      patientNumber: {
        startsWith: prefix,
      },
    },
  });

  const nextNumber = (count + 1).toString().padStart(4, "0");
  return `${prefix}-${nextNumber}`;
}

// Créer un nouveau patient
export async function createPatient(data: PatientFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canCreate = await checkPermission(
      session.user.id,
      "CREATE",
      "PATIENTS"
    );

    if (!canCreate) {
      await logAction(
        "CREATE",
        "PATIENTS",
        undefined,
        undefined,
        data,
        false,
        "Permission refusée"
      );
      throw new Error("Vous n'avez pas l'autorisation de créer des patients");
    }

    // Générer le numéro de patient
    const patientNumber = await generatePatientNumber(
      session.user.organizationId
    );

    // Créer le patient
    const patient = await prisma.patient.create({
      data: {
        ...data,
        dateOfBirth: new Date(data.dateOfBirth),
        patientNumber,
        organizationId: session.user.organizationId,
        createdById: session.user.id,
      },
      include: {
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            consultations: true,
            prescriptions: true,
          },
        },
      },
    });

    // 📝 AUDIT DE SUCCÈS
    await logAction("CREATE", "PATIENTS", patient.id, undefined, {
      patientNumber: patient.patientNumber,
      firstName: patient.firstName,
      lastName: patient.lastName,
    });

    revalidatePath("/dashboard/patients");
    return { success: true, patient };
  } catch (error) {
    console.error("Erreur lors de la création du patient:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer la liste des patients avec filtres
export async function getPatients(
  filters: PatientFilters = {},
  page = 1,
  limit = 20
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(session.user.id, "READ", "PATIENTS");

    if (!canRead) {
      await logAction(
        "READ",
        "PATIENTS",
        undefined,
        undefined,
        undefined,
        false,
        "Permission refusée"
      );
      throw new Error(
        "Vous n'avez pas l'autorisation de consulter les patients"
      );
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    // Filtres de recherche
    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { patientNumber: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
        { phone: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    if (filters.gender) {
      where.gender = filters.gender;
    }

    if (filters.bloodType) {
      where.bloodType = filters.bloodType;
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        include: {
          createdBy: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
          _count: {
            select: {
              consultations: true,
              prescriptions: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.patient.count({ where }),
    ]);

    return {
      success: true,
      patients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des patients:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer un patient par ID
export async function getPatientById(id: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const patient = await prisma.patient.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        updatedBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        consultations: {
          include: {
            doctor: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { consultationDate: "desc" },
          take: 5,
        },
        prescriptions: {
          include: {
            doctor: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { prescriptionDate: "desc" },
          take: 5,
        },
        medicalHistory: {
          orderBy: { date: "desc" },
          take: 10,
        },
        _count: {
          select: {
            consultations: true,
            prescriptions: true,
            medicalHistory: true,
          },
        },
      },
    });

    if (!patient) {
      throw new Error("Patient non trouvé");
    }

    return { success: true, patient };
  } catch (error) {
    console.error("Erreur lors de la récupération du patient:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour un patient
export async function updatePatient(
  id: string,
  data: Partial<PatientFormData>
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canUpdate = await checkPermission(
      session.user.id,
      "UPDATE",
      "PATIENTS",
      id
    );

    if (!canUpdate) {
      await logAction(
        "UPDATE",
        "PATIENTS",
        id,
        undefined,
        data,
        false,
        "Permission refusée"
      );
      throw new Error("Vous n'avez pas l'autorisation de modifier ce patient");
    }

    const updateData: any = { ...data };
    if (data.dateOfBirth) {
      updateData.dateOfBirth = new Date(data.dateOfBirth);
    }
    updateData.updatedById = session.user.id;

    const patient = await prisma.patient.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: updateData,
      include: {
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        updatedBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/patients");
    revalidatePath(`/dashboard/patients/${id}`);
    return { success: true, patient };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du patient:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Supprimer un patient (soft delete)
export async function deletePatient(id: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.patient.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        isActive: false,
        updatedById: session.user.id,
      },
    });

    revalidatePath("/dashboard/patients");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la suppression du patient:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
