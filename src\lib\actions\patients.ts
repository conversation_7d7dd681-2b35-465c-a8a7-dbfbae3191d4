"use server";

import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Gender, BloodType, MaritalStatus } from "@prisma/client";
import { checkPermission, logAction } from "./permissions";
import { PERMISSIONS } from "@/lib/middleware/permissions";

// Types pour les données patients
export interface PatientFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: Gender;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country: string;
  bloodType: BloodType;
  allergies?: string;
  chronicDiseases?: string;
  emergencyContact?: string;
  maritalStatus: MaritalStatus;
  occupation?: string;
  insurance?: string;
  notes?: string;
}

export interface PatientFilters {
  search?: string;
  gender?: Gender;
  bloodType?: BloodType;
  isActive?: boolean;
  ageRange?: {
    min?: number;
    max?: number;
  };
}

// Générer un numéro de patient unique pour l'organisation
async function generatePatientNumber(organizationId: string): Promise<string> {
  const count = await prisma.patient.count({
    where: { organizationId },
  });

  const year = new Date().getFullYear();
  const number = (count + 1).toString().padStart(4, "0");

  return `PAT-${year}-${number}`;
}

// Créer un nouveau patient
export async function createPatient(
  data: CreatePatientData
): Promise<ApiResponse<PatientWithRelations>> {
  try {
    // Vérifier les limites de l'organisation
    const organization = await prisma.organization.findUnique({
      where: { id: data.organizationId },
      include: { _count: { select: { patients: true } } },
    });

    if (!organization) {
      return {
        success: false,
        error: "Organisation non trouvée",
      };
    }

    if (organization._count.patients >= organization.maxPatients) {
      return {
        success: false,
        error: `Limite de patients atteinte (${organization.maxPatients})`,
      };
    }

    // Générer le numéro de patient
    const patientNumber = await generatePatientNumber(data.organizationId);

    // Créer le patient
    const patient = await prisma.patient.create({
      data: {
        ...data,
        patientNumber,
        createdById: data.organizationId, // Temporaire, sera remplacé par l'ID de l'utilisateur connecté
      },
      include: {
        organization: true,
        createdBy: true,
        updatedBy: true,
        consultations: {
          orderBy: { consultationDate: "desc" },
          take: 5,
        },
        prescriptions: {
          orderBy: { createdAt: "desc" },
          take: 5,
        },
        medicalHistory: {
          orderBy: { date: "desc" },
          take: 10,
        },
      },
    });

    revalidatePath("/patients");

    return {
      success: true,
      data: patient,
      message: "Patient créé avec succès",
    };
  } catch (error) {
    console.error("Erreur lors de la création du patient:", error);
    return {
      success: false,
      error: "Erreur lors de la création du patient",
    };
  }
}

// Mettre à jour un patient
export async function updatePatient(
  data: UpdatePatientData
): Promise<ApiResponse<PatientWithRelations>> {
  try {
    const patient = await prisma.patient.update({
      where: { id: data.id },
      data: {
        ...data,
        updatedById: data.organizationId, // Temporaire, sera remplacé par l'ID de l'utilisateur connecté
      },
      include: {
        organization: true,
        createdBy: true,
        updatedBy: true,
        consultations: {
          orderBy: { consultationDate: "desc" },
          take: 5,
        },
        prescriptions: {
          orderBy: { createdAt: "desc" },
          take: 5,
        },
        medicalHistory: {
          orderBy: { date: "desc" },
          take: 10,
        },
      },
    });

    revalidatePath("/patients");
    revalidatePath(`/patients/${data.id}`);

    return {
      success: true,
      data: patient,
      message: "Patient mis à jour avec succès",
    };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du patient:", error);
    return {
      success: false,
      error: "Erreur lors de la mise à jour du patient",
    };
  }
}

// Récupérer un patient par ID
export async function getPatientById(
  id: string,
  organizationId: string
): Promise<ApiResponse<PatientWithRelations>> {
  try {
    const patient = await prisma.patient.findFirst({
      where: {
        id,
        organizationId,
      },
      include: {
        organization: true,
        createdBy: true,
        updatedBy: true,
        consultations: {
          orderBy: { consultationDate: "desc" },
          include: {
            doctor: true,
            prescriptions: true,
          },
        },
        prescriptions: {
          orderBy: { createdAt: "desc" },
          include: {
            doctor: true,
            consultation: true,
          },
        },
        medicalHistory: {
          orderBy: { date: "desc" },
        },
      },
    });

    if (!patient) {
      return {
        success: false,
        error: "Patient non trouvé",
      };
    }

    return {
      success: true,
      data: patient,
    };
  } catch (error) {
    console.error("Erreur lors de la récupération du patient:", error);
    return {
      success: false,
      error: "Erreur lors de la récupération du patient",
    };
  }
}

// Récupérer la liste des patients avec filtres
export async function getPatients(
  filters: PatientFilters,
  page: number = 1,
  limit: number = 20
): Promise<
  ApiResponse<{
    patients: PatientWithRelations[];
    total: number;
    pages: number;
  }>
> {
  try {
    const skip = (page - 1) * limit;

    const where = {
      organizationId: filters.organizationId,
      ...(filters.search && {
        OR: [
          {
            firstName: {
              contains: filters.search,
              mode: "insensitive" as const,
            },
          },
          {
            lastName: {
              contains: filters.search,
              mode: "insensitive" as const,
            },
          },
          {
            patientNumber: {
              contains: filters.search,
              mode: "insensitive" as const,
            },
          },
          { phone: { contains: filters.search, mode: "insensitive" as const } },
          { email: { contains: filters.search, mode: "insensitive" as const } },
        ],
      }),
      ...(filters.gender && { gender: filters.gender }),
      ...(filters.bloodType && { bloodType: filters.bloodType }),
      ...(filters.maritalStatus && { maritalStatus: filters.maritalStatus }),
      ...(filters.isActive !== undefined && { isActive: filters.isActive }),
      ...(filters.createdAfter && { createdAt: { gte: filters.createdAfter } }),
      ...(filters.createdBefore && {
        createdAt: { lte: filters.createdBefore },
      }),
    };

    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        include: {
          organization: true,
          createdBy: true,
          _count: {
            select: {
              consultations: true,
              prescriptions: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.patient.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    return {
      success: true,
      data: {
        patients,
        total,
        pages,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des patients:", error);
    return {
      success: false,
      error: "Erreur lors de la récupération des patients",
    };
  }
}

// Désactiver un patient
export async function deactivatePatient(
  id: string,
  organizationId: string
): Promise<ApiResponse> {
  try {
    await prisma.patient.update({
      where: {
        id,
        organizationId,
      },
      data: { isActive: false },
    });

    revalidatePath("/patients");

    return {
      success: true,
      message: "Patient désactivé avec succès",
    };
  } catch (error) {
    console.error("Erreur lors de la désactivation du patient:", error);
    return {
      success: false,
      error: "Erreur lors de la désactivation du patient",
    };
  }
}

// Réactiver un patient
export async function reactivatePatient(
  id: string,
  organizationId: string
): Promise<ApiResponse> {
  try {
    await prisma.patient.update({
      where: {
        id,
        organizationId,
      },
      data: { isActive: true },
    });

    revalidatePath("/patients");

    return {
      success: true,
      message: "Patient réactivé avec succès",
    };
  } catch (error) {
    console.error("Erreur lors de la réactivation du patient:", error);
    return {
      success: false,
      error: "Erreur lors de la réactivation du patient",
    };
  }
}

// Récupérer tous les patients pour une organisation (version simplifiée)
export async function getAllPatients() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const patients = await prisma.patient.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        patientNumber: true,
        phone: true,
        email: true,
      },
      orderBy: [{ firstName: "asc" }, { lastName: "asc" }],
    });

    return { success: true, patients };
  } catch (error) {
    console.error("Erreur lors de la récupération des patients:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
