"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import {
  UserRole,
  PermissionAction,
  PermissionResource,
} from "@prisma/client";

// ===== TYPES =====

export interface PermissionCheck {
  action: PermissionAction;
  resource: PermissionResource;
  resourceId?: string;
  conditions?: Record<string, any>;
}

export interface UserPermissionData {
  userId: string;
  permissionId: string;
  isGranted: boolean;
  reason?: string;
  expiresAt?: string;
}

// ===== VÉRIFICATION DES PERMISSIONS =====

export async function checkPermission(
  userId: string,
  action: PermissionAction,
  resource: PermissionResource,
  resourceId?: string
): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userPermissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    if (!user) return false;

    // Super Admin a tous les droits
    if (user.role === "SUPER_ADMIN") return true;

    // Vérifier les permissions spécifiques de l'utilisateur d'abord
    const userPermission = user.userPermissions.find(
      up => up.permission.action === action && up.permission.resource === resource
    );

    if (userPermission) {
      // Vérifier si la permission n'a pas expiré
      if (userPermission.expiresAt && userPermission.expiresAt < new Date()) {
        return false;
      }
      return userPermission.isGranted;
    }

    // Vérifier les permissions du rôle
    const rolePermission = await prisma.rolePermission.findFirst({
      where: {
        role: user.role,
        permission: {
          action,
          resource,
        },
        isGranted: true,
      },
      include: {
        permission: true,
      },
    });

    if (rolePermission) {
      // Appliquer les conditions si nécessaire
      return await applyPermissionConditions(
        user,
        rolePermission.permission,
        resourceId,
        rolePermission.conditions
      );
    }

    return false;
  } catch (error) {
    console.error("Erreur lors de la vérification des permissions:", error);
    return false;
  }
}

async function applyPermissionConditions(
  user: any,
  permission: any,
  resourceId?: string,
  conditions?: string
): Promise<boolean> {
  if (!conditions) return true;

  try {
    const conditionsObj = JSON.parse(conditions);

    // Condition: own_patients_only
    if (conditionsObj.own_patients_only && permission.resource === "PATIENTS") {
      if (!resourceId) return true; // Si pas de resourceId, on autorise (pour les listes)
      
      const patient = await prisma.patient.findFirst({
        where: {
          id: resourceId,
          createdById: user.id,
        },
      });
      
      return !!patient;
    }

    // Condition: own_consultations_only
    if (conditionsObj.own_consultations_only && permission.resource === "CONSULTATIONS") {
      if (!resourceId) return true;
      
      const consultation = await prisma.consultation.findFirst({
        where: {
          id: resourceId,
          doctorId: user.id,
        },
      });
      
      return !!consultation;
    }

    // Condition: department_only
    if (conditionsObj.department_only) {
      const employee = await prisma.employee.findFirst({
        where: { userId: user.id },
        include: { department: true },
      });

      if (!employee) return false;

      // Vérifier si la ressource appartient au même département
      // Cette logique dépend du type de ressource
      return true; // Simplifié pour l'exemple
    }

    return true;
  } catch (error) {
    console.error("Erreur lors de l'application des conditions:", error);
    return false;
  }
}

// ===== GESTION DES PERMISSIONS =====

export async function getAllPermissions() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que l'utilisateur peut gérer les permissions
    const canManage = await checkPermission(
      session.user.id,
      "MANAGE",
      "ROLES"
    );

    if (!canManage) {
      throw new Error("Permissions insuffisantes");
    }

    const permissions = await prisma.permission.findMany({
      where: { isActive: true },
      orderBy: [
        { resource: "asc" },
        { action: "asc" },
      ],
    });

    return { success: true, permissions };
  } catch (error) {
    console.error("Erreur lors de la récupération des permissions:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getRolePermissions(role: UserRole) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      throw new Error("Session utilisateur invalide");
    }

    const canManage = await checkPermission(
      session.user.id,
      "READ",
      "ROLES"
    );

    if (!canManage) {
      throw new Error("Permissions insuffisantes");
    }

    const rolePermissions = await prisma.rolePermission.findMany({
      where: { role },
      include: {
        permission: true,
      },
      orderBy: {
        permission: {
          resource: "asc",
        },
      },
    });

    return { success: true, rolePermissions };
  } catch (error) {
    console.error("Erreur lors de la récupération des permissions du rôle:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function updateRolePermission(
  role: UserRole,
  permissionId: string,
  isGranted: boolean,
  conditions?: string
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      throw new Error("Session utilisateur invalide");
    }

    const canManage = await checkPermission(
      session.user.id,
      "MANAGE",
      "ROLES"
    );

    if (!canManage) {
      throw new Error("Permissions insuffisantes");
    }

    await prisma.rolePermission.upsert({
      where: {
        role_permissionId: {
          role,
          permissionId,
        },
      },
      update: {
        isGranted,
        conditions,
      },
      create: {
        role,
        permissionId,
        isGranted,
        conditions,
      },
    });

    revalidatePath("/dashboard/settings/permissions");
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour des permissions du rôle:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function grantUserPermission(data: UserPermissionData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      throw new Error("Session utilisateur invalide");
    }

    const canManage = await checkPermission(
      session.user.id,
      "MANAGE",
      "USERS"
    );

    if (!canManage) {
      throw new Error("Permissions insuffisantes");
    }

    await prisma.userPermission.upsert({
      where: {
        userId_permissionId: {
          userId: data.userId,
          permissionId: data.permissionId,
        },
      },
      update: {
        isGranted: data.isGranted,
        reason: data.reason,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        grantedBy: session.user.id,
      },
      create: {
        userId: data.userId,
        permissionId: data.permissionId,
        isGranted: data.isGranted,
        reason: data.reason,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        grantedBy: session.user.id,
      },
    });

    revalidatePath("/dashboard/settings/users");
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de l'attribution de permission utilisateur:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== AUDIT =====

export async function logAction(
  action: string,
  resource: string,
  resourceId?: string,
  oldValues?: any,
  newValues?: any,
  success: boolean = true,
  errorMessage?: string
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) return;

    await prisma.auditLog.create({
      data: {
        organizationId: session.user.organizationId,
        userId: session.user.id,
        action,
        resource,
        resourceId,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        success,
        errorMessage,
      },
    });
  } catch (error) {
    console.error("Erreur lors de l'enregistrement de l'audit:", error);
  }
}

export async function getAuditLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: string;
    endDate?: string;
  }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const canView = await checkPermission(
      session.user.id,
      "READ",
      "SETTINGS"
    );

    if (!canView) {
      throw new Error("Permissions insuffisantes");
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (filters?.userId) where.userId = filters.userId;
    if (filters?.action) where.action = { contains: filters.action, mode: "insensitive" };
    if (filters?.resource) where.resource = { contains: filters.resource, mode: "insensitive" };
    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) where.createdAt.gte = new Date(filters.startDate);
      if (filters.endDate) where.createdAt.lte = new Date(filters.endDate);
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      success: true,
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des logs d'audit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
