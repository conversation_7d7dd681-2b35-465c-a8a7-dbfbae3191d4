"use server";

import { prisma } from "@/lib/prisma";
import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { checkPermission, logAction } from "./permissions";

// Types pour les actions de pharmacie
export interface PharmacyMovementData {
  medicationId: string;
  type: "IN" | "OUT" | "ADJUSTMENT";
  quantity: number;
  reason?: string;
  reference?: string;
  unitPrice?: number;
  expiryDate?: string;
  batchNumber?: string;
}

export interface DispensingData {
  prescriptionId: string;
  items: Array<{
    prescriptionItemId: string;
    medicationId: string;
    dispensedQuantity: number;
    unitPrice: number;
    batchNumber?: string;
    expiryDate?: string;
  }>;
  paymentMethod?: "CASH" | "CARD" | "MOBILE" | "INSURANCE" | "CREDIT";
  paymentReference?: string;
  discountAmount?: number;
  notes?: string;
}

// ===== GESTION DES STOCKS =====

// Obtenir le stock actuel d'un médicament
export async function getMedicationStock(medicationId: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      return { success: false, error: "Non autorisé" }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "PHARMACY"
    );

    if (!canRead) {
      await logAction("READ", "PHARMACY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter la pharmacie");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "PHARMACY"
    );

    if (!canRead) {
      await logAction("READ", "PHARMACY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter la pharmacie");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "PHARMACY"
    );

    if (!canRead) {
      await logAction("READ", "PHARMACY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter la pharmacie");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "PHARMACY"
    );

    if (!canRead) {
      await logAction("READ", "PHARMACY", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter la pharmacie");
    };
    }

    const medication = await prisma.medication.findFirst({
      where: {
        id: medicationId,
        organizationId: session.user.organizationId,
      },
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockLevel: true,
        maxStockLevel: true,
        price: true,
      },
    });

    if (!medication) {
      return { success: false, error: "Médicament non trouvé" };
    }

    return { success: true, stock: medication };
  } catch (error) {
    console.error("Erreur lors de la récupération du stock:", error);
    return { success: false, error: "Erreur serveur" };
  }
}

// Obtenir les médicaments avec stock faible
export async function getLowStockMedications() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      return { success: false, error: "Non autorisé" };
    }

    const medications = await prisma.medication.findMany({
      where: {
        organizationId: session.user.organizationId,
        isActive: true,
        stockQuantity: {
          lte: prisma.medication.fields.minStockLevel,
        },
      },
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockLevel: true,
        form: true,
        strength: true,
      },
      orderBy: {
        stockQuantity: "asc",
      },
    });

    return { success: true, medications };
  } catch (error) {
    console.error("Erreur lors de la récupération des stocks faibles:", error);
    return { success: false, error: "Erreur serveur" };
  }
}

// ===== MOUVEMENTS DE STOCK =====

// Créer un mouvement de stock
export async function createPharmacyMovement(data: PharmacyMovementData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId || !session?.user?.id) {
      return { success: false, error: "Non autorisé" };
    }

    // Vérifier que le médicament existe
    const medication = await prisma.medication.findFirst({
      where: {
        id: data.medicationId,
        organizationId: session.user.organizationId,
      },
    });

    if (!medication) {
      return { success: false, error: "Médicament non trouvé" };
    }

    const previousStock = medication.stockQuantity;
    let newStock: number;

    // Calculer le nouveau stock selon le type de mouvement
    switch (data.type) {
      case "IN":
        newStock = previousStock + Math.abs(data.quantity);
        break;
      case "OUT":
        newStock = previousStock - Math.abs(data.quantity);
        if (newStock < 0) {
          return { success: false, error: "Stock insuffisant" };
        }
        break;
      case "ADJUSTMENT":
        newStock = data.quantity; // Quantité absolue pour les ajustements
        break;
      default:
        return { success: false, error: "Type de mouvement invalide" };
    }

    // Créer le mouvement et mettre à jour le stock dans une transaction
    const result = await prisma.$transaction(async (tx) => {
      // Créer le mouvement
      const movement = await tx.pharmacyMovement.create({
        data: {
          organizationId: session.user.organizationId,
          medicationId: data.medicationId,
          type: data.type,
          quantity:
            data.type === "OUT"
              ? -Math.abs(data.quantity)
              : Math.abs(data.quantity),
          previousStock,
          newStock,
          reason: data.reason,
          reference: data.reference,
          unitPrice: data.unitPrice,
          totalValue: data.unitPrice
            ? data.unitPrice * Math.abs(data.quantity)
            : null,
          expiryDate: data.expiryDate ? new Date(data.expiryDate) : null,
          batchNumber: data.batchNumber,
          userId: session.user.id,
        },
      });

      // Mettre à jour le stock du médicament
      await tx.medication.update({
        where: { id: data.medicationId },
        data: { stockQuantity: newStock },
      });

      return movement;
    });

    revalidatePath("/dashboard/pharmacy");
    return { success: true, movement: result };
  } catch (error) {
    console.error("Erreur lors de la création du mouvement:", error);
    return { success: false, error: "Erreur serveur" };
  }
}

// Obtenir l'historique des mouvements
export async function getPharmacyMovements(params?: {
  medicationId?: string;
  type?: "IN" | "OUT" | "ADJUSTMENT";
  limit?: number;
  offset?: number;
}) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      return { success: false, error: "Non autorisé" };
    }

    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (params?.medicationId) {
      where.medicationId = params.medicationId;
    }

    if (params?.type) {
      where.type = params.type;
    }

    const movements = await prisma.pharmacyMovement.findMany({
      where,
      include: {
        medication: {
          select: {
            name: true,
            form: true,
            strength: true,
          },
        },
        user: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: params?.limit || 50,
      skip: params?.offset || 0,
    });

    return { success: true, movements };
  } catch (error) {
    console.error("Erreur lors de la récupération des mouvements:", error);
    return { success: false, error: "Erreur serveur" };
  }
}

// ===== DISPENSATION =====

// Obtenir les prescriptions en attente de dispensation
export async function getPendingPrescriptions() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      return { success: false, error: "Non autorisé" };
    }

    const prescriptions = await prisma.prescription.findMany({
      where: {
        organizationId: session.user.organizationId,
        status: "ACTIVE",
        items: {
          some: {
            isExternal: false, // Seulement les médicaments internes
            isDispensed: false,
          },
        },
      },
      include: {
        patient: {
          select: {
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        doctor: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        items: {
          where: {
            isExternal: false,
            isDispensed: false,
          },
          include: {
            medication: {
              select: {
                id: true,
                name: true,
                form: true,
                strength: true,
                stockQuantity: true,
                price: true,
              },
            },
          },
        },
      },
      orderBy: {
        prescriptionDate: "desc",
      },
    });

    // Filtrer les prescriptions qui ont au moins un médicament interne non dispensé
    const filteredPrescriptions = prescriptions.filter(
      (prescription) => prescription.items.length > 0
    );

    return { success: true, prescriptions: filteredPrescriptions };
  } catch (error) {
    console.error("Erreur lors de la récupération des prescriptions:", error);
    return { success: false, error: "Erreur serveur" };
  }
}

// Générer un numéro de dispensation unique
async function generateDispensingNumber(
  organizationId: string
): Promise<string> {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");

  const prefix = `DISP-${year}${month}${day}`;

  // Trouver le dernier numéro du jour
  const lastDispensing = await prisma.dispensing.findFirst({
    where: {
      organizationId,
      dispensingNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      dispensingNumber: "desc",
    },
  });

  let sequence = 1;
  if (lastDispensing) {
    const lastSequence = parseInt(
      lastDispensing.dispensingNumber.split("-").pop() || "0"
    );
    sequence = lastSequence + 1;
  }

  return `${prefix}-${String(sequence).padStart(4, "0")}`;
}

// Créer une dispensation
export async function createDispensing(data: DispensingData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId || !session?.user?.id) {
      return { success: false, error: "Non autorisé" };
    }

    // Vérifier que la prescription existe
    const prescription = await prisma.prescription.findFirst({
      where: {
        id: data.prescriptionId,
        organizationId: session.user.organizationId,
      },
      include: {
        items: {
          include: {
            medication: true,
          },
        },
      },
    });

    if (!prescription) {
      return { success: false, error: "Prescription non trouvée" };
    }

    // Vérifier les stocks disponibles
    for (const item of data.items) {
      const prescriptionItem = prescription.items.find(
        (pi) => pi.id === item.prescriptionItemId
      );
      if (!prescriptionItem || !prescriptionItem.medication) {
        return { success: false, error: "Item de prescription invalide" };
      }

      if (prescriptionItem.medication.stockQuantity < item.dispensedQuantity) {
        return {
          success: false,
          error: `Stock insuffisant pour ${prescriptionItem.medication.name}`,
        };
      }
    }

    // Générer le numéro de dispensation
    const dispensingNumber = await generateDispensingNumber(
      session.user.organizationId
    );

    // Calculer le montant total
    const totalAmount = data.items.reduce(
      (sum, item) => sum + item.unitPrice * item.dispensedQuantity,
      0
    );
    const finalAmount = totalAmount - (data.discountAmount || 0);

    // Créer la dispensation dans une transaction
    const result = await prisma.$transaction(async (tx) => {
      // Créer la dispensation
      const dispensing = await tx.dispensing.create({
        data: {
          organizationId: session.user.organizationId,
          dispensingNumber,
          prescriptionId: data.prescriptionId,
          pharmacistId: session.user.id,
          status: "COMPLETED",
          totalAmount: finalAmount,
          paidAmount: finalAmount,
          discountAmount: data.discountAmount || 0,
          paymentMethod: data.paymentMethod,
          paymentReference: data.paymentReference,
          notes: data.notes,
        },
      });

      // Créer les items de dispensation et les mouvements de stock
      for (const item of data.items) {
        // Créer l'item de dispensation
        const prescriptionItem = prescription.items.find(
          (pi) => pi.id === item.prescriptionItemId
        )!;

        await tx.dispensingItem.create({
          data: {
            dispensingId: dispensing.id,
            prescriptionItemId: item.prescriptionItemId,
            medicationId: item.medicationId,
            prescribedQuantity: prescriptionItem.quantity,
            dispensedQuantity: item.dispensedQuantity,
            unitPrice: item.unitPrice,
            totalPrice: item.unitPrice * item.dispensedQuantity,
            batchNumber: item.batchNumber,
            expiryDate: item.expiryDate ? new Date(item.expiryDate) : null,
          },
        });

        // Créer le mouvement de stock (sortie)
        const medication = prescriptionItem.medication!;
        await tx.pharmacyMovement.create({
          data: {
            organizationId: session.user.organizationId,
            medicationId: item.medicationId,
            type: "OUT",
            quantity: -item.dispensedQuantity,
            previousStock: medication.stockQuantity,
            newStock: medication.stockQuantity - item.dispensedQuantity,
            reason: `Dispensation ${dispensingNumber}`,
            dispensingId: dispensing.id,
            userId: session.user.id,
          },
        });

        // Mettre à jour le stock du médicament
        await tx.medication.update({
          where: { id: item.medicationId },
          data: {
            stockQuantity: {
              decrement: item.dispensedQuantity,
            },
          },
        });

        // Mettre à jour l'item de prescription
        const newDispensedQuantity =
          prescriptionItem.dispensedQuantity + item.dispensedQuantity;
        await tx.prescriptionItem.update({
          where: { id: item.prescriptionItemId },
          data: {
            dispensedQuantity: newDispensedQuantity,
            isDispensed: newDispensedQuantity >= prescriptionItem.quantity,
          },
        });
      }

      return dispensing;
    });

    revalidatePath("/dashboard/pharmacy");
    revalidatePath("/dashboard/prescriptions");
    return { success: true, dispensing: result };
  } catch (error) {
    console.error("Erreur lors de la création de la dispensation:", error);
    return { success: false, error: "Erreur serveur" };
  }
}
