"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";

// Types pour les prescriptions
export interface PrescriptionFormData {
  patientId: string;
  consultationId?: string;
  generalInstructions?: string;
  notes?: string;
  expiresAt?: Date;
  items: PrescriptionItemData[];
}

export interface PrescriptionItemData {
  medicationId?: string; // Optionnel pour médicaments externes
  // Support des médicaments externes
  isExternal: boolean;
  externalMedicationName?: string;
  externalMedicationForm?: string;
  externalMedicationStrength?: string;
  externalMedicationCategory?: string;
  estimatedPrice?: number;
  // Champs communs
  dosage: string;
  frequency: string;
  duration: string;
  timing?: string;
  route?: string;
  instructions?: string;
  quantity: number;
}

// Générer un numéro de prescription unique
function generatePrescriptionNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `RX-${timestamp}-${random}`.toUpperCase();
}

// Créer une nouvelle prescription
export async function createPrescription(data: PrescriptionFormData) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le patient existe
    const patient = await prisma.patient.findFirst({
      where: {
        id: data.patientId,
        organizationId: session.user.organizationId,
      },
    });

    if (!patient) {
      throw new Error("Patient non trouvé");
    }

    // Vérifier que la consultation existe (si fournie)
    if (data.consultationId) {
      const consultation = await prisma.consultation.findFirst({
        where: {
          id: data.consultationId,
          organizationId: session.user.organizationId,
        },
      });

      if (!consultation) {
        throw new Error("Consultation non trouvée");
      }
    }

    // Vérifier que tous les médicaments internes existent
    const internalMedicationIds = data.items
      .filter((item) => !item.isExternal && item.medicationId)
      .map((item) => item.medicationId!);

    if (internalMedicationIds.length > 0) {
      const medications = await prisma.medication.findMany({
        where: {
          id: { in: internalMedicationIds },
          organizationId: session.user.organizationId,
          isActive: true,
        },
      });

      if (medications.length !== internalMedicationIds.length) {
        throw new Error(
          "Un ou plusieurs médicaments internes sont introuvables"
        );
      }
    }

    // Créer la prescription avec ses items
    const prescription = await prisma.prescription.create({
      data: {
        organizationId: session.user.organizationId,
        patientId: data.patientId,
        doctorId: session.user.id,
        consultationId: data.consultationId,
        prescriptionNumber: generatePrescriptionNumber(),
        generalInstructions: data.generalInstructions,
        notes: data.notes,
        expiresAt: data.expiresAt,
        items: {
          create: data.items.map((item) => ({
            medicationId: item.isExternal ? null : item.medicationId,
            // Champs pour médicaments externes
            isExternal: item.isExternal || false,
            externalMedicationName: item.externalMedicationName,
            externalMedicationForm: item.externalMedicationForm,
            externalMedicationStrength: item.externalMedicationStrength,
            externalMedicationCategory: item.externalMedicationCategory,
            estimatedPrice: item.estimatedPrice,
            // Champs communs
            dosage: item.dosage,
            frequency: item.frequency,
            duration: item.duration,
            timing: item.timing,
            route: item.route,
            instructions: item.instructions,
            quantity: item.quantity,
          })),
        },
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        doctor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            type: true,
          },
        },
        items: {
          include: {
            medication: {
              select: {
                id: true,
                name: true,
                genericName: true,
                strength: true,
                form: true,
                category: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/prescriptions");
    revalidatePath("/dashboard/consultations");
    return { success: true, prescription };
  } catch (error) {
    console.error("Erreur lors de la création de la prescription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer toutes les prescriptions
export async function getPrescriptions(
  filters: {
    patientId?: string;
    doctorId?: string;
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  } = {}
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    // Construire les conditions de filtrage
    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (filters.patientId) {
      where.patientId = filters.patientId;
    }

    if (filters.doctorId) {
      where.doctorId = filters.doctorId;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.search) {
      where.OR = [
        {
          prescriptionNumber: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          patient: {
            OR: [
              {
                firstName: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
              {
                patientNumber: {
                  contains: filters.search,
                  mode: "insensitive",
                },
              },
            ],
          },
        },
      ];
    }

    // Récupérer les prescriptions avec pagination
    const [prescriptions, total] = await Promise.all([
      prisma.prescription.findMany({
        where,
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              patientNumber: true,
              phone: true,
              email: true,
            },
          },
          doctor: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            },
          },
          consultation: {
            select: {
              id: true,
              consultationDate: true,
              type: true,
            },
          },
          items: {
            include: {
              medication: {
                select: {
                  id: true,
                  name: true,
                  genericName: true,
                  strength: true,
                  form: true,
                  category: true,
                },
              },
            },
          },
        },
        orderBy: {
          prescriptionDate: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.prescription.count({ where }),
    ]);

    return {
      success: true,
      prescriptions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des prescriptions:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer une prescription par ID
export async function getPrescriptionById(id: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const prescription = await prisma.prescription.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
            bloodType: true,
            allergies: true,
            address: true,
          },
        },
        doctor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
            phone: true,
            email: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            type: true,
            diagnosis: true,
          },
        },
        items: {
          include: {
            medication: {
              select: {
                id: true,
                name: true,
                genericName: true,
                strength: true,
                form: true,
                category: true,
                manufacturer: true,
                sideEffects: true,
                contraindications: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!prescription) {
      throw new Error("Prescription non trouvée");
    }

    return { success: true, prescription };
  } catch (error) {
    console.error("Erreur lors de la récupération de la prescription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour une prescription complète
export async function updatePrescription(
  id: string,
  data: Partial<PrescriptionFormData> & { status?: string }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que la prescription existe
    const existingPrescription = await prisma.prescription.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        items: true,
      },
    });

    if (!existingPrescription) {
      throw new Error("Prescription non trouvée");
    }

    // Si des items sont fournis, vérifier que tous les médicaments internes existent
    if (data.items) {
      const internalMedicationIds = data.items
        .filter((item: any) => !item.isExternal && item.medicationId)
        .map((item: any) => item.medicationId);

      if (internalMedicationIds.length > 0) {
        const medications = await prisma.medication.findMany({
          where: {
            id: { in: internalMedicationIds },
            organizationId: session.user.organizationId,
            isActive: true,
          },
        });

        if (medications.length !== internalMedicationIds.length) {
          throw new Error(
            "Un ou plusieurs médicaments internes sont introuvables"
          );
        }
      }
    }

    // Mettre à jour la prescription
    const prescription = await prisma.prescription.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        generalInstructions: data.generalInstructions,
        notes: data.notes,
        expiresAt: data.expiresAt,
        status: data.status as any,
        // Si des items sont fournis, les remplacer complètement
        ...(data.items && {
          items: {
            deleteMany: {}, // Supprimer tous les items existants
            create: data.items.map((item: any) => ({
              medicationId: item.isExternal ? null : item.medicationId,
              // Champs pour médicaments externes
              isExternal: item.isExternal || false,
              externalMedicationName: item.externalMedicationName,
              externalMedicationForm: item.externalMedicationForm,
              externalMedicationStrength: item.externalMedicationStrength,
              externalMedicationCategory: item.externalMedicationCategory,
              estimatedPrice: item.estimatedPrice,
              // Champs communs
              dosage: item.dosage,
              frequency: item.frequency,
              duration: item.duration,
              timing: item.timing,
              route: item.route,
              instructions: item.instructions,
              quantity: item.quantity,
            })),
          },
        }),
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            patientNumber: true,
          },
        },
        doctor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            type: true,
          },
        },
        items: {
          include: {
            medication: {
              select: {
                id: true,
                name: true,
                genericName: true,
                strength: true,
                form: true,
                category: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/prescriptions");
    revalidatePath(`/dashboard/prescriptions/${id}`);
    return { success: true, prescription };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la prescription:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Mettre à jour le statut d'une prescription
export async function updatePrescriptionStatus(id: string, status: string) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const prescription = await prisma.prescription.update({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      data: {
        status: status as any,
      },
    });

    revalidatePath("/dashboard/prescriptions");
    return { success: true, prescription };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du statut:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Récupérer tous les médicaments disponibles
export async function getMedications(
  filters: {
    search?: string;
    category?: string;
    form?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
  } = {}
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const page = filters.page || 1;
    const limit = filters.limit || 50;
    const skip = (page - 1) * limit;

    // Construire les conditions de filtrage
    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.form) {
      where.form = filters.form;
    }

    if (filters.search) {
      where.OR = [
        {
          name: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          genericName: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
        {
          activeIngredient: {
            contains: filters.search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Récupérer les médicaments avec pagination
    const [medications, total] = await Promise.all([
      prisma.medication.findMany({
        where,
        orderBy: [{ name: "asc" }, { strength: "asc" }],
        skip,
        take: limit,
      }),
      prisma.medication.count({ where }),
    ]);

    return {
      success: true,
      medications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des médicaments:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// Créer un nouveau médicament
export async function createMedication(data: {
  name: string;
  genericName?: string;
  brand?: string;
  category: string;
  form: string;
  strength?: string;
  activeIngredient?: string;
  description?: string;
  manufacturer?: string;
  price?: number;
  stockQuantity?: number;
  minStockLevel?: number;
  maxStockLevel?: number;
  requiresPrescription?: boolean;
}) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier que le médicament n'existe pas déjà
    const existingMedication = await prisma.medication.findFirst({
      where: {
        organizationId: session.user.organizationId,
        name: data.name,
        strength: data.strength,
      },
    });

    if (existingMedication) {
      throw new Error("Un médicament avec ce nom et cette force existe déjà");
    }

    const medication = await prisma.medication.create({
      data: {
        organizationId: session.user.organizationId,
        name: data.name,
        genericName: data.genericName,
        brand: data.brand,
        category: data.category as any,
        form: data.form as any,
        strength: data.strength,
        activeIngredient: data.activeIngredient,
        description: data.description,
        manufacturer: data.manufacturer,
        price: data.price,
        stockQuantity: data.stockQuantity || 0,
        minStockLevel: data.minStockLevel || 10,
        maxStockLevel: data.maxStockLevel || 1000,
        requiresPrescription: data.requiresPrescription ?? true,
      },
    });

    revalidatePath("/dashboard/medications");
    return { success: true, medication };
  } catch (error) {
    console.error("Erreur lors de la création du médicament:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
