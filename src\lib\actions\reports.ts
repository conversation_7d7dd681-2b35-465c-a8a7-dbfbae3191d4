"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { checkPermission, logAction } from "./permissions";

// ===== TYPES =====

export interface ReportGenerationData {
  type:
    | "financial"
    | "patients"
    | "consultations"
    | "hospitalization"
    | "laboratory"
    | "hr";
  format: "PDF" | "Excel";
  dateRange: "7d" | "30d" | "90d" | "1y" | "custom";
  startDate?: string;
  endDate?: string;
  filters?: {
    department?: string;
    doctor?: string;
    status?: string;
  };
}

export interface ReportData {
  id: string;
  title: string;
  type: string;
  format: string;
  status: "pending" | "processing" | "completed" | "failed";
  generatedAt: Date;
  fileSize?: number;
  downloadUrl?: string;
  organizationId: string;
}

// ===== GÉNÉRATION DE RAPPORTS =====

export async function generateReport(data: ReportGenerationData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "REPORTS"
    );

    if (!canRead) {
      await logAction("READ", "REPORTS", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les rapports");
    }
    // 🔐 VÉRIFICATION DES PERMISSIONS
    const canRead = await checkPermission(
      session.user.id,
      "READ",
      "REPORTS"
    );

    if (!canRead) {
      await logAction("READ", "REPORTS", undefined, undefined, undefined, false, "Permission refusée");
      throw new Error("Vous n'avez pas l'autorisation de consulter les rapports");
    }

    const organizationId = session.user.organizationId;

    // Créer l'enregistrement du rapport
    const report = await prisma.report.create({
      data: {
        title: getReportTitle(data.type, data.dateRange),
        type: data.type,
        format: data.format,
        status: "processing",
        organizationId,
        generatedBy: session.user.id,
        parameters: JSON.stringify(data),
      },
    });

    // Simuler la génération du rapport (en arrière-plan)
    // Dans un vrai projet, ceci serait fait par un job queue
    setTimeout(async () => {
      try {
        const reportContent = await generateReportContent(data, organizationId);

        await prisma.report.update({
          where: { id: report.id },
          data: {
            status: "completed",
            fileSize: Math.floor(Math.random() * 5000000) + 500000, // 0.5-5.5 MB
            downloadUrl: `/api/reports/${report.id}/download`,
            content: JSON.stringify(reportContent),
          },
        });
      } catch (error) {
        await prisma.report.update({
          where: { id: report.id },
          data: {
            status: "failed",
            error: error instanceof Error ? error.message : "Erreur inconnue",
          },
        });
      }
    }, 2000); // Simulation de 2 secondes de traitement

    revalidatePath("/dashboard/reports");

    return { success: true, report };
  } catch (error) {
    console.error("Erreur lors de la génération du rapport:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== RÉCUPÉRATION DES RAPPORTS =====

export async function getReports() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const reports = await prisma.report.findMany({
      where: {
        organizationId: session.user.organizationId,
      },
      include: {
        generatedByUser: {
          select: {
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 50, // Limiter à 50 rapports récents
    });

    return { success: true, reports };
  } catch (error) {
    console.error("Erreur lors de la récupération des rapports:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getReportById(id: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    const report = await prisma.report.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        generatedByUser: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!report) {
      throw new Error("Rapport non trouvé");
    }

    return { success: true, report };
  } catch (error) {
    console.error("Erreur lors de la récupération du rapport:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== SUPPRESSION DE RAPPORTS =====

export async function deleteReport(id: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    await prisma.report.delete({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
    });

    revalidatePath("/dashboard/reports");

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la suppression du rapport:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== FONCTIONS UTILITAIRES =====

function getReportTitle(type: string, dateRange: string): string {
  const typeNames = {
    financial: "Rapport Financier",
    patients: "Rapport Patients",
    consultations: "Rapport Consultations",
    hospitalization: "Rapport Hospitalisations",
    laboratory: "Rapport Laboratoire",
    hr: "Rapport RH",
  };

  const rangeNames = {
    "7d": "7 derniers jours",
    "30d": "30 derniers jours",
    "90d": "3 derniers mois",
    "1y": "Cette année",
    custom: "Période personnalisée",
  };

  const typeName = typeNames[type as keyof typeof typeNames] || "Rapport";
  const rangeName =
    rangeNames[dateRange as keyof typeof rangeNames] || dateRange;

  return `${typeName} - ${rangeName}`;
}

function getDateRange(dateRange: string, startDate?: string, endDate?: string) {
  if (dateRange === "custom" && startDate && endDate) {
    return {
      start: new Date(startDate),
      end: new Date(endDate),
    };
  }

  const now = new Date();
  let start: Date;

  switch (dateRange) {
    case "7d":
      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case "30d":
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case "90d":
      start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case "1y":
      start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return { start, end: now };
}

async function generateReportContent(
  data: ReportGenerationData,
  organizationId: string
) {
  const { start, end } = getDateRange(
    data.dateRange,
    data.startDate,
    data.endDate
  );

  switch (data.type) {
    case "financial":
      return await generateFinancialReport(organizationId, start, end);
    case "patients":
      return await generatePatientsReport(organizationId, start, end);
    case "consultations":
      return await generateConsultationsReport(organizationId, start, end);
    case "hospitalization":
      return await generateHospitalizationReport(organizationId, start, end);
    case "laboratory":
      return await generateLaboratoryReport(organizationId, start, end);
    case "hr":
      return await generateHRReport(organizationId, start, end);
    default:
      throw new Error("Type de rapport non supporté");
  }
}

async function generateFinancialReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const [payments, consultations, labOrders] = await Promise.all([
    prisma.payment.findMany({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
        status: "PAID",
      },
      include: {
        patient: { select: { firstName: true, lastName: true } },
        consultation: { select: { type: true } },
      },
    }),

    prisma.consultation.aggregate({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
      },
      _sum: { consultationFee: true },
      _count: true,
    }),

    prisma.labOrder.aggregate({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
        status: "COMPLETED",
      },
      _sum: { totalAmount: true },
      _count: true,
    }),
  ]);

  return {
    summary: {
      totalRevenue: payments.reduce((sum, p) => sum + p.amount, 0),
      consultationRevenue: consultations._sum.consultationFee || 0,
      laboratoryRevenue: labOrders._sum.totalAmount || 0,
      totalTransactions: payments.length,
    },
    payments,
    period: { startDate, endDate },
  };
}

async function generatePatientsReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const patients = await prisma.patient.findMany({
    where: {
      organizationId,
      createdAt: { gte: startDate, lte: endDate },
      isActive: true,
    },
    include: {
      consultations: { select: { id: true, createdAt: true } },
      labOrders: { select: { id: true, createdAt: true } },
      admissions: { select: { id: true, admissionDate: true } },
    },
  });

  return {
    summary: {
      totalPatients: patients.length,
      averageAge: 0, // À calculer
      genderDistribution: {}, // À calculer
    },
    patients,
    period: { startDate, endDate },
  };
}

async function generateConsultationsReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const consultations = await prisma.consultation.findMany({
    where: {
      organizationId,
      createdAt: { gte: startDate, lte: endDate },
    },
    include: {
      patient: { select: { firstName: true, lastName: true } },
      doctor: { select: { firstName: true, lastName: true } },
      payments: {
        where: { status: "PAID" },
        select: { amount: true, status: true },
      },
    },
  });

  return {
    summary: {
      totalConsultations: consultations.length,
      totalRevenue: consultations.reduce(
        (sum, c) => sum + c.payments.reduce((pSum, p) => pSum + p.amount, 0),
        0
      ),
      averageDuration: 0, // À calculer
    },
    consultations,
    period: { startDate, endDate },
  };
}

async function generateHospitalizationReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const admissions = await prisma.admission.findMany({
    where: {
      organizationId,
      admissionDate: { gte: startDate, lte: endDate },
    },
    include: {
      patient: { select: { firstName: true, lastName: true } },
      doctor: { select: { name: true } },
      room: { select: { number: true, roomType: true } },
      bed: { select: { number: true } },
    },
  });

  return {
    summary: {
      totalAdmissions: admissions.length,
      averageStay: 0, // À calculer
      occupancyRate: 0, // À calculer
    },
    admissions,
    period: { startDate, endDate },
  };
}

async function generateLaboratoryReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const labOrders = await prisma.labOrder.findMany({
    where: {
      organizationId,
      createdAt: { gte: startDate, lte: endDate },
    },
    include: {
      patient: { select: { firstName: true, lastName: true } },
      items: {
        include: {
          testType: { select: { name: true, category: true } },
        },
      },
    },
  });

  return {
    summary: {
      totalOrders: labOrders.length,
      totalRevenue: labOrders.reduce((sum, o) => sum + (o.totalAmount || 0), 0),
      completedOrders: labOrders.filter((o) => o.status === "COMPLETED").length,
    },
    labOrders,
    period: { startDate, endDate },
  };
}

async function generateHRReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  const [employees, attendances, leaveRequests] = await Promise.all([
    prisma.employee.findMany({
      where: { organizationId, status: "ACTIVE" },
      include: {
        department: { select: { name: true } },
        position: { select: { title: true } },
      },
    }),

    prisma.attendance.findMany({
      where: {
        organizationId,
        date: { gte: startDate, lte: endDate },
      },
      include: {
        employee: { select: { firstName: true, lastName: true } },
      },
    }),

    prisma.leaveRequest.findMany({
      where: {
        organizationId,
        createdAt: { gte: startDate, lte: endDate },
      },
      include: {
        employee: { select: { firstName: true, lastName: true } },
      },
    }),
  ]);

  return {
    summary: {
      totalEmployees: employees.length,
      attendanceRate: 0, // À calculer
      leaveRequests: leaveRequests.length,
    },
    employees,
    attendances,
    leaveRequests,
    period: { startDate, endDate },
  };
}
