"use server";

import { getServerSession } from "next-auth";
import { revalidatePath } from "next/cache";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { UserRole } from "@prisma/client";
import bcrypt from "bcryptjs";
import { logAction } from "./permissions";

// ===== TYPES =====

export interface CreateUserData {
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  password: string;
}

export interface UpdateUserData {
  id: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface UserFilters {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  page?: number;
  limit?: number;
}

// ===== GESTION DES UTILISATEURS =====

export async function getUsers(filters: UserFilters = {}) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    const {
      search = "",
      role,
      isActive,
      page = 1,
      limit = 20,
    } = filters;

    // Construire les conditions de recherche
    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: "insensitive" } },
        { lastName: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    // Récupérer les utilisateurs avec pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          isActive: true,
          createdAt: true,
          lastLogin: true,
          employee: {
            select: {
              id: true,
              department: {
                select: {
                  name: true,
                },
              },
              position: {
                select: {
                  title: true,
                },
              },
            },
          },
        },
        orderBy: [
          { isActive: "desc" },
          { role: "asc" },
          { firstName: "asc" },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    await logAction("READ", "USERS", undefined, undefined, { count: users.length });

    return {
      success: true,
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des utilisateurs:", error);
    await logAction("READ", "USERS", undefined, undefined, undefined, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function getUserById(id: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    const user = await prisma.user.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
      include: {
        employee: {
          include: {
            department: true,
            position: true,
          },
        },
        userPermissions: {
          include: {
            permission: true,
          },
        },
        userSessions: {
          where: {
            isActive: true,
          },
          orderBy: {
            loginAt: "desc",
          },
          take: 5,
        },
      },
    });

    if (!user) {
      throw new Error("Utilisateur non trouvé");
    }

    await logAction("READ", "USERS", id);

    return { success: true, user };
  } catch (error) {
    console.error("Erreur lors de la récupération de l'utilisateur:", error);
    await logAction("READ", "USERS", id, undefined, undefined, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function createUser(data: CreateUserData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    // Vérifier que l'email n'existe pas déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      throw new Error("Un utilisateur avec cet email existe déjà");
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(data.password, 12);

    // Créer l'utilisateur
    const user = await prisma.user.create({
      data: {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        phone: data.phone,
        role: data.role,
        password: hashedPassword,
        organizationId: session.user.organizationId,
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    await logAction("CREATE", "USERS", user.id, undefined, user);

    revalidatePath("/dashboard/settings/users");

    return { success: true, user };
  } catch (error) {
    console.error("Erreur lors de la création de l'utilisateur:", error);
    await logAction("CREATE", "USERS", undefined, undefined, data, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function updateUser(data: UpdateUserData) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    // Récupérer l'utilisateur actuel pour l'audit
    const existingUser = await prisma.user.findFirst({
      where: {
        id: data.id,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingUser) {
      throw new Error("Utilisateur non trouvé");
    }

    // Empêcher la modification de son propre statut actif
    if (data.id === session.user.id && data.isActive === false) {
      throw new Error("Vous ne pouvez pas désactiver votre propre compte");
    }

    // Préparer les données de mise à jour
    const updateData: any = {};
    if (data.firstName !== undefined) updateData.firstName = data.firstName;
    if (data.lastName !== undefined) updateData.lastName = data.lastName;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.role !== undefined) updateData.role = data.role;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Mettre à jour l'utilisateur
    const user = await prisma.user.update({
      where: { id: data.id },
      data: updateData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        isActive: true,
        updatedAt: true,
      },
    });

    await logAction("UPDATE", "USERS", user.id, existingUser, user);

    revalidatePath("/dashboard/settings/users");

    return { success: true, user };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'utilisateur:", error);
    await logAction("UPDATE", "USERS", data.id, undefined, data, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function deleteUser(id: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    // Empêcher la suppression de son propre compte
    if (id === session.user.id) {
      throw new Error("Vous ne pouvez pas supprimer votre propre compte");
    }

    // Récupérer l'utilisateur pour l'audit
    const existingUser = await prisma.user.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingUser) {
      throw new Error("Utilisateur non trouvé");
    }

    // Vérifier s'il y a des données liées
    const [consultations, prescriptions, payments] = await Promise.all([
      prisma.consultation.count({ where: { doctorId: id } }),
      prisma.prescription.count({ where: { doctorId: id } }),
      prisma.payment.count({ where: { createdById: id } }),
    ]);

    if (consultations > 0 || prescriptions > 0 || payments > 0) {
      // Désactiver au lieu de supprimer si il y a des données liées
      const user = await prisma.user.update({
        where: { id },
        data: { isActive: false },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          isActive: true,
        },
      });

      await logAction("UPDATE", "USERS", id, existingUser, { isActive: false, reason: "Désactivé au lieu de supprimé (données liées)" });

      revalidatePath("/dashboard/settings/users");

      return { 
        success: true, 
        user, 
        message: "Utilisateur désactivé (suppression impossible car il a des données liées)" 
      };
    } else {
      // Supprimer complètement
      await prisma.user.delete({
        where: { id },
      });

      await logAction("DELETE", "USERS", id, existingUser);

      revalidatePath("/dashboard/settings/users");

      return { success: true, message: "Utilisateur supprimé avec succès" };
    }
  } catch (error) {
    console.error("Erreur lors de la suppression de l'utilisateur:", error);
    await logAction("DELETE", "USERS", id, undefined, undefined, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

export async function resetUserPassword(id: string, newPassword: string) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    // Vérifier que l'utilisateur existe
    const existingUser = await prisma.user.findFirst({
      where: {
        id,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingUser) {
      throw new Error("Utilisateur non trouvé");
    }

    // Hacher le nouveau mot de passe
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Mettre à jour le mot de passe
    await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });

    await logAction("UPDATE", "USERS", id, undefined, { action: "password_reset" });

    return { success: true, message: "Mot de passe réinitialisé avec succès" };
  } catch (error) {
    console.error("Erreur lors de la réinitialisation du mot de passe:", error);
    await logAction("UPDATE", "USERS", id, undefined, { action: "password_reset" }, false, error instanceof Error ? error.message : "Erreur inconnue");
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}

// ===== STATISTIQUES =====

export async function getUsersStats() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.organizationId) {
      throw new Error("Session utilisateur invalide");
    }

    // Vérifier les permissions
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!currentUser || !["ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
      throw new Error("Permissions insuffisantes");
    }

    const [
      totalUsers,
      activeUsers,
      usersByRole,
      recentUsers,
      activeSessions,
    ] = await Promise.all([
      prisma.user.count({
        where: { organizationId: session.user.organizationId },
      }),
      prisma.user.count({
        where: { 
          organizationId: session.user.organizationId,
          isActive: true,
        },
      }),
      prisma.user.groupBy({
        by: ["role"],
        where: { organizationId: session.user.organizationId },
        _count: { role: true },
      }),
      prisma.user.findMany({
        where: { organizationId: session.user.organizationId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 5,
      }),
      prisma.userSession.count({
        where: {
          user: {
            organizationId: session.user.organizationId,
          },
          isActive: true,
        },
      }),
    ]);

    return {
      success: true,
      stats: {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        usersByRole,
        recentUsers,
        activeSessions,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    };
  }
}
