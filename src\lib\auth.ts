import { NextAuthOptions } from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import CredentialsProvider from "next-auth/providers/credentials";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { UserRole } from "@prisma/client";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
          include: {
            organization: true,
          },
        });

        if (!user) {
          return null;
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          return null;
        }

        // Vérifier que l'organisation est active
        if (
          !user.organization.isActive ||
          user.organization.subscriptionStatus !== "active"
        ) {
          throw new Error("Organisation suspendue ou inactive");
        }

        // Vérifier que l'utilisateur est actif
        if (!user.isActive) {
          throw new Error("Compte utilisateur désactivé");
        }

        // Mettre à jour la dernière connexion
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLogin: new Date() },
        });

        return {
          id: user.id,
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          role: user.role,
          organizationId: user.organizationId,
          organization: {
            id: user.organization.id,
            name: user.organization.name,
            slug: user.organization.slug,
            subscriptionPlan: user.organization.subscriptionPlan,
            subscriptionStatus: user.organization.subscriptionStatus,
          },
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 jours
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.organizationId = user.organizationId;
        token.organization = user.organization;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as UserRole;
        session.user.organizationId = token.organizationId as string;
        session.user.organization = token.organization as any;
      }
      return session;
    },
  },
  // pages: {
  //   signIn: "/auth/signin",
  //   error: "/auth/error",
  // },
  secret: process.env.NEXTAUTH_SECRET,
};

// Fonction utilitaire pour hacher les mots de passe
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// Fonction utilitaire pour vérifier les mots de passe
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Fonction pour vérifier les permissions
export function hasPermission(
  userRole: UserRole,
  requiredRoles: UserRole[]
): boolean {
  return requiredRoles.includes(userRole);
}

// Hiérarchie des rôles pour les permissions
export const roleHierarchy: Record<UserRole, number> = {
  SUPER_ADMIN: 100,
  ADMIN: 80,
  DOCTOR: 60,
  NURSE: 40,
  PHARMACIST: 30,
  TECHNICIAN: 20,
  RECEPTIONIST: 10,
  ACCOUNTANT: 15,
};

// Fonction pour vérifier si un rôle a un niveau suffisant
export function hasMinimumRole(
  userRole: UserRole,
  minimumRole: UserRole
): boolean {
  return roleHierarchy[userRole] >= roleHierarchy[minimumRole];
}

// Types pour l'authentification
declare module "next-auth" {
  interface User {
    role: UserRole;
    organizationId: string;
    organization: {
      id: string;
      name: string;
      slug: string;
      subscriptionPlan: string;
      subscriptionStatus: string;
    };
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: UserRole;
      organizationId: string;
      organization: {
        id: string;
        name: string;
        slug: string;
        subscriptionPlan: string;
        subscriptionStatus: string;
      };
    };
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: UserRole;
    organizationId: string;
    organization: {
      id: string;
      name: string;
      slug: string;
      subscriptionPlan: string;
      subscriptionStatus: string;
    };
  }
}
