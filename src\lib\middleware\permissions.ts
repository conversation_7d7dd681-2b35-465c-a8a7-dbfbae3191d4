import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { checkPermission } from "@/lib/actions/permissions";
import { PermissionAction, PermissionResource } from "@prisma/client";

// ===== TYPES =====

export interface PermissionRequirement {
  action: PermissionAction;
  resource: PermissionResource;
  resourceId?: string;
}

export interface PermissionContext {
  userId: string;
  organizationId: string;
  role: string;
}

// ===== MIDDLEWARE DE PERMISSIONS =====

/**
 * Vérifie si l'utilisateur actuel a les permissions requises
 */
export async function requirePermissions(
  requirements: PermissionRequirement | PermissionRequirement[]
): Promise<{ success: boolean; context?: PermissionContext; error?: string }> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return { success: false, error: "Session utilisateur invalide" };
    }

    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: session.user.organizationId,
      role: session.user.role,
    };

    // Normaliser les requirements en array
    const reqArray = Array.isArray(requirements) ? requirements : [requirements];

    // Vérifier chaque permission
    for (const req of reqArray) {
      const hasPermission = await checkPermission(
        session.user.id,
        req.action,
        req.resource,
        req.resourceId
      );

      if (!hasPermission) {
        return {
          success: false,
          error: `Permission refusée: ${req.action} sur ${req.resource}`,
        };
      }
    }

    return { success: true, context };
  } catch (error) {
    console.error("Erreur lors de la vérification des permissions:", error);
    return {
      success: false,
      error: "Erreur lors de la vérification des permissions",
    };
  }
}

/**
 * Wrapper pour les Server Actions avec vérification de permissions
 */
export function withPermissions<T extends any[], R>(
  requirements: PermissionRequirement | PermissionRequirement[],
  action: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const permissionCheck = await requirePermissions(requirements);
    
    if (!permissionCheck.success) {
      throw new Error(permissionCheck.error || "Permissions insuffisantes");
    }

    return action(...args);
  };
}

// ===== PERMISSIONS PRÉDÉFINIES =====

export const PERMISSIONS = {
  // Patients
  PATIENTS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "PATIENTS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "PATIENTS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "PATIENTS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "PATIENTS" as PermissionResource },
  },
  
  // Consultations
  CONSULTATIONS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "CONSULTATIONS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "CONSULTATIONS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "CONSULTATIONS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "CONSULTATIONS" as PermissionResource },
  },
  
  // Prescriptions
  PRESCRIPTIONS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "PRESCRIPTIONS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "PRESCRIPTIONS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "PRESCRIPTIONS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "PRESCRIPTIONS" as PermissionResource },
    PRINT: { action: "PRINT" as PermissionAction, resource: "PRESCRIPTIONS" as PermissionResource },
  },
  
  // Médicaments
  MEDICATIONS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "MEDICATIONS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "MEDICATIONS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "MEDICATIONS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "MEDICATIONS" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "MEDICATIONS" as PermissionResource },
  },
  
  // Pharmacie
  PHARMACY: {
    READ: { action: "READ" as PermissionAction, resource: "PHARMACY" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "PHARMACY" as PermissionResource },
  },
  
  // Laboratoire
  LABORATORY: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "LABORATORY" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "LABORATORY" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "LABORATORY" as PermissionResource },
    APPROVE: { action: "APPROVE" as PermissionAction, resource: "LABORATORY" as PermissionResource },
    PRINT: { action: "PRINT" as PermissionAction, resource: "LABORATORY" as PermissionResource },
  },
  
  // Hospitalisation
  HOSPITALIZATION: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "HOSPITALIZATION" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "HOSPITALIZATION" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "HOSPITALIZATION" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "HOSPITALIZATION" as PermissionResource },
  },
  
  // Employés
  EMPLOYEES: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "EMPLOYEES" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "EMPLOYEES" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "EMPLOYEES" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "EMPLOYEES" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "EMPLOYEES" as PermissionResource },
  },
  
  // Départements
  DEPARTMENTS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "DEPARTMENTS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "DEPARTMENTS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "DEPARTMENTS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "DEPARTMENTS" as PermissionResource },
  },
  
  // Paiements
  PAYMENTS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "PAYMENTS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "PAYMENTS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "PAYMENTS" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "PAYMENTS" as PermissionResource },
  },
  
  // Rapports
  REPORTS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "REPORTS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "REPORTS" as PermissionResource },
    EXPORT: { action: "EXPORT" as PermissionAction, resource: "REPORTS" as PermissionResource },
  },
  
  // Analytics
  ANALYTICS: {
    READ: { action: "READ" as PermissionAction, resource: "ANALYTICS" as PermissionResource },
    EXPORT: { action: "EXPORT" as PermissionAction, resource: "ANALYTICS" as PermissionResource },
  },
  
  // Paramètres
  SETTINGS: {
    READ: { action: "READ" as PermissionAction, resource: "SETTINGS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "SETTINGS" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "SETTINGS" as PermissionResource },
  },
  
  // Utilisateurs
  USERS: {
    CREATE: { action: "CREATE" as PermissionAction, resource: "USERS" as PermissionResource },
    READ: { action: "READ" as PermissionAction, resource: "USERS" as PermissionResource },
    UPDATE: { action: "UPDATE" as PermissionAction, resource: "USERS" as PermissionResource },
    DELETE: { action: "DELETE" as PermissionAction, resource: "USERS" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "USERS" as PermissionResource },
  },
  
  // Rôles
  ROLES: {
    READ: { action: "READ" as PermissionAction, resource: "ROLES" as PermissionResource },
    MANAGE: { action: "MANAGE" as PermissionAction, resource: "ROLES" as PermissionResource },
  },
} as const;

// ===== HELPERS =====

/**
 * Vérifie si l'utilisateur peut accéder à une ressource spécifique
 */
export async function canAccess(
  action: PermissionAction,
  resource: PermissionResource,
  resourceId?: string
): Promise<boolean> {
  const session = await getServerSession(authOptions);
  if (!session?.user) return false;

  return checkPermission(session.user.id, action, resource, resourceId);
}

/**
 * Obtient le contexte de permissions de l'utilisateur actuel
 */
export async function getPermissionContext(): Promise<PermissionContext | null> {
  const session = await getServerSession(authOptions);
  if (!session?.user) return null;

  return {
    userId: session.user.id,
    organizationId: session.user.organizationId,
    role: session.user.role,
  };
}

/**
 * Vérifie si l'utilisateur a un rôle spécifique ou supérieur
 */
export async function hasRole(requiredRole: string): Promise<boolean> {
  const session = await getServerSession(authOptions);
  if (!session?.user) return false;

  const roleHierarchy = [
    "RECEPTIONIST",
    "TECHNICIAN", 
    "LAB_TECHNICIAN",
    "NURSE",
    "PHARMACIST",
    "ACCOUNTANT",
    "DOCTOR",
    "ADMIN",
    "SUPER_ADMIN",
  ];

  const userRoleIndex = roleHierarchy.indexOf(session.user.role);
  const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

  return userRoleIndex >= requiredRoleIndex;
}
