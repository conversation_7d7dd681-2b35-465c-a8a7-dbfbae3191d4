import {
  Organization,
  User,
  Patient,
  Consultation,
  Prescription,
  MedicalHistory,
  UserRole,
  Gender,
  BloodType,
  MaritalStatus,
  ConsultationStatus,
  ConsultationType,
  PrescriptionStatus,
  PaymentStatus,
} from "@prisma/client";

// Types de base exportés depuis Prisma
export type {
  Organization,
  User,
  Patient,
  Consultation,
  Prescription,
  MedicalHistory,
  UserRole,
  Gender,
  BloodType,
  MaritalStatus,
  ConsultationStatus,
  ConsultationType,
  PrescriptionStatus,
  PaymentStatus,
};

// Types étendus avec relations pour SaaS multi-tenant
export type OrganizationWithStats = Organization & {
  _count: {
    users: number;
    patients: number;
    consultations: number;
  };
  stats?: {
    activeUsers: number;
    activePatients: number;
    monthlyConsultations: number;
    revenue: number;
  };
};

export type UserWithOrganization = User & {
  organization: Organization;
};

export type PatientWithRelations = Patient & {
  organization: Organization;
  consultations?: Consultation[];
  prescriptions?: Prescription[];
  medicalHistory?: MedicalHistory[];
  createdBy: User;
  updatedBy?: User;
};

export type ConsultationWithRelations = Consultation & {
  organization: Organization;
  patient: Patient;
  doctor: User;
  prescriptions?: Prescription[];
};

export type PrescriptionWithRelations = Prescription & {
  organization: Organization;
  patient: Patient;
  doctor: User;
  consultation?: Consultation;
};

// Types pour les formulaires SaaS
export interface CreateOrganizationData {
  name: string;
  slug: string;
  type: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  website?: string;
  subscriptionPlan?: string;
}

export interface UpdateOrganizationData
  extends Partial<CreateOrganizationData> {
  id: string;
  logo?: string;
  timezone?: string;
  currency?: string;
  language?: string;
  maxUsers?: number;
  maxPatients?: number;
  maxStorage?: number;
}

export interface CreatePatientData {
  organizationId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender: Gender;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  bloodType?: BloodType;
  allergies?: string;
  chronicDiseases?: string;
  emergencyContact?: string;
  maritalStatus?: MaritalStatus;
  occupation?: string;
  insurance?: string;
  notes?: string;
}

export interface UpdatePatientData extends Partial<CreatePatientData> {
  id: string;
}

export interface CreateConsultationData {
  patientId: string;
  doctorId: string;
  consultationDate: Date;
  type: ConsultationType;
  chiefComplaint?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  weight?: number;
  height?: number;
  bloodPressure?: string;
  temperature?: number;
  heartRate?: number;
  consultationFee?: number;
  duration?: number;
}

export interface UpdateConsultationData
  extends Partial<CreateConsultationData> {
  id: string;
  status?: ConsultationStatus;
  paymentStatus?: PaymentStatus;
}

export interface CreatePrescriptionData {
  patientId: string;
  doctorId: string;
  consultationId?: string;
  medications: string; // JSON string
  dosage?: string;
  duration?: string;
  instructions?: string;
  expiresAt?: Date;
}

export interface UpdatePrescriptionData
  extends Partial<CreatePrescriptionData> {
  id: string;
  status?: PrescriptionStatus;
}

// Types pour les réponses API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Types pour les filtres et recherche
export interface PatientFilters {
  search?: string;
  gender?: Gender;
  bloodType?: BloodType;
  maritalStatus?: MaritalStatus;
  isActive?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

export interface ConsultationFilters {
  patientId?: string;
  doctorId?: string;
  status?: ConsultationStatus;
  type?: ConsultationType;
  dateFrom?: Date;
  dateTo?: Date;
  paymentStatus?: PaymentStatus;
}

// Types pour les statistiques
export interface PatientStats {
  total: number;
  active: number;
  inactive: number;
  byGender: Record<Gender, number>;
  byBloodType: Record<BloodType, number>;
  recentRegistrations: number;
}

export interface ConsultationStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
  byStatus: Record<ConsultationStatus, number>;
  byType: Record<ConsultationType, number>;
  revenue: {
    total: number;
    pending: number;
    paid: number;
  };
}

// Types pour les données JSON stockées
export interface AllergyData {
  name: string;
  severity: "mild" | "moderate" | "severe";
  reaction?: string;
}

export interface ChronicDiseaseData {
  name: string;
  diagnosedDate?: string;
  status: "active" | "controlled" | "remission";
  medications?: string[];
}

export interface EmergencyContactData {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
}

export interface MedicationData {
  name: string;
  dosage: string;
  frequency: string;
  duration?: string;
  instructions?: string;
}

// Types pour les notifications
export interface NotificationData {
  id: string;
  type: "info" | "success" | "warning" | "error";
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}
