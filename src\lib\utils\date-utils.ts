// Utilitaires pour la gestion des dates

/**
 * Formate une date en format français court
 * @param date - Date à formater (string ou Date)
 * @returns Date formatée (ex: "15/12/2023")
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return 'Date invalide'
  }
  
  return dateObj.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

/**
 * Formate une date avec l'heure en format français
 * @param date - Date à formater (string ou Date)
 * @returns Date et heure formatées (ex: "15/12/2023 à 14:30")
 */
export function formatDateTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return 'Date invalide'
  }
  
  const dateStr = dateObj.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
  
  const timeStr = dateObj.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  })
  
  return `${dateStr} à ${timeStr}`
}

/**
 * Formate une date en format long français
 * @param date - Date à formater (string ou Date)
 * @returns Date formatée (ex: "vendredi 15 décembre 2023")
 */
export function formatDateLong(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return 'Date invalide'
  }
  
  return dateObj.toLocaleDateString('fr-FR', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

/**
 * Formate une heure
 * @param date - Date à formater (string ou Date)
 * @returns Heure formatée (ex: "14:30")
 */
export function formatTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return 'Heure invalide'
  }
  
  return dateObj.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * Calcule la différence entre deux dates en jours
 * @param date1 - Première date
 * @param date2 - Deuxième date
 * @returns Nombre de jours de différence
 */
export function daysDifference(date1: string | Date, date2: string | Date): number {
  const dateObj1 = typeof date1 === 'string' ? new Date(date1) : date1
  const dateObj2 = typeof date2 === 'string' ? new Date(date2) : date2
  
  const timeDiff = Math.abs(dateObj2.getTime() - dateObj1.getTime())
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

/**
 * Vérifie si une date est aujourd'hui
 * @param date - Date à vérifier
 * @returns true si la date est aujourd'hui
 */
export function isToday(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  
  return dateObj.toDateString() === today.toDateString()
}

/**
 * Vérifie si une date est dans le passé
 * @param date - Date à vérifier
 * @returns true si la date est dans le passé
 */
export function isPast(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  
  return dateObj < now
}

/**
 * Vérifie si une date est dans le futur
 * @param date - Date à vérifier
 * @returns true si la date est dans le futur
 */
export function isFuture(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  
  return dateObj > now
}

/**
 * Ajoute des jours à une date
 * @param date - Date de base
 * @param days - Nombre de jours à ajouter
 * @returns Nouvelle date
 */
export function addDays(date: string | Date, days: number): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date)
  dateObj.setDate(dateObj.getDate() + days)
  return dateObj
}

/**
 * Soustrait des jours à une date
 * @param date - Date de base
 * @param days - Nombre de jours à soustraire
 * @returns Nouvelle date
 */
export function subtractDays(date: string | Date, days: number): Date {
  return addDays(date, -days)
}

/**
 * Obtient le début de la journée (00:00:00)
 * @param date - Date de base
 * @returns Date au début de la journée
 */
export function startOfDay(date: string | Date): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date)
  dateObj.setHours(0, 0, 0, 0)
  return dateObj
}

/**
 * Obtient la fin de la journée (23:59:59)
 * @param date - Date de base
 * @returns Date à la fin de la journée
 */
export function endOfDay(date: string | Date): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date)
  dateObj.setHours(23, 59, 59, 999)
  return dateObj
}

/**
 * Formate une date pour un input HTML date
 * @param date - Date à formater
 * @returns Date au format YYYY-MM-DD
 */
export function formatDateForInput(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return ''
  }
  
  return dateObj.toISOString().split('T')[0]
}

/**
 * Formate une date pour un input HTML datetime-local
 * @param date - Date à formater
 * @returns Date au format YYYY-MM-DDTHH:mm
 */
export function formatDateTimeForInput(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return ''
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day}T${hours}:${minutes}`
}

/**
 * Obtient une date relative (ex: "il y a 2 jours", "dans 3 heures")
 * @param date - Date à comparer
 * @returns Texte relatif
 */
export function getRelativeTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (Math.abs(diffMinutes) < 1) {
    return 'À l\'instant'
  } else if (Math.abs(diffMinutes) < 60) {
    return diffMinutes > 0 ? `Il y a ${diffMinutes} min` : `Dans ${Math.abs(diffMinutes)} min`
  } else if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `Il y a ${diffHours}h` : `Dans ${Math.abs(diffHours)}h`
  } else if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}` : `Dans ${Math.abs(diffDays)} jour${Math.abs(diffDays) > 1 ? 's' : ''}`
  } else {
    return formatDate(dateObj)
  }
}
