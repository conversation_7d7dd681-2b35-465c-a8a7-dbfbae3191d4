// Utilitaires pour la gestion des patients

// Calculer l'âge d'un patient
export function calculateAge(dateOfBirth: Date | string): number {
  const today = new Date()
  const birthDate = new Date(dateOfBirth)
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }
  
  return age
}

// Formater le groupe sanguin pour l'affichage
export function formatBloodType(bloodType: string): string {
  const bloodTypeMap: { [key: string]: string } = {
    'A_POSITIVE': 'A+',
    'A_NEGATIVE': 'A-',
    'B_POSITIVE': 'B+',
    'B_NEGATIVE': 'B-',
    'AB_POSITIVE': 'AB+',
    'AB_NEGATIVE': 'AB-',
    'O_POSITIVE': 'O+',
    'O_NEGATIVE': 'O-',
    'UNKNOWN': 'Inconnu'
  }
  return bloodTypeMap[bloodType] || bloodType
}

// Formater le genre pour l'affichage
export function formatGender(gender: string): string {
  const genderMap: { [key: string]: string } = {
    'MALE': 'Homme',
    'FEMALE': 'Femme',
    'OTHER': 'Autre'
  }
  return genderMap[gender] || gender
}

// Formater le statut matrimonial pour l'affichage
export function formatMaritalStatus(maritalStatus: string): string {
  const statusMap: { [key: string]: string } = {
    'SINGLE': 'Célibataire',
    'MARRIED': 'Marié(e)',
    'DIVORCED': 'Divorcé(e)',
    'WIDOWED': 'Veuf/Veuve',
    'OTHER': 'Autre'
  }
  return statusMap[maritalStatus] || maritalStatus
}

// Générer les initiales d'un patient
export function getPatientInitials(firstName: string, lastName: string): string {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

// Obtenir la couleur du badge de groupe sanguin
export function getBloodTypeColor(bloodType: string): string {
  const colors: { [key: string]: string } = {
    'A_POSITIVE': 'bg-blue-100 text-blue-800',
    'A_NEGATIVE': 'bg-blue-200 text-blue-900',
    'B_POSITIVE': 'bg-green-100 text-green-800',
    'B_NEGATIVE': 'bg-green-200 text-green-900',
    'AB_POSITIVE': 'bg-purple-100 text-purple-800',
    'AB_NEGATIVE': 'bg-purple-200 text-purple-900',
    'O_POSITIVE': 'bg-red-100 text-red-800',
    'O_NEGATIVE': 'bg-red-200 text-red-900',
    'UNKNOWN': 'bg-gray-100 text-gray-800'
  }
  return colors[bloodType] || 'bg-gray-100 text-gray-800'
}

// Valider un numéro de téléphone malien
export function validateMalianPhone(phone: string): boolean {
  // Format malien: +223 XX XX XX XX ou 00223 XX XX XX XX
  const malianPhoneRegex = /^(\+223|00223|223)?[0-9\s]{8,10}$/
  return malianPhoneRegex.test(phone.replace(/\s/g, ''))
}

// Formater un numéro de téléphone malien
export function formatMalianPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.startsWith('223')) {
    const number = cleaned.substring(3)
    return `+223 ${number.substring(0, 2)} ${number.substring(2, 4)} ${number.substring(4, 6)} ${number.substring(6, 8)}`
  }
  
  if (cleaned.length === 8) {
    return `+223 ${cleaned.substring(0, 2)} ${cleaned.substring(2, 4)} ${cleaned.substring(4, 6)} ${cleaned.substring(6, 8)}`
  }
  
  return phone
}
