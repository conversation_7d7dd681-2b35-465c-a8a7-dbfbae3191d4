// Utilitaires pour les prescriptions

export interface PrescriptionItem {
  id: string
  dosage: string
  frequency: string
  duration: string
  timing?: string
  route?: string
  instructions?: string
  quantity: number
  isExternal: boolean
  externalMedicationName?: string
  externalMedicationForm?: string
  externalMedicationStrength?: string
  externalMedicationCategory?: string
  estimatedPrice?: number
  medication?: {
    id: string
    name: string
    genericName?: string
    strength?: string
    form: string
    category: string
    unitPrice?: number
  } | null
}

// Séparer les médicaments internes et externes
export function separateMedications(items: PrescriptionItem[]) {
  const internal = items.filter(item => !item.isExternal)
  const external = items.filter(item => item.isExternal)
  
  return { internal, external }
}

// Calculer le coût total d'une prescription
export function calculateTotalCost(items: PrescriptionItem[]): number {
  return items.reduce((total, item) => {
    if (item.isExternal) {
      return total + (item.estimatedPrice || 0) * item.quantity
    } else {
      return total + (item.medication?.unitPrice || 0) * item.quantity
    }
  }, 0)
}

// Calculer le coût des médicaments internes
export function calculateInternalCost(items: PrescriptionItem[]): number {
  return items
    .filter(item => !item.isExternal)
    .reduce((total, item) => {
      return total + (item.medication?.unitPrice || 0) * item.quantity
    }, 0)
}

// Calculer le coût des médicaments externes
export function calculateExternalCost(items: PrescriptionItem[]): number {
  return items
    .filter(item => item.isExternal)
    .reduce((total, item) => {
      return total + (item.estimatedPrice || 0) * item.quantity
    }, 0)
}

// Obtenir le nom d'un médicament (interne ou externe)
export function getMedicationName(item: PrescriptionItem): string {
  if (item.isExternal) {
    return item.externalMedicationName || 'Médicament externe inconnu'
  } else {
    return item.medication?.name || 'Médicament inconnu'
  }
}

// Obtenir la forme d'un médicament (interne ou externe)
export function getMedicationForm(item: PrescriptionItem): string {
  if (item.isExternal) {
    return item.externalMedicationForm || 'Forme inconnue'
  } else {
    return item.medication?.form || 'Forme inconnue'
  }
}

// Obtenir la force/dosage d'un médicament (interne ou externe)
export function getMedicationStrength(item: PrescriptionItem): string {
  if (item.isExternal) {
    return item.externalMedicationStrength || ''
  } else {
    return item.medication?.strength || ''
  }
}

// Obtenir la catégorie d'un médicament (interne ou externe)
export function getMedicationCategory(item: PrescriptionItem): string {
  if (item.isExternal) {
    return item.externalMedicationCategory || 'OTHER'
  } else {
    return item.medication?.category || 'OTHER'
  }
}

// Obtenir le prix unitaire d'un médicament (interne ou externe)
export function getMedicationUnitPrice(item: PrescriptionItem): number {
  if (item.isExternal) {
    return item.estimatedPrice || 0
  } else {
    return item.medication?.unitPrice || 0
  }
}

// Formater l'affichage d'un médicament
export function formatMedicationDisplay(item: PrescriptionItem): string {
  const name = getMedicationName(item)
  const strength = getMedicationStrength(item)
  const form = getMedicationForm(item)
  
  let display = name
  if (strength) {
    display += ` ${strength}`
  }
  if (form) {
    display += ` - ${form}`
  }
  
  return display
}

// Générer un résumé de prescription
export function generatePrescriptionSummary(items: PrescriptionItem[]) {
  const { internal, external } = separateMedications(items)
  const totalCost = calculateTotalCost(items)
  const internalCost = calculateInternalCost(items)
  const externalCost = calculateExternalCost(items)
  
  return {
    totalItems: items.length,
    internalItems: internal.length,
    externalItems: external.length,
    totalCost,
    internalCost,
    externalCost,
    hasInternalMedications: internal.length > 0,
    hasExternalMedications: external.length > 0,
    isMixedPrescription: internal.length > 0 && external.length > 0
  }
}

// Valider un item de prescription
export function validatePrescriptionItem(item: PrescriptionItem): string[] {
  const errors: string[] = []
  
  // Validation commune
  if (!item.dosage.trim()) {
    errors.push('Le dosage est requis')
  }
  if (!item.frequency.trim()) {
    errors.push('La fréquence est requise')
  }
  if (!item.duration.trim()) {
    errors.push('La durée est requise')
  }
  if (item.quantity <= 0) {
    errors.push('La quantité doit être supérieure à 0')
  }
  
  // Validation spécifique aux médicaments externes
  if (item.isExternal) {
    if (!item.externalMedicationName?.trim()) {
      errors.push('Le nom du médicament externe est requis')
    }
    if (!item.externalMedicationForm?.trim()) {
      errors.push('La forme du médicament externe est requise')
    }
  } else {
    // Validation spécifique aux médicaments internes
    if (!item.medication?.id) {
      errors.push('Un médicament interne doit être sélectionné')
    }
  }
  
  return errors
}

// Valider une prescription complète
export function validatePrescription(items: PrescriptionItem[]): string[] {
  const errors: string[] = []
  
  if (items.length === 0) {
    errors.push('Au moins un médicament doit être prescrit')
  }
  
  items.forEach((item, index) => {
    const itemErrors = validatePrescriptionItem(item)
    itemErrors.forEach(error => {
      errors.push(`Médicament ${index + 1}: ${error}`)
    })
  })
  
  return errors
}

// Catégories de médicaments avec leurs labels
export const MEDICATION_CATEGORIES = {
  ANTIBIOTIC: 'Antibiotique',
  ANALGESIC: 'Antalgique',
  ANTI_INFLAMMATORY: 'Anti-inflammatoire',
  ANTIHYPERTENSIVE: 'Antihypertenseur',
  ANTIDIABETIC: 'Antidiabétique',
  VITAMIN: 'Vitamine',
  ANTIHISTAMINE: 'Antihistaminique',
  ANTACID: 'Antiacide',
  BRONCHODILATOR: 'Bronchodilatateur',
  DIURETIC: 'Diurétique',
  SEDATIVE: 'Sédatif',
  OTHER: 'Autre'
}

// Formes de médicaments
export const MEDICATION_FORMS = {
  TABLET: 'Comprimé',
  CAPSULE: 'Gélule',
  SYRUP: 'Sirop',
  INJECTION: 'Injection',
  OINTMENT: 'Pommade',
  DROPS: 'Gouttes',
  CREAM: 'Crème',
  POWDER: 'Poudre',
  SUPPOSITORY: 'Suppositoire',
  INHALER: 'Inhalateur'
}

// Voies d'administration
export const ADMINISTRATION_ROUTES = {
  ORAL: 'Orale',
  INTRAVENOUS: 'Intraveineuse',
  INTRAMUSCULAR: 'Intramusculaire',
  SUBCUTANEOUS: 'Sous-cutanée',
  TOPICAL: 'Topique',
  SUBLINGUAL: 'Sublinguale',
  RECTAL: 'Rectale',
  NASAL: 'Nasale',
  OPHTHALMIC: 'Ophtalmique',
  OTIC: 'Otique'
}

// Obtenir le label d'une catégorie
export function getCategoryLabel(category: string): string {
  return MEDICATION_CATEGORIES[category as keyof typeof MEDICATION_CATEGORIES] || 'Autre'
}

// Obtenir le label d'une forme
export function getFormLabel(form: string): string {
  return MEDICATION_FORMS[form as keyof typeof MEDICATION_FORMS] || form
}

// Obtenir le label d'une voie d'administration
export function getRouteLabel(route: string): string {
  return ADMINISTRATION_ROUTES[route as keyof typeof ADMINISTRATION_ROUTES] || route
}
