import { OnboardingData } from '@/lib/actions/onboarding'

// Fonction pour valider les données d'onboarding
export function validateOnboardingData(data: OnboardingData): string[] {
  const errors: string[] = []

  // Validation organisation
  if (!data.organizationName || data.organizationName.length < 2) {
    errors.push('Le nom de l\'organisation doit contenir au moins 2 caractères')
  }

  if (!data.organizationEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.organizationEmail)) {
    errors.push('Email de l\'organisation invalide')
  }

  if (!data.organizationPhone || data.organizationPhone.length < 8) {
    errors.push('Numéro de téléphone de l\'organisation invalide')
  }

  // Validation administrateur
  if (!data.adminFirstName || data.adminFirstName.length < 2) {
    errors.push('Le prénom de l\'administrateur doit contenir au moins 2 caractères')
  }

  if (!data.adminLastName || data.adminLastName.length < 2) {
    errors.push('Le nom de l\'administrateur doit contenir au moins 2 caractères')
  }

  if (!data.adminEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.adminEmail)) {
    errors.push('Email de l\'administrateur invalide')
  }

  if (!data.adminPassword || data.adminPassword.length < 8) {
    errors.push('Le mot de passe doit contenir au moins 8 caractères')
  }

  return errors
}
