import { User<PERSON><PERSON> } from "@prisma/client"
import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: UserRole
      organizationId: string
      organization: {
        id: string
        name: string
        slug: string
        subscriptionPlan: string
        subscriptionStatus: string
      }
    } & DefaultSession["user"]
  }

  interface User extends DefaultUser {
    role: UserRole
    organizationId: string
    organization: {
      id: string
      name: string
      slug: string
      subscriptionPlan: string
      subscriptionStatus: string
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    role: UserRole
    organizationId: string
    organization: {
      id: string
      name: string
      slug: string
      subscriptionPlan: string
      subscriptionStatus: string
    }
  }
}
